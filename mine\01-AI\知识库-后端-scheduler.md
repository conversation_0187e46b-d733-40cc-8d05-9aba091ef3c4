# scheduler 编写例子

```python
from omni.redis.redis_client import rc
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler

from repository.models import User


# 被register_scheduler标记的类会自动加入BackgroundScheduler
# trigger='interval' 以固定时间间隔执行
# seconds 多少秒触发一次
@register_scheduler(trigger='interval', seconds=5 * 60)
class UserScheduler(BaseScheduler):  # 需要继承BaseScheduler
    # run_task方法继承自BaseScheduler,为Scheduler具体要执行的内容
    def run_task(self):
        pass
```
