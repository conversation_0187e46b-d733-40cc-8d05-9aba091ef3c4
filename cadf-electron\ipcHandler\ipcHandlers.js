const {BrowserWindow} = require('electron');
const ipcMainHandler = require('./ipcMainHandler');

let loginWindow = null; // 重命名变量以提高通用性

function registerIpcHandlers() { // 移除 ipcMainHandler 参数

    // 重命名 IPC 处理器并接受 URL 参数
    ipcMainHandler.handle('open-login-window', async (url) => {
        if (!url) {
            return {success: false, message: 'URL parameter is required.'};
        }

        if (loginWindow && !loginWindow.isDestroyed()) {
            loginWindow.focus();
            return {success: true, message: 'Window already open.'};
        }

        // 为每个新的登录窗口创建完全独立的session，使用时间戳确保唯一性
        const session = require('electron').session;
        const uniquePartitionId = `login-window-partition-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
        const loginSession = session.fromPartition(uniquePartitionId);

        loginWindow = new BrowserWindow({
            width: 800,
            height: 600,
            show: true,
            webPreferences: {
                // 使用独立的session
                session: loginSession,
                // 注意：如果小红书页面需要特定权限或 Node.js 集成（通常不建议用于外部网站），
                // 可能需要调整这里的配置。
                // nodeIntegration: false, 
                // contextIsolation: true,
                // sandbox: true // 推荐用于加载外部内容
            }
        });

        // 不再清除所有cookie，而是使用独立的session

        // 使用传递的 URL
        loginWindow.loadURL(url);

        loginWindow.on('closed', () => {
            loginWindow = null;
        });

        return {success: true, message: 'Window opened.'};
    });

    // 重命名 IPC 处理器为 get-cookies-and-close
    // 注意：此处理器获取当前窗口的所有 Cookie，然后关闭窗口
    ipcMainHandler.handle('get-cookies-and-close', async () => { // 使用导入的 ipcMainHandler
        // 确保操作的是当前存在的 loginWindow
        if (!loginWindow || loginWindow.isDestroyed()) {
            console.error('Login window not found or already closed.');
            return {success: false, cookies: [], message: 'Window not found.'};
        }

        try {
            // 使用登录窗口的独立session获取cookie
            const session = loginWindow.webContents.session;
            const cookies = await session.cookies.get({});
            
            // 只返回小红书相关域名的cookie
            const filteredCookies = cookies.filter(cookie => 
                cookie.domain.includes('xiaohongshu.com') || 
                cookie.domain.includes('xhscdn.com')
            );

            loginWindow.close(); // 关闭窗口
            loginWindow = null;

            console.log('Cookies retrieved and window closed.');
            return {success: true, cookies: filteredCookies, message: 'Cookies retrieved and window closed.'};
        } catch (error) {
            console.error('Error getting cookies:', error);
            if (loginWindow && !loginWindow.isDestroyed()) {
                loginWindow.close(); // 发生错误也尝试关闭窗口
                loginWindow = null;
            }
            return {success: false, cookies: [], message: `Error: ${error.message}`};
        }
    });
}

module.exports = {
    registerIpcHandlers
};
