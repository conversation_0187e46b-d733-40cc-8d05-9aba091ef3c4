import time
from datetime import datetime # 导入 datetime
import pytz # 导入 pytz

from config.config import XHS_ACCOUNTS_CRAWL_SET
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from repository.models import Account, AccountMetrics
from scraper.xhs_note_view_scraper import xhs_note_view_scraper


@consume_redis_set(redis_key=XHS_ACCOUNTS_CRAWL_SET, num_threads=1)
def handle_task(account_id):
    olog.info(f"开始处理账号笔记指标爬取任务: {account_id}")
    try:
        # 1. 查找账号信息
        account = Account.objects(id=account_id).first()
        if not account:
            olog.error(f"未找到 ID 为 {account_id} 的账号。")
            return
        # Add Account Status Check
        if account.status != "在线":
             olog.warning(f"账号 {account_id} ({account.name}) 状态为 '{account.status}'，不是 '在线'，跳过爬取。")
             return
        # --- End of Add ---
        if account.platform != "小红书":
             olog.warning(f"账号 {account_id} ({account.name}) 不是小红书平台，跳过爬取。")
             return
        if not account.cookie:
            olog.error(f"账号 {account_id} ({account.name}) 没有配置 Cookie。")
            return

        # 2. 转换并调用爬虫
        olog.info(f"开始为账号 {account_id} ({account.name}) 爬取小红书笔记数据...")
        try:
            notes_data = xhs_note_view_scraper(mongo_cookies=account.cookie)
        except Exception as scrape_err:
            olog.error(f"为账号 {account_id} ({account.name}) 调用爬虫时发生错误: {scrape_err}", exc_info=True)
            return

        if not notes_data:
            olog.warning(f"账号 {account_id} ({account.name}) 未爬取到任何笔记数据。")
            # 即使没爬到数据，也可以选择记录一次空结果
            # XhsNoteMetrics(account_id=str(account.id), notes_data=[]).save()
            # olog.info(f"已为账号 {account_id} 存储空爬取记录。")
            return # 如果不希望存储空记录，则直接返回

        # 3. 存储爬取结果 - 为每个笔记创建一条记录
        saved_count = 0
        crawl_time = int(time.time()) # 统一本次爬取时间
        shanghai_tz = pytz.timezone('Asia/Shanghai') # 获取上海时区

        for note in notes_data:
            try:
                # 转换发布时间为时间戳 (使用上海时区)
                publish_time_str = note.get('publish_time')
                publish_timestamp = None
                if publish_time_str:
                    try:
                        # 1. 解析为 naive datetime 对象
                        naive_dt = datetime.strptime(publish_time_str, '%Y-%m-%d %H:%M:%S')
                        # 2. 本地化为上海时区
                        aware_dt = shanghai_tz.localize(naive_dt)
                        # 3. 获取 Unix 时间戳 (UTC)
                        publish_timestamp = int(aware_dt.timestamp())
                    except ValueError as time_err:
                        olog.warning(f"账号 {account_id} 笔记 '{note.get('title')}' 的发布时间 '{publish_time_str}' 格式无法解析: {time_err}")

                metric_record = AccountMetrics(
                    account_id=str(account.id),
                    platform=account.platform,
                    crawled_at=crawl_time,
                    title=note.get('title'),
                    publish_time=publish_timestamp,
                    view_count=note.get('view_count'),
                    like_count=note.get('like_count'),
                    comment_count=note.get('comment_count'),
                    share_count=note.get('share_count'),
                    favorite_count=note.get('favorite_count')
                )
                metric_record.save()
                saved_count += 1
            except Exception as save_err:
                 olog.error(f"为账号 {account_id} 存储平台 '{account.platform}' 的笔记/帖子 '{note.get('title')}' 数据时出错: {save_err}")

        olog.info(f"成功为账号 {account_id} ({account.name}) 存储了 {saved_count}/{len(notes_data)} 条平台 '{account.platform}' 的指标数据。")

    except Exception as e:
        olog.error(f"处理账号 {account_id} 笔记指标爬取任务时发生错误: {e}", exc_info=True)
