import time
from mongoengine.queryset.visitor import Q
from mongoengine.errors import NotUniqueError

from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.log.log import olog
from repository.models import DataDictionary


@register_handler('data_dictionary')
class DataDictionaryApi:

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    def query_all(self, data):
        """
        查询数据字典条目，可通过 category 筛选。
        """
        category_filter = data.get('category')

        query_filters = Q()
        if category_filter:
            query_filters &= Q(category=category_filter)

        dictionaries = DataDictionary.objects(query_filters)
        total_count = dictionaries.count()

        # 返回完整信息用于管理界面
        results = []
        for d in dictionaries:
            results.append({
                "id_": str(d.id),
                "category": d.category,
                "key": d.key,
                "value": d.value
            })

        return {
            'dictionaries': results,
            'total_count': total_count
        }

    @auth_required(['admin'])
    def create(self, data):
        """
        创建数据字典条目
        """
        category = data.get('category')
        key = data.get('key')
        value = data.get('value')

        olog.info(f"创建数据字典条目: category={category}, key={key}")

        try:
            dictionary = DataDictionary(
                category=category,
                key=key,
                value=value
            )
            dictionary.save()

            olog.info(f"数据字典条目创建成功: {dictionary.id}")
            return {
                "id_": str(dictionary.id),
                "category": dictionary.category,
                "key": dictionary.key,
                "value": dictionary.value
            }
        except NotUniqueError:
            olog.error(f"数据字典条目已存在: category={category}, key={key}")
            raise Exception("该类别下的键已存在")

    @auth_required(['admin'])
    def update(self, data):
        """
        更新数据字典条目
        """
        id_ = data.get('id_')
        category = data.get('category')
        key = data.get('key')
        value = data.get('value')

        olog.info(f"更新数据字典条目: id={id_}")

        dictionary = DataDictionary.objects(id=id_).first()
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise Exception("数据字典条目不存在")

        try:
            dictionary.category = category
            dictionary.key = key
            dictionary.value = value
            dictionary.save()

            olog.info(f"数据字典条目更新成功: {dictionary.id}")
            return {
                "id_": str(dictionary.id),
                "category": dictionary.category,
                "key": dictionary.key,
                "value": dictionary.value
            }
        except NotUniqueError:
            olog.error(f"数据字典条目键冲突: category={category}, key={key}")
            raise Exception("该类别下的键已存在")

    @auth_required(['admin'])
    def delete(self, data):
        """
        删除数据字典条目
        """
        id_ = data.get('id_')

        olog.info(f"删除数据字典条目: id={id_}")

        dictionary = DataDictionary.objects(id=id_).first()
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise Exception("数据字典条目不存在")

        dictionary.delete()
        olog.info(f"数据字典条目删除成功: id={id_}")

        return {"success": True}

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    def get_categories(self, data):
        """
        获取所有数据字典类别
        """
        categories = DataDictionary.objects.distinct('category')
        return {"categories": categories}

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    def get_by_id(self, data):
        """
        根据ID获取数据字典条目
        """
        id_ = data.get('id_')
        
        olog.info(f"查询数据字典条目: id={id_}")
        
        dictionary = DataDictionary.objects(id=id_).first()
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise Exception("数据字典条目不存在")
        
        return {
            "dictionary": {
                "id_": str(dictionary.id),
                "category": dictionary.category,
                "key": dictionary.key,
                "value": dictionary.value
            }
        }
