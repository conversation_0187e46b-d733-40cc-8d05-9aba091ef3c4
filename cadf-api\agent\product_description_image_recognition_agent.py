import base64

import httpx
from langchain_core.messages import HumanMessage

from omni.llm.chat.chat_model_factory import ChatModelFactory
from utils.image_tools import convert_png_base64_encode

PROMPT = """
# 角色
你是一名专业的产品描述员。

# 背景
你收到了多张关于某个产品或服务的图片。

# 任务
根据输入的图片内容，为图片中展示的产品或服务生成一段客观的产品描述。
整合所有图片的信息，生成一段连贯的描述。
描述应准确反映图片内容。

# 返回值
返回一段完整的、客观的产品描述文本。
不要使用markdown格式
"""


def product_description_image_recognition_agent(image_urls: list[str]) -> str:
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")

    image_data = [
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{convert_png_base64_encode(url)}"
            }
        }
        for url in image_urls
    ]

    human_message_content = [
        {"type": "text", "text": PROMPT},
        *image_data,
    ]

    messages = [HumanMessage(content=human_message_content)]

    chain = llm

    result = chain.invoke(messages)
    print(result.content)
    return result.content
