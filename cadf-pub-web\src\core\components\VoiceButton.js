import {AlertType} from "@/core/components/alert";
import {addAlert} from "@/core/components/redux/alert-slice";
import ASRManager from "@/core/tools/asr-manager";
import {Button} from "@mui/material";
import {useCallback, useEffect, useState} from "react";
import {useDispatch} from "react-redux";

// 录音状态枚举
const VOICE_STATUS = {
    IDLE: "idle", // 初始/空闲状态
    RECORDING: "recording", // 正在录音
};

// 按钮基础样式
const BASE_BUTTON_STYLE = {
    height: 44,
    borderRadius: "12px",
    WebkitUserSelect: "none",
    MozUserSelect: "none",
    msUserSelect: "none",
    userSelect: "none",
    WebkitTouchCallout: "none",
    touchAction: "none",
    WebkitContextMenu: "none",
    contextMenu: "none",
};

/**
 * 语音输入按钮组件，集成了语音录制和识别功能
 */
function VoiceButton({onSpeechRecognized}) {
    const dispatch = useDispatch();
    const [voiceStatus, setVoiceStatus] = useState(VOICE_STATUS.IDLE);
    const isRecording = voiceStatus === VOICE_STATUS.RECORDING;

    /**
     * 禁用页面文本选择功能
     * 通过设置全局CSS样式来防止用户在录音过程中进行文本选择操作
     */
    const disableTextSelection = useCallback(() => {
        document.documentElement.style.cssText = `
            user-select: none !important;
            touch-action: none !important;
            -webkit-user-select: none !important;
            -webkit-touch-callout: none !important;
        `;
    }, []);

    /**
     * 恢复页面文本选择功能
     * 清除之前设置的禁用文本选择的CSS样式
     */
    const enableTextSelection = useCallback(() => {
        document.documentElement.style.cssText = "";
    }, []);

    /**
     * 统一错误处理方法
     * @param {Error} error - 错误对象
     * @param {string} message - 错误信息前缀
     * 将错误信息显示在界面上，并重置录音状态
     */
    const handleError = useCallback(
        (error, message) => {
            console.error(`${message}:`, error);
            dispatch(
                addAlert({
                    type: AlertType.ERROR,
                    message: `${message}: ${error.message || "未知错误"}`,
                })
            );
            setVoiceStatus(VOICE_STATUS.IDLE);
        },
        [dispatch]
    );

    /**
     * 开始录音
     * 初始化录音设备并开始录音，更新录音状态
     */
    const startRecording = useCallback(async () => {
        try {
            await ASRManager.startRecording();
            setVoiceStatus(VOICE_STATUS.RECORDING);
        } catch (error) {
            handleError(error, "录音初始化失败");
        }
    }, [handleError]);

    /**
     * 停止录音并进行语音识别
     * 停止录音后调用语音识别服务，将识别结果传递给父组件
     */
    const stopRecordingAndRecognize = useCallback(async () => {
        if (!isRecording || !ASRManager.isCurrentlyRecording()) {
            setVoiceStatus(VOICE_STATUS.IDLE);
            return;
        }

        try {
            // 注意：此处不改变状态，保持"正在录音"状态
            // 调用语音识别服务
            const result = await ASRManager.stopRecordingAndRecognize();

            // 识别完成后再改变状态为空闲
            setVoiceStatus(VOICE_STATUS.IDLE);

            if (result?.text) {
                onSpeechRecognized(result.text);
            }
        } catch (error) {
            handleError(error, "语音识别失败");
            // 确保状态重置
            setVoiceStatus(VOICE_STATUS.IDLE);
            ASRManager.destroy();
        }
    }, [isRecording, onSpeechRecognized, handleError]);

    /**
     * 处理按下开始事件
     * @param {Event} e - 事件对象
     * 禁用文本选择并开始录音
     */
    const handlePressStart = useCallback(
        async (e) => {
            e.preventDefault();
            disableTextSelection();
            await startRecording();
        },
        [startRecording, disableTextSelection]
    );

    /**
     * 处理按下结束事件
     * @param {Event} e - 事件对象
     * 恢复文本选择并停止录音
     */
    const handlePressEnd = useCallback(
        (e) => {
            e.preventDefault();
            enableTextSelection();
            stopRecordingAndRecognize();
        },
        [stopRecordingAndRecognize, enableTextSelection]
    );

    /**
     * 处理触摸开始事件
     * @param {Event} e - 触摸事件对象
     */
    const handleTouchStart = useCallback(
        async (e) => {
            e.preventDefault();
            e.stopPropagation();
            await handlePressStart(e);
        },
        [handlePressStart]
    );

    /**
     * 处理触摸结束事件
     * @param {Event} e - 触摸事件对象
     */
    const handleTouchEnd = useCallback(
        (e) => {
            e.preventDefault();
            e.stopPropagation();
            handlePressEnd(e);
        },
        [handlePressEnd]
    );

    // 初始化和清理
    useEffect(() => {
        ASRManager.initRecorder().catch((error) => {
            console.error("麦克风初始化失败:", error);
            dispatch(
                addAlert({
                    type: AlertType.ERROR,
                    message: `麦克风初始化失败: ${
                        error.message || "请检查麦克风权限是否已开启"
                    }`,
                })
            );
        });

        return () => {
            ASRManager.destroy();
            enableTextSelection();
        };
    }, [dispatch, enableTextSelection]);

    // 根据状态计算按钮样式
    const buttonStyle = {
        ...BASE_BUTTON_STYLE,
        bgcolor: isRecording ? "error.main" : "background.default",
        color: isRecording ? "white" : "text.primary",
        borderColor: isRecording ? "error.main" : "divider",
        "&:hover": {
            bgcolor: isRecording ? "error.dark" : "background.default",
            borderColor: isRecording ? "error.dark" : "primary.main",
        },
        transition: "all 0.3s ease",
    };

    return (
        <Button
            fullWidth
            variant="outlined"
            onMouseDown={handlePressStart}
            onMouseUp={handlePressEnd}
            onMouseLeave={handlePressEnd}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchCancel={handleTouchEnd}
            sx={buttonStyle}
        >
            {isRecording ? "正在录音..." : "按住说话"}
        </Button>
    );
}

// 导出VoiceButton组件
export default VoiceButton;
