import base64
import io

from PIL import Image
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

from agent.utils.openai_image_edit import OpenaiImageEditor
from omni.llm.agent.structured_output_agent import structured_output_agent
from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog

"""
替换图片中的产品智能体
"""

# Pydantic 模型定义
class ImageCategoryResult(BaseModel):
    product_type: str = Field(
        None, description="具体的美妆产品类型，如洗发水、护发素等"
    )
    description: str = Field(..., description="图片主要内容描述")


def identify_products_in_images(image: bytes) -> dict:
    """
    识别图片中的产品类别。
    :param image: 图片的二进制内容
    :return: 包含图片类别和描述的字典，格式为 {"category": ..., "product_type": ..., "description": ...}
    """
    olog.info("开始执行 identify_products_in_images...")
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    img = Image.open(io.BytesIO(image))
    original_format = img.format
    olog.info(f"处理图片，原始格式为: {original_format}")

    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=original_format)
    base64_str = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")
    mime_type = original_format.lower() if original_format else "png"
    llm_image_input = {
        "type": "image_url",
        "image_url": {"url": f"data:image/{mime_type};base64,{base64_str}"},
    }

    prompt = """
# 角色
你是一位专注于电商图片分析的AI视觉专家，擅长识别美妆产品及其细分类别。

# 任务
请分析图片内容，完成以下任务：
1. 识别图片中的美妆产品细分类别（如洗发水、护发素、沐浴露等）。
2. 用简洁的中文描述图片的主要内容。

# 返回值要求
- product_type: 具体的美妆产品类型（如洗发水、护发素、沐浴露等）。
- description: 用一句话描述图片的主要内容，确保语言简洁明了。
"""

    messages = [
        HumanMessage(content=[{"type": "text", "text": prompt}, llm_image_input])
    ]
    raw_result = llm.invoke(messages).content
    agent_structured_output = structured_output_agent(
        model_class=ImageCategoryResult, raw_data=raw_result
    )
    result = agent_structured_output.model_dump()
    olog.info(f"识别结果: {result}")
    return result


def edit_product_image(
        scene_image: bytes,
        product_image: bytes,
        scene_placeholder: str,
        product_placeholder: str,
) -> bytes:
    """
    将场景图中的主产品替换为产品图，并移除非产品固有的营销文字、标签等。

    :param scene_image: 场景图片的二进制内容
    :param product_image: 产品图片的二进制内容
    :param scene_placeholder: 场景图中的占位变量值
    :param product_placeholder: 产品图中的占位变量值
    :return: 编辑后的图片二进制内容
    :raises: 无显式抛出异常，但内部错误会记录到日志中
    """
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    prompt = f"""
# 任务说明
1. 将第一张图中的所有非产品固有的营销文字、标语、标签等移除，确保最终场景干净，仅保留自然元素。
2. 将第一张图中的 `{scene_placeholder}` 替换为第二张图中的 `{product_placeholder}`。
3. 对产品固有的品牌标志、型号、成分说明等文字图案进行逼真的失焦模糊处理（类似轻微失焦或动态模糊），使其轮廓可见但细节不清。禁止使用涂抹、打码或粗暴覆盖的方式。

# 注意事项
- 确保替换后的产品与场景自然融合，避免生硬的边缘或违和感。
- 模糊处理需保持真实感，避免过度模糊导致信息完全丢失。
- 最终生成的图片应保持高分辨率，无明显人工处理痕迹。
"""
    # edited_images 现在是一个元组 (bytes, error_type)
    edited_image_data, error_type = editor.generate_image_from_image(
        image_bytes_list=[scene_image, product_image], prompt=prompt
    )
    if error_type != "success" or not edited_image_data:
        olog.error(f"编辑产品图片失败，错误类型: {error_type}")
        return b""

    olog.info(f"成功编辑图片 (replace_product_in_scene)，返回 1 张图片。")
    return edited_image_data


def replace_product_in_image(
        scene_image: bytes,
        product_image: bytes,
) -> bytes:
    """
    先识别场景图和产品图的类别，再将识别结果传递给 replace_product_in_image 进行产品替换。

    :param scene_image: 场景图片的二进制内容
    :param product_image: 产品图片的二进制内容
    :return: 编辑后的图片二进制内容
    :raises: 无显式抛出异常，但内部错误会记录到日志中
    """
    olog.info("开始执行 process_and_replace_product...")
    # 1. 识别场景图和产品图的类别
    scene_info = identify_products_in_images(scene_image)
    product_info = identify_products_in_images(product_image)
    if not scene_info or not product_info:
        olog.warning("未能识别场景图或产品图的类别，跳过替换步骤。")
        return b""

    olog.info(f"识别到场景图类别: {scene_info.get('product_type', '未知')}")
    olog.info(f"识别到产品图类别: {product_info.get('product_type', '未知')}")
    # 2. 将识别结果传递给 replace_product_in_image
    edited_image = edit_product_image(
        scene_image,
        product_image,
        scene_info.get("product_type", ""),
        product_info.get("product_type", ""),
    )
    olog.info("成功完成图片替换。")
    return edited_image
