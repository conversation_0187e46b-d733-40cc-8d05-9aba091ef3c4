"use client";

import { useEffect, useState } from 'react';
import { <PERSON>ert, Box, Button, Chip, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Divider, Grid, IconButton, MobileStepper, Paper, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ArrowLeft, ChevronLeft, ChevronRight, XCircle } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import TokenIcon from '@mui/icons-material/Token';
import { aiGeneratedMaterialApi } from '@/api/ai-generated-material-api';
import { format } from 'date-fns';

export default function ContentDetailPage() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams();
    const [content, setContent] = useState(null);
    const [activeStep, setActiveStep] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

    useEffect(() => {
        const contentId = params?.contentId;
        if (!contentId) {
            setError("缺少内容 ID");
            setLoading(false);
            return;
        }
        
        setLoading(true);
        setError(null);
        
        const fetchData = async () => {
            try {
                const response = await aiGeneratedMaterialApi.queryById(contentId);
                
                if (!response || !response.id_) {
                    throw new Error("未找到内容");
                }
                
                const imageUrls = (response.images || [])
                    .map(img => {
                        if (img.signed_url) {
                            return img.signed_url;
                        } else {
                            console.warn("Image missing signed_url or key:", img);
                            return 'https://placehold.co/300x200/cccccc/333333?text=URL+Error';
                        }
                    });

                setContent({
                    ...response,
                    id: response.id_,
                    date: response.create_at ? format(new Date(response.create_at * 1000), 'yyyy-MM-dd') : 'N/A',
                    images: imageUrls,
                    title: response.title || '无标题',
                    tokens: response.token_consumption || 0,
                });
            } catch (err) {
                console.error("Failed to fetch content details:", err);
                setError(err.message || '获取内容详情失败');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [params?.contentId]);

    const handleGoBack = () => {
        router.push(`/protected/ai-image-text-generation/${params.id}`);
    };

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleDelete = () => {
        setOpenConfirmDialog(true);
    };

    const handleCloseConfirmDialog = () => {
        setOpenConfirmDialog(false);
    };

    const handleConfirmDelete = async () => {
        handleCloseConfirmDialog();

        try {
            await aiGeneratedMaterialApi.delete(content.id);
            router.push(`/protected/ai-image-text-generation/${params.id}`);
        } catch (err) {
            console.error("Failed to delete material:", err);
            alert(`删除失败: ${err.message || '未知错误'}`);
        }
    };

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Button
                        startIcon={<ArrowLeft size={18} />}
                        onClick={handleGoBack}
                        sx={{ mr: 2 }}
                    >
                        返回列表
                    </Button>
                    <Typography variant="h5" component="h1" sx={{ fontWeight: 600, flexGrow: 1 }}>
                        内容详情
                    </Typography>
                </Box>

                {loading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
                        <CircularProgress />
                    </Box>
                )}

                {error && (
                    <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
                )}

                {!loading && !error && content && (
                    <Paper
                        elevation={0}
                        sx={{
                            mb: 4,
                            p: 0,
                            overflow: 'hidden',
                            borderRadius: 2,
                            border: `1px solid ${theme.palette.divider}`
                        }}
                    >
                        <Box sx={{
                            p: 2,
                            bgcolor: theme.palette.primary.light,
                            color: theme.palette.primary.contrastText,
                            borderBottom: `1px solid ${theme.palette.divider}`,
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                                {content.title}
                            </Typography>
                        </Box>

                        <Box sx={{
                            display: 'flex',
                            flexDirection: { xs: 'column', md: 'row' },
                            minHeight: { xs: 'auto', md: '70vh' },
                        }}>
                            <Paper
                                elevation={0}
                                sx={{
                                    borderRight: { md: `1px solid ${theme.palette.divider}` },
                                    overflow: 'visible',
                                    position: 'relative',
                                    width: { xs: '100%', md: '45%' },
                                    height: { xs: '350px', md: 'auto' },
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    bgcolor: theme.palette.grey[50],
                                    p: { xs: 1, md: 2 },
                                }}
                            >
                                {content.images && content.images.length > 0 ? (
                                    <Box sx={{
                                        position: 'relative',
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        paddingBottom: content.images.length > 1 ? '56px' : 0,
                                        padding: 3,
                                    }}>
                                        <Box
                                            component="img"
                                            src={content.images[activeStep]}
                                            alt={`${content.title || 'Image'} - ${activeStep + 1}`}
                                            sx={{
                                                maxWidth: '100%',
                                                maxHeight: '100%',
                                                objectFit: 'contain',
                                                display: 'block',
                                                borderRadius: 1,
                                                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                                            }}
                                            onError={(e) => {
                                                e.target.src = 'https://placehold.co/600x400/ffcccc/333333?text=Error+Loading';
                                            }}
                                        />

                                        {content.images.length > 1 && (
                                            <>
                                                <IconButton
                                                    onClick={handleBack}
                                                    disabled={activeStep === 0}
                                                    sx={{
                                                        position: 'absolute',
                                                        left: 8,
                                                        top: '50%',
                                                        transform: 'translateY(-50%)',
                                                        color: theme.palette.common.white,
                                                        bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                        '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' },
                                                        '&.Mui-disabled': { opacity: 0.5 }
                                                    }}
                                                    aria-label="上一张图片"
                                                >
                                                    <ChevronLeft size={24} />
                                                </IconButton>
                                                <IconButton
                                                    onClick={handleNext}
                                                    disabled={activeStep === content.images.length - 1}
                                                    sx={{
                                                        position: 'absolute',
                                                        right: 8,
                                                        top: '50%',
                                                        transform: 'translateY(-50%)',
                                                        color: theme.palette.common.white,
                                                        bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                        '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' },
                                                        '&.Mui-disabled': { opacity: 0.5 }
                                                    }}
                                                    aria-label="下一张图片"
                                                >
                                                    <ChevronRight size={24} />
                                                </IconButton>
                                                <Box sx={{
                                                    position: 'absolute',
                                                    bottom: 8,
                                                    left: '50%',
                                                    transform: 'translateX(-50%)',
                                                    bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                    color: 'white',
                                                    px: 1,
                                                    py: 0.5,
                                                    borderRadius: 1,
                                                    fontSize: '0.8rem',
                                                }}>
                                                    {`${activeStep + 1} / ${content.images.length}`}
                                                </Box>
                                            </>
                                        )}
                                    </Box>
                                ) : (
                                    <Typography color="text.secondary">无图片</Typography>
                                )}
                            </Paper>

                            <Box
                                sx={{
                                    width: { xs: '100%', md: '55%' },
                                    p: 3,
                                    overflowY: 'auto',
                                    height: { xs: 'auto', md: '100%' },
                                    bgcolor: 'background.paper',
                                }}
                            >
                                <Box sx={{ mb: 2, pb: 2, borderBottom: `1px dashed ${theme.palette.divider}` }}>
                                    <Typography variant="subtitle1" color="text.secondary" gutterBottom sx={{ fontWeight: 'bold' }}>
                                        标题
                                    </Typography>
                                    <Typography variant="h6" component="div" sx={{ mb: 2 }}>
                                        {content.title || '无标题'}
                                    </Typography>
                                </Box>

                                <Box sx={{ mt: 3, mb: 3 }}>
                                    <Typography variant="subtitle1" color="text.secondary" gutterBottom sx={{ fontWeight: 'bold' }}>
                                        内容
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            whiteSpace: 'pre-wrap',
                                            color: 'text.primary',
                                            lineHeight: 1.6,
                                        }}
                                    >
                                        {content.content || '无内容'}
                                    </Typography>
                                </Box>

                                <Divider sx={{ my: 2 }} />

                                <Grid container spacing={2}>
                                    <Grid item xs={12} sm={6} key="detail-date">
                                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                            生成日期
                                        </Typography>
                                        <Typography variant="body2">
                                            {content.date}
                                        </Typography>
                                    </Grid>

                                    <Grid item xs={12} sm={6} key="detail-tokens">
                                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                            Token消耗
                                        </Typography>
                                        <Chip
                                            icon={<TokenIcon sx={{ fontSize: '0.875rem', mr: 0.5 }} />}
                                            label={`${content.tokens}`}
                                            size="small"
                                            color="primary"
                                            variant="outlined"
                                        />
                                    </Grid>
                                </Grid>
                            </Box>
                        </Box>

                        <Divider />
                        <Box sx={{ p: 2 }}>
                            <Button
                                variant="outlined"
                                color="error"
                                startIcon={<XCircle size={18} />}
                                onClick={handleDelete}
                            >
                                删除此内容
                            </Button>
                        </Box>
                    </Paper>
                )}
            </Box>

            <Dialog
                open={openConfirmDialog}
                onClose={handleCloseConfirmDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{"确认删除"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        确定要删除这个生成内容吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseConfirmDialog} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleConfirmDelete} color="error" autoFocus>
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
} 