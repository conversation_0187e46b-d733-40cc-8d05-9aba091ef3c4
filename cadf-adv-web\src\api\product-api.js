import api from "@/core/api/api";

export const productApi = {
    create: async (title, description, domain, images) => {
        return await api({
            resource: "product",
            method_name: "create",
            title,
            description,
            domain,
            images,
        });
    },

    modify: async (id_, title, description, domain, images) => {
        return await api({
            resource: "product",
            method_name: "modify",
            id_,
            title,
            description,
            domain,
            images,
        });
    },

    delete: async (id_) => {
        return await api({
            resource: "product",
            method_name: "delete",
            id_,
        });
    },

    query_one: async (id_) => {
        return await api({
            resource: "product",
            method_name: "query_one",
            id_,
        });
    },

    query_all: async (search, page = 0, limit = 5) => {
        return await api({
            resource: "product",
            method_name: "query_all",
            search,
            page,
            limit,
        });
    },

    // query_product_overview: async (search, page = 0, limit = 10) => {
    //     return await api({
    //         resource: "product",
    //         method_name: "query_product_overview",
    //         search,
    //         page,
    //         limit,
    //     });
    // },
};