import time
from collections import defaultdict
from datetime import datetime, timezone

from bson import ObjectId

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from repository.models import (
    DailyTaskRevenue,
    PromotionTaskDetail,
    AiGeneratedMaterial,
    PromotionTask,
)


def get_midnight_timestamp(dt_object):
    """获取给定datetime对象当天零点的时间戳"""
    return int(
        time.mktime(
            dt_object.replace(hour=0, minute=0, second=0, microsecond=0).timetuple()
        )
    )


def format_timestamp(ts):
    """将时间戳格式化为 YYYY-MM-DD，如果时间戳无效则返回 None"""
    if ts and isinstance(ts, (int, float)):
        try:
            return datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d")
        except (TypeError, ValueError):
            return None
    return None


@register_handler("daily_task_revenue")
class DailyTaskRevenueApi:

    @auth_required(["admin", "creator"])
    def query_daily_summary(self, data):
        """
        **目的**: 查询指定用户的每日收益汇总信息，支持分页。此方法先分页查询日期，再根据日期获取数据聚合。

        **算法描述**:
            - **输入处理**: 获取 `user_id`、`page` (默认 1) 和 `page_size` (默认 10)。如果 `user_id` 缺失，则抛出异常。
            - **日期分页查询**:
                - 查询 `DailyTaskRevenue` 集合，获取指定 `user_id` 的所有唯一日期 (`date` 字段，时间戳格式) 列表。
                - 对日期列表进行降序排序。
                - 计算总的唯一日期数量 (`total_items`)。
                - 根据 `page` 和 `page_size` 计算分页的起始和结束索引，提取当前页的日期列表 `paginated_dates`。如果当前页没有日期，直接返回空。
            - **数据查询与聚合**:
                - 根据 `user_id` 和 `paginated_dates` 查询 `DailyTaskRevenue` 集合，获取当前页日期对应的所有收益记录（包括 status 和 settled_at）。
                - 初始化一个默认字典 `grouped_data` 用于存储按日期聚合的结果（总浏览量、总收益、状态列表、结算时间列表）。
                - 遍历查询到的收益记录：
                    - 累加记录的 `daily_views` 到对应日期的 `total_views`。
                    - 累加记录的 `daily_revenue` 到对应日期的 `total_revenue`。
                    - 将记录的 `status` 添加到对应日期的 `statuses` 列表。
                    - 如果 `status` 为 "已结算" 且 `settled_at` 有效，将其添加到对应日期的 `settlement_times` 列表。
            - **结果格式化**:
                - 初始化一个空列表 `results`。
                - 遍历 `paginated_dates` 中的每个日期时间戳 `date_ts`：
                    - 获取该日期的聚合数据 `daily_summary`。
                    - 将时间戳格式化为 'YYYY-MM-DD' 字符串。
                    - **状态判定**: 如果 `daily_summary['statuses']` 中包含 "已结算"，则状态为 "已结算"；否则为 "未结算"。
                    - **结算日期判定**: 如果状态为 "已结算"，找出 `daily_summary['settlement_times']` 中的最大值（最新结算时间），并格式化为 'YYYY-MM-DD'；否则为 `None`。
                    - 将格式化后的每日汇总信息添加到 `results` 列表。
            - **返回**: 返回包含 `items` (当前页的汇总数据列表)、`total_items` (总天数)、`page` 和 `page_size` 的字典。
        """
        user_id = data.get("user_id")
        page = data.get("page", 1)
        page_size = data.get("page_size", 10)

        if not user_id:
            raise MException("缺少 user_id")

        distinct_dates = DailyTaskRevenue.objects(user_id=user_id).distinct("date")

        if not distinct_dates:
            return {"items": [], "total_items": 0, "page": page, "page_size": page_size}

        sorted_dates = sorted(distinct_dates, reverse=True)
        total_items = len(sorted_dates)

        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_dates = sorted_dates[start_index:end_index]

        if not paginated_dates:

            return {
                "items": [],
                "total_items": total_items,
                "page": page,
                "page_size": page_size,
            }

        revenues_for_page = DailyTaskRevenue.objects(
            user_id=user_id, date__in=paginated_dates
        ).only("date", "daily_views", "daily_revenue", "status", "settled_at")

        grouped_data = defaultdict(
            lambda: {
                "total_views": 0,
                "total_revenue": 0,
                "statuses": [],
                "settlement_times": [],
            }
        )

        for revenue in revenues_for_page:
            date_ts = revenue.date
            grouped_data[date_ts]["total_views"] += revenue.daily_views
            grouped_data[date_ts]["total_revenue"] += revenue.daily_revenue
            grouped_data[date_ts]["statuses"].append(revenue.status)
            if revenue.status == "已结算" and revenue.settled_at:
                grouped_data[date_ts]["settlement_times"].append(revenue.settled_at)

        results = []
        for date_ts in paginated_dates:
            daily_summary = grouped_data[date_ts]
            date_str = datetime.fromtimestamp(date_ts).strftime("%Y-%m-%d")

            status = "未结算"
            settlement_date = None

            if "已结算" in daily_summary["statuses"]:
                status = "已结算"

                if daily_summary["settlement_times"]:
                    latest_settled_ts = max(daily_summary["settlement_times"])
                    settlement_date = format_timestamp(latest_settled_ts)

            results.append(
                {
                    "date": date_str,
                    "totalViews": daily_summary["total_views"],
                    "totalRevenue": daily_summary["total_revenue"],
                    "status": status,
                    "settlementDate": settlement_date,
                }
            )

        return {
            "items": results,
            "total_items": total_items,
            "page": page,
            "page_size": page_size,
        }

    @auth_required(["admin", "creator"])
    def query_details_by_date(self, data):
        """
        **目的**: 查询指定用户在特定日期的收益明细列表，并包含关联的任务、素材等信息。

        **算法描述**:
            - **输入处理**: 获取 `user_id` 和 `date` (格式 'YYYY-MM-DD')。如果任一参数缺失，抛出异常。
            - **日期转换**: 将输入的日期字符串转换为当天零点的时间戳 `target_timestamp`。如果格式错误，抛出异常。
            - **收益记录查询**: 根据 `user_id` 和 `target_timestamp` 查询 `DailyTaskRevenue` 集合，获取当天的所有收益记录（包含 `promotion_task_detail_id`, `daily_views`, `daily_revenue`, `status`, `settled_at`）。如果查询结果为空，直接返回空的明细列表和总计。
            - **关联 ID 提取**: 从查询到的收益记录中提取所有有效的 `promotion_task_detail_id` 列表 (`detail_ids`)。
            - **关联数据查询 (批量)**:
                - 使用 `detail_ids` 查询 `PromotionTaskDetail` 集合，获取 `id`, `ai_generated_material_id`, `promotion_task_id`, `publish_url` 字段，并将结果存储在字典 `task_details_dict` (以 detail ID 字符串为键) 中。
                - 从 `task_details_dict` 的值中提取所有有效的 `ai_generated_material_id`，查询 `AiGeneratedMaterial` 集合，获取 `id`, `title` 字段，并将结果存储在字典 `materials_dict` (以 material ID 字符串为键) 中。
                - 从 `task_details_dict` 的值中提取所有有效的 `promotion_task_id`，查询 `PromotionTask` 集合，获取 `id`, `task_name` 字段，并将结果存储在字典 `promotion_tasks_dict` (以 task ID 字符串为键) 中。
            - **结果组合与格式化**:
                - 初始化空列表 `result_items` 用于存储最终的明细条目，初始化当日总浏览量 `total_views_day` 和总收益 `total_revenue_day` 为 0。
                - 遍历当天的每条收益记录 `dr`:
                    - 根据 `dr.promotion_task_detail_id` (字符串) 从 `task_details_dict` 中查找关联的 `task_detail`。
                    - 如果找不到 `task_detail`，设置 `material` 和 `promotion_task` 为 `None`，`publish_url` 为空字符串。
                    - 如果找到 `task_detail`，则根据其 `ai_generated_material_id` 和 `promotion_task_id` 分别从 `materials_dict` 和 `promotion_tasks_dict` 中查找关联的 `material` 和 `promotion_task` (找不到则为 `None`)，并获取 `publish_url` (不存在则为空字符串)。
                    - **状态**: 直接使用 `dr.status` 作为状态。
                    - **日期**: 设置结算日期 `settlement_date_str`：如果 `dr.status` 为 "已结算" 且 `dr.settled_at` 有效，则将其格式化为 'YYYY-MM-DD'；否则为 `None`。出账日期 `payment_date_str` 始终为 `None`。
                    - 构建包含明细信息的字典，包括：`id` (使用 `promotion_task_detail_id`), `title` (如果 `material` 存在则用其标题，否则为 "未知标题"), `views` (`dr.daily_views`), `revenue` (`dr.daily_revenue`), `date` (输入的日期字符串), `status` (来自 `dr`), `url` (`publish_url`), `task` (如果 `promotion_task` 存在则用其名称，否则为 "未知任务"), `settlementDate` (`settlement_date_str`), `paymentDate` (`payment_date_str`)。
                    - 将构建的字典添加到 `result_items` 列表。
                    - 累加 `dr.daily_views` 到 `total_views_day`。
                    - 累加 `dr.daily_revenue` 到 `total_revenue_day`。
            - **返回**: 返回包含 `items` (当日明细列表), `totalViews` (当日总浏览量), `totalRevenue` (当日总收益) 的字典。
        """
        user_id = data.get("user_id")
        date_str = data.get("date")

        if not user_id or not date_str:
            raise MException("缺少 user_id 或 date")

        try:

            dt_object = datetime.strptime(date_str, "%Y-%m-%d")
            target_timestamp = get_midnight_timestamp(dt_object)
        except ValueError:
            raise MException("日期格式错误，请使用 YYYY-MM-DD")

        daily_revenues = DailyTaskRevenue.objects(
            user_id=user_id, date=target_timestamp
        ).only(
            "promotion_task_detail_id",
            "daily_views",
            "daily_revenue",
            "status",
            "settled_at",
        )
        if not daily_revenues:
            return {"items": [], "totalViews": 0, "totalRevenue": 0}

        detail_ids = [
            ObjectId(dr.promotion_task_detail_id)
            for dr in daily_revenues
            if ObjectId.is_valid(dr.promotion_task_detail_id)
        ]

        task_details_dict = {
            str(td.id): td
            for td in PromotionTaskDetail.objects(id__in=detail_ids).only(
                "id", "ai_generated_material_id", "promotion_task_id", "publish_url"
            )
        }

        material_ids = [
            ObjectId(td.ai_generated_material_id)
            for td in task_details_dict.values()
            if td.ai_generated_material_id
            and ObjectId.is_valid(td.ai_generated_material_id)
        ]
        materials_dict = {
            str(m.id): m
            for m in AiGeneratedMaterial.objects(id__in=material_ids).only(
                "id", "title"
            )
        }

        promotion_task_ids = [
            ObjectId(td.promotion_task_id)
            for td in task_details_dict.values()
            if td.promotion_task_id and ObjectId.is_valid(td.promotion_task_id)
        ]
        promotion_tasks_dict = {
            str(pt.id): pt
            for pt in PromotionTask.objects(id__in=promotion_task_ids).only(
                "id", "task_name"
            )
        }

        result_items = []
        total_views_day = 0
        total_revenue_day = 0

        for dr in daily_revenues:
            detail_id_str = dr.promotion_task_detail_id
            task_detail = task_details_dict.get(detail_id_str)
            if not task_detail:

                material = None
                promotion_task = None
                publish_url = ""
            else:
                material = (
                    materials_dict.get(task_detail.ai_generated_material_id)
                    if task_detail.ai_generated_material_id
                    else None
                )
                promotion_task = (
                    promotion_tasks_dict.get(task_detail.promotion_task_id)
                    if task_detail.promotion_task_id
                    else None
                )
                publish_url = task_detail.publish_url or ""

            status = dr.status

            settlement_date_str = None
            if status == "已结算" and dr.settled_at:
                settlement_date_str = format_timestamp(dr.settled_at)

            payment_date_str = None

            result_items.append(
                {
                    "id": detail_id_str,
                    "title": material.title if material else "未知标题",
                    "views": dr.daily_views,
                    "revenue": dr.daily_revenue,
                    "date": date_str,
                    "status": status,
                    "url": publish_url,
                    "task": promotion_task.task_name if promotion_task else "未知任务",
                    "settlementDate": settlement_date_str,
                    "paymentDate": payment_date_str,
                }
            )
            total_views_day += dr.daily_views
            total_revenue_day += dr.daily_revenue

        return {
            "items": result_items,
            "totalViews": total_views_day,
            "totalRevenue": total_revenue_day,
        }
