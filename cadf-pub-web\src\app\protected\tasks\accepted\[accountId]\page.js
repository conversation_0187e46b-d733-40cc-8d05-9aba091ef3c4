"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation'; // No need for useSearchParams here if accountId is from path
import {
    Alert, Box, Button, CardActions, CardContent, Chip, CircularProgress, Container, Grid,
    LinearProgress, Pagination, Paper, Stack, Typography, useMediaQuery, useTheme, IconButton
} from '@mui/material';
import { Clock, AlertTriangle, ChevronLeft, Home, ListChecks, Eye, Info as InfoIcon, CheckCircle, XCircle, Link as LinkIconLucide } from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useDispatch } from 'react-redux';
import { addAlert } from '@/core/components/redux/alert-slice';
import { AlertType } from '@/core/components/alert';
import NextLink from 'next/link';
import ProductCard from '@/components/ProductCard';
import React from 'react';

// Card for displaying an accepted task
function AcceptedTaskListItemCard({ task, onClick, isMobile }) {
    const theme = useTheme();

    const getStatusChipProps = () => {
        let label = '待提交链接';
        let color = 'info';
        let variant = 'outlined';
        let icon = <LinkIconLucide size={16} />;

        if (task.publish_url && task.publish_at) {
            switch (task.validation_status) {
                case '成功':
                    label = '已发布 (验证成功)';
                    color = 'success';
                    variant = 'filled';
                    icon = <CheckCircle size={16} />;
                    break;
                case '失败':
                    label = `验证失败`;
                    color = 'error';
                    icon = <XCircle size={16} />;
                    break;
                case '待验证':
                default:
                    label = '发布链接待验证';
                    color = 'warning';
                    icon = <AlertTriangle size={16} />;
                    break;
            }
        }
        return { label, color, variant, icon };
    };

    const statusProps = getStatusChipProps();

    return (
        <Grid item xs={12} sm={6} md={12/5} key={task.id_}>
            <ProductCard
                images={task.imageUrls}
                onClick={onClick}
            >
                <Box sx={{
                    p: 2,
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    borderRadius: '4px'
                }}>
                    <CardContent sx={{ pb: 1, flexGrow: 1, display: 'flex', flexDirection: 'column', p:0 }}>
                        <Typography variant={isMobile ? "subtitle1" : "h6"} gutterBottom sx={{ 
                            fontWeight: 'medium', 
                            mb: 1, 
                            flexShrink: 0,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical'
                        }} title={task.title}>
                            {task.title || '未命名任务'}
                        </Typography>
                        <Stack spacing={1} sx={{ flexGrow: 1, mt: 'auto' }}>
                            <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center',
                                borderRadius: '16px',
                                border: `1px solid ${theme.palette[statusProps.color]?.main || theme.palette.grey[400]}`,
                                overflow: 'hidden',
                                width: 'fit-content',
                                '& .icon-container': {
                                    bgcolor: theme.palette[statusProps.color]?.main || theme.palette.grey[400],
                                    color: theme.palette[statusProps.color]?.contrastText || theme.palette.common.white,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    padding: '6px',
                                }
                            }}>
                                <Box className="icon-container">
                                    {React.cloneElement(statusProps.icon, { size: 18 })}
                                </Box>
                                <Box sx={{ 
                                    px: 1.5, 
                                    py: 0.5,
                                    color: theme.palette[statusProps.color]?.main || theme.palette.text.primary,
                                    fontWeight: 'medium',
                                    fontSize: '0.8125rem',
                                }}>
                                    {statusProps.label}
                                </Box>
                            </Box>
                            {task.validation_status === '失败' && task.validation_details && (
                                 <Typography variant="caption" color="error" sx={{fontSize: '0.75rem', mt: 0.5, display: 'block'}}>原因: {task.validation_details}</Typography>
                            )}
                            <Stack direction="row" spacing={1} alignItems="center" color="text.secondary" sx={{ mt: 0.5 }}>
                                <Clock size={16} />
                                <Typography variant="body2">截止: {task.deadline === "N/A" ? '无' : task.deadline}</Typography>
                            </Stack>
                            <Typography variant="caption" color="text.secondary">接取于: {new Date(task.accepted_at * 1000).toLocaleString()}</Typography>
                        </Stack>
                    </CardContent>
                    <CardActions sx={{ p: 0, pt: 1, mt: 'auto' }}>
                        <Button variant="contained" fullWidth onClick={onClick} sx={{ borderRadius: 1.5, textTransform: 'none' }} startIcon={<Eye size={16}/>}>
                            查看任务详情
                        </Button>
                    </CardActions>
                </Box>
            </ProductCard>
        </Grid>
    );
}

function AcceptedTasksContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const params = useParams();
    const dispatch = useDispatch();

    const accountId = params.accountId;
    const [accountInfo, setAccountInfo] = useState(null);
    const [loadingAccountInfo, setLoadingAccountInfo] = useState(true);

    const [tasksState, setTasksState] = useState({ tasks: [], totalCount: 0, loading: true, error: null });
    const [currentPage, setCurrentPage] = useState(0);
    const rowsPerPage = isMobile ? 12 : 5; // PC: 5, Mobile: 12

    useEffect(() => {
        if (accountId) {
            const fetchAccInfo = async () => {
                setLoadingAccountInfo(true);
                try {
                    const res = await pubPromotionTaskApi.getAccountInfo(accountId);
                    if (res && res.account_info) {
                        setAccountInfo(res.account_info);
                    } else {
                        dispatch(addAlert({ type: AlertType.ERROR, message: "未能获取账户信息" }));
                    }
                } catch (err) {
                    dispatch(addAlert({ type: AlertType.ERROR, message: `加载账户信息失败: ${err.message}` }));
                }
                setLoadingAccountInfo(false);
            };
            fetchAccInfo();
        } else {
            setLoadingAccountInfo(false); // No accountId, no loading
        }
    }, [accountId, dispatch]);

    useEffect(() => {
        if (!accountId) {
            setTasksState({ tasks: [], totalCount: 0, loading: false, error: "未提供账户ID，无法查询已接任务。" });
            dispatch(addAlert({ type: AlertType.ERROR, message: "缺少账户ID"}));
            return;
        }
        // Only fetch tasks if accountId is present
        const fetchAccepted = async () => {
            setTasksState(prev => ({ ...prev, loading: true, error: null }));
            try {
                const response = await pubPromotionTaskApi.queryAcceptedTasks(currentPage, rowsPerPage, accountId);
                if (response && response.accepted_tasks) {
                    const processedTasks = response.accepted_tasks.map(task => ({
                        ...task,
                        imageUrls: (Array.isArray(task.imageUrls) && task.imageUrls.length > 0 && !task.imageUrls.some(url=>!url || url.includes('placehold.co'))) 
                                ? task.imageUrls 
                                : ['https://placehold.co/600x400?text=No+Image'],
                    }));
                    setTasksState({ tasks: processedTasks, totalCount: response.total_count || 0, loading: false, error: null });
                } else {
                    setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的已接任务数据' });
                }
            } catch (error) {
                console.error(`获取账户 ${accountId} 的已接任务列表失败:`, error);
                const errorMsg = `获取已接任务列表失败: ${error.message || '未知错误'}`;
                setTasksState({ tasks: [], totalCount: 0, loading: false, error: errorMsg });
                dispatch(addAlert({ type: AlertType.ERROR, message: errorMsg }));
            }
        };
        fetchAccepted();
    }, [accountId, currentPage, dispatch, rowsPerPage]);

    const handlePageChange = (event, newPage) => setCurrentPage(newPage - 1);

    const handleTaskClick = (task) => {
        router.push(`/protected/tasks/detail/${task.id_}`);
    };

    const totalPages = Math.ceil(tasksState.totalCount / rowsPerPage);
    const accountDisplayName = accountInfo?.name || (accountId ? `账户 ${accountId.slice(-6)}` : '选择的账户');

    return (
        <Container maxWidth="xl" sx={{ py: { xs: 2, sm: 3, md: 4 }, px: { xs: 2, sm: 3, md: 4 } }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                 <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Typography variant="caption" color="text.secondary" onClick={() => router.back()} sx={{cursor: 'pointer', '&:hover': {textDecoration: 'underline'}}}>
                    返回任务中心
                </Typography>
            </Stack>
            
            <Typography variant={isMobile ? "h5" : "h4"} component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
                已接任务 for {loadingAccountInfo ? <CircularProgress size={isMobile? 20:28}/> : accountDisplayName}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: { xs: 2, sm: 3, md: 4 } }}>
                查看和管理您已接取的图文推广任务。
            </Typography>

            {(tasksState.loading || loadingAccountInfo) && <LinearProgress sx={{ mb: 2 }} />}
            {tasksState.error && <Alert severity="error" sx={{ mb: 2 }}>{tasksState.error}</Alert>}
            
            {!tasksState.loading && !tasksState.error && tasksState.tasks.length === 0 && (
                <Paper elevation={0} sx={{p:3, textAlign: 'center', backgroundColor: 'background.default'}}>
                    <ListChecks size={48} style={{ margin: '0 auto 16px', color: theme.palette.text.secondary }} />
                    <Typography variant="h6" gutterBottom>
                        {accountDisplayName} 暂无已接任务
                    </Typography>
                    <Typography color="text.secondary">
                        您选择的账户当前没有已接取的任务，可以前往任务市场看看。
                    </Typography>
                     <Button component={NextLink} href={`/protected/tasks/market?accountId=${accountId || ''}`} variant="outlined" sx={{mt:2}}>前往任务市场</Button>
                </Paper>
            )}

            {!tasksState.loading && !tasksState.error && tasksState.tasks.length > 0 && (
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                    {tasksState.tasks.map((task) => (
                        <AcceptedTaskListItemCard key={task.id_} task={task} onClick={() => handleTaskClick(task)} isMobile={isMobile} />
                    ))}
                </Grid>
            )}

            {totalPages > 1 && !tasksState.loading && !tasksState.error && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                    <Pagination count={totalPages} page={currentPage + 1} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
                </Box>
            )}
        </Container>
    );
}

export default function AcceptedTasksPage() {
    return (
        <Suspense fallback={<Container maxWidth="xl" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}><CircularProgress /></Container>}>
            <AcceptedTasksContent />
        </Suspense>
    );
}
 