import importlib
import os

# 全局字典，用于存储处理器实例
handlers = {}


# 装饰器函数，用于注册处理器类
def register_handler(handler_name):
    def decorator(cls):
        handlers[handler_name] = cls()  # 将处理器实例存储在handlers字典中
        return cls

    return decorator


# 加载处理器模块
def load_handlers():
    handlers_dir = 'api'  # 处理器模块所在的目录
    for filename in os.listdir(handlers_dir):  # 遍历目录中的文件
        if filename.endswith('api.py'):  # 只处理以'api.py'结尾的文件
            module_name = f"{handlers_dir.replace('/', '.')}.{filename[:-3]}"  # 构建模块名称，去掉文件扩展名'.py'
            importlib.import_module(module_name)  # 动态导入模块
