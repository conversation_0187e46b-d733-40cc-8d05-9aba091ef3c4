from agent.standard_agents.xhs_text_generator import generate_xhs_text

if __name__ == "__main__":
    # 示例输入
    product_info = "这是一款全新的智能手表，支持健康监测和多种运动模式。"
    benchmark_title = "用了这款手表后，我的生活变得更健康了！"
    benchmark_content = "自从入手这款手表，每天都能监测我的心率和睡眠质量，还能记录跑步数据，真的很方便。"

    result = generate_xhs_text(product_info, benchmark_title, benchmark_content)
    print("生成标题：", result.title)
    print("生成内容：", result.content)