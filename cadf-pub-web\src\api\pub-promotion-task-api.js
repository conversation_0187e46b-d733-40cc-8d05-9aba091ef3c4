import api from "@/core/api/api";

export const pubPromotionTaskApi = {
    // 重命名并调整参数: 查询可接取的任务 (现在称为市场任务，依赖账户ID)
    queryMarketTasks: async (account_id, page = 0, limit = 5) => {
        if (!account_id) {
            // 根据新的后端要求, account_id 是必需的
            // 前端应该确保在调用此API之前有 account_id
            // 或者在这里抛出错误/返回一个表示失败的Promise
            console.error("queryMarketTasks 调用缺少必需的 account_id 参数");
            return Promise.reject(new Error("查询任务市场需要账户ID"));
        }
        return await api({
            resource: "pub_promotion_task",
            method_name: "query_market_tasks", // 后端方法名已更改
            account_id,
            page, // Pass page
            limit // Pass limit
        });
    },

    // Placeholder for accepting a task
    acceptTask: async (promotion_task_detail_id, account_id) => {
        console.log(`API call to accept task: ${promotion_task_detail_id} with account ${account_id}`);
        // In a real scenario, this would make an API call:
        return await api({
            resource: "pub_promotion_task",
            method_name: "accept_task",
            promotion_task_detail_id,
            account_id
        });
        // Simulate success for now
        // return Promise.resolve({ message: '任务接取成功 (模拟)', accepted_task: { id_: promotion_task_detail_id /* ... other fields ... */ } });
        // Simulate error:
        // return Promise.reject(new Error('接取任务失败 (模拟)'));
    },

    // 新增: 查询已接取的任务
    queryAcceptedTasks: async (page = 0, limit = 5, account_id = null) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "query_accepted_tasks",
            page,
            limit,
            account_id // 可选的账户ID
        });
    },

    // 新增: 查询账户列表及今日任务状态
    queryAccountsWithTaskStatus: async (platform_filter = null) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "query_accounts_with_task_status",
            platform: platform_filter // Pass platform filter if provided
        });
    },

    // 新增: 放弃任务
    giveUpTask: async (promotion_task_detail_id) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "give_up_task",
            promotion_task_detail_id
        });
    },

    // 新增: 更新发布链接
    updatePublishLink: async (promotion_task_detail_id, publish_url) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "update_publish_link",
            promotion_task_detail_id,
            publish_url
        });
    },

    // 新增: 获取单个任务详情供查看/接取
    getTaskDetailForView: async (promotion_task_detail_id) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "get_promotion_task_detail",
            promotion_task_detail_id
        });
    },

    // 新增: 获取单个账户的详细信息
    getAccountInfo: async (account_id) => {
        return await api({
            resource: "pub_promotion_task",
            method_name: "get_account_info",
            account_id
        });
    },

    // Add other API methods for PubPromotionTaskApi here...

    // "一键接任务" 功能已移除
    // acceptAllTasks: async () => { ... }
};
