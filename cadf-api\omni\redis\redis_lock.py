import time

from omni.redis.redis_client import rc


# 阻塞锁
def acquire_block_lock(lock_name, lock_timeout=60):
    while True:
        if acquire_lock(lock_name, lock_timeout):
            return
        else:
            time.sleep(0.1)


# 获得锁,超时后自动释放,timeout秒为单位
def acquire_lock(lock_name, lock_timeout=60):
    lock_key = f"lock:{lock_name}"
    if rc.setnx(lock_key, "locked"):
        rc.expire(lock_key, lock_timeout)
        return True
    else:
        return False


def release_lock(lock_name):
    lock_key = f"lock:{lock_name}"
    rc.delete(lock_key)
