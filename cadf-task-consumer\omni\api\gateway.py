import importlib
import json
import traceback

from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from omni.api.auth import auth_handler, AccessResponse
from omni.api.blueprint_register import register_routers
from omni.api.exception import MException
from omni.api.handler_register import handlers, load_handlers
from omni.log.log import olog

# 创建 FastAPI 应用
app = FastAPI()

# 启用跨域资源共享
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 启用响应压缩
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 加预定义路由
register_routers(app, 'omni.api.blueprint')
register_routers(app, 'blueprint')

# 加预定义API
importlib.import_module('omni.api.api.asr_api')
importlib.import_module('omni.api.api.oss_api')
importlib.import_module('omni.api.api.tts_api')

# 加载所有处理器
load_handlers()


@app.post("/api")
@app.get("/api")
async def api(request: Request):
    try:
        # 获取请求头中的 Authorization 字段
        token = request.headers.get('Authorization')

        # 根据请求类型获取请求体
        if request.method == "POST":
            body = await request.json()  # 获取 JSON 格式的请求体
        elif request.method == "GET":
            body = dict(request.query_params)  # 获取 URL 参数
        else:
            raise MException('不支持的请求方法')  # 抛出不支持的请求方法异常

        # 从请求体中获取资源和方法名称
        resource = body.get('resource')
        method_name = body.get('method_name')

        # 初始化响应对象
        response = {"code": 200, "message": "OK", "data": None}

        # 获取对应的处理器
        handler = handlers.get(resource)
        if not handler:
            raise MException('无效资源路由')  # 抛出无效资源路由异常

        # 获取处理器中的方法
        method = getattr(handler, method_name, None)
        if not method:
            raise MException('无效方法路由')  # 抛出无效方法路由异常

        # 通过token获取用户信息
        user_id, user_roles = auth_handler(token)

        # 将用户信息添加到请求体中
        body['user_id'] = user_id
        body['user_roles'] = user_roles

        # 进行auth_required的权限验证+后续的逻辑处理
        result = method(body)

        # 权限验证
        if isinstance(result, AccessResponse):
            if result.status_code == 403:
                return Response(
                    content=json.dumps({"code": 403, "message": "请先登录", "data": None}, ensure_ascii=False),
                    media_type="application/json"
                )
            elif result.status_code == 401:
                return Response(
                    content=json.dumps({"code": 401, "message": "权限不足", "data": None}, ensure_ascii=False),
                    media_type="application/json"
                )

        # 使用原始的Response就直接返回
        if isinstance(result, Response):
            return result

        # 根据结果构建响应
        if result is not None:
            response["data"] = result

        # 返回最终的 JSON 响应
        return Response(
            content=json.dumps(response, ensure_ascii=False),
            media_type="application/json"
        )

    except MException as e:
        # 处理自定义异常
        olog.error(f"报错资源：{resource}:{method_name},错误信息：{e}")
        return Response(
            content=json.dumps({"code": 500, "message": str(e), "data": None}, ensure_ascii=False),
            media_type="application/json",
            status_code=200
        )
    except Exception as e:
        # 处理其他异常
        olog.error(f"报错资源：{resource}:{method_name}，{traceback.format_exc()}")
        return Response(
            content=json.dumps({"code": 500, "message": '系统异常', "data": None}, ensure_ascii=False),
            media_type="application/json",
            status_code=200
        )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # 全局异常处理
    olog.error(f"全局系统错误：{type(exc)}，{exc}")
    return Response(
        content=json.dumps({"code": 500, "message": "系统异常", "data": None}, ensure_ascii=False),
        media_type="application/json",
        status_code=200
    )
