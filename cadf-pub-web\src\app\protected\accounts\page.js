"use client";

import {use<PERSON><PERSON>back, useEffect, useState} from 'react';
import {Box, Button, Card, CardContent, Chip, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, IconButton, InputLabel, MenuItem, Paper, Select, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme} from '@mui/material';
import {AlertCircle, BookOpen, CheckCircle, Edit, Instagram, LayoutGrid, Link, Plus, RefreshCw, Trash2, Video, XCircle} from 'lucide-react';
import {accountApi} from '@/api/account-api';
import {dataDictionaryApi} from '@/api/data-dictionary-api';
import {handleGetCookiesAndClose, handleOpenLoginWindow} from '@/api/electron-api';
import {useDispatch} from 'react-redux';
import {addAlert} from '@/core/components/redux/alert-slice';
import {AlertType} from '@/core/components/alert';

// 平台配置 (id 使用中文名称)
const platforms = [
    {id: '抖音', name: '抖音', icon: <Video size={20}/>, color: '#000000', bgColor: '#ffffff', isSupported: false},
    {id: '小红书', name: '小红书', icon: <BookOpen size={20}/>, color: '#ffffff', bgColor: '#ff2442', isSupported: true},
    {id: '微博', name: '微博', icon: <Instagram size={20}/>, color: '#ffffff', bgColor: '#ff8200', isSupported: false},
    {id: 'Bilibili', name: 'Bilibili', icon: <Video size={20}/>, color: '#ffffff', bgColor: '#fb7299', isSupported: false},
    {id: '快手', name: '快手', icon: <Video size={20}/>, color: '#ffffff', bgColor: '#ff4906', isSupported: false},
    {id: '知乎', name: '知乎', icon: <LayoutGrid size={20}/>, color: '#ffffff', bgColor: '#0066ff', isSupported: false}
];

// 获取平台按钮样式
const getPlatformButtonStyle = (platform, isSelected = false, isDouyin = false) => {
    const baseStyle = {
        color: platform.color === '#ffffff' ? platform.bgColor : platform.color,
        borderColor: platform.bgColor,
        transition: 'all 0.2s ease-in-out',
        boxShadow: isSelected ? '0 2px 8px rgba(0, 0, 0, 0.1)' : 'none',
        height: '36px',
    };

    const douyinStyle = isDouyin ? {
        border: '1px solid black',
        backgroundColor: 'transparent',
        color: '#000000'
    } : {};

    const selectedStyle = isSelected ? {
        backgroundColor: isDouyin ? '#000000' : platform.bgColor,
        color: isDouyin ? '#ffffff' : platform.color,
        transform: 'translateY(-1px)',
        boxShadow: `0 4px 10px ${platform.bgColor}40`,
        '&:hover': {
            backgroundColor: isDouyin ? '#000000' : platform.bgColor,
            boxShadow: `0 4px 12px ${platform.bgColor}60`,
        }
    } : {
        '&:hover': {
            borderColor: platform.bgColor,
            backgroundColor: `${platform.bgColor}10`,
            transform: 'translateY(-1px)',
            boxShadow: `0 2px 8px ${platform.bgColor}20`,
            ...(isDouyin && {borderColor: 'black'})
        }
    };

    return {...baseStyle, ...douyinStyle, ...selectedStyle};
};

export default function AccountsPage() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    const dispatch = useDispatch();

    const [selectedPlatform, setSelectedPlatform] = useState(null);
    const [accounts, setAccounts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [openPlatformDialog, setOpenPlatformDialog] = useState(false);
    const [isCheckingLogin, setIsCheckingLogin] = useState(false);
    const [openXhsConfirmDialog, setOpenXhsConfirmDialog] = useState(false);
    const [platformToAdd, setPlatformToAdd] = useState(null);
    const [isConfirmingLogin, setIsConfirmingLogin] = useState(false);

    const [openDeleteConfirmDialog, setOpenDeleteConfirmDialog] = useState(false);
    const [accountToDeleteId, setAccountToDeleteId] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // 编辑账号相关状态
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [accountToEdit, setAccountToEdit] = useState(null);
    const [editingAccountName, setEditingAccountName] = useState("");
    const [editingAccountDomain, setEditingAccountDomain] = useState("");
    const [availableDomains, setAvailableDomains] = useState([]);
    const [isSavingEdit, setIsSavingEdit] = useState(false);

    const [totalAccounts, setTotalAccounts] = useState(0);

    const [openXhsLoginInstructionDialog, setOpenXhsLoginInstructionDialog] = useState(false);

    const loadDomains = useCallback(async () => {
        try {
            const response = await dataDictionaryApi.queryAll('domain');
            if (response && Array.isArray(response.dictionaries)) {
                setAvailableDomains(response.dictionaries.map(d => d.value).filter(Boolean));
            } else {
                console.error('加载领域列表失败: API 返回格式不正确', response);
                dispatch(addAlert({type: AlertType.ERROR, message: '加载领域选项失败'}));
            }
        } catch (error) {
            console.error('加载领域列表失败:', error);
            dispatch(addAlert({type: AlertType.ERROR, message: `加载领域选项失败: ${error.message || '请稍后重试'}`}));
        }
    }, [dispatch]);

    const loadAccounts = useCallback(async (currentPlatform) => {
        setIsLoading(true);
        try {
            const response = await accountApi.queryAll(currentPlatform);
            if (response && Array.isArray(response.accounts)) {
                setAccounts(response.accounts.map(account => ({
                    ...account,
                })));
                setTotalAccounts(response.total_count || 0);
            } else {
                console.error('加载账号失败: API 返回格式不正确', response);
                setAccounts([]);
                setTotalAccounts(0);
                dispatch(addAlert({type: AlertType.ERROR, message: '加载账号列表失败: 数据格式错误'}));
            }
        } catch (error) {
            console.error('加载账号失败:', error);
            setAccounts([]);
            setTotalAccounts(0);
            dispatch(addAlert({type: AlertType.ERROR, message: `加载账号列表失败: ${error.message || '请稍后重试'}`}));
        } finally {
            setIsLoading(false);
        }
    }, [dispatch]);

    useEffect(() => {
        loadAccounts(selectedPlatform);
        loadDomains();
    }, [selectedPlatform, loadAccounts, loadDomains]);

    const checkAllLoginStatus = async () => {
        dispatch(addAlert({type: AlertType.INFO, message: '状态由后端实时更新，此按钮功能待定'}));
    };

    const handlePlatformSelect = async (platformId) => {
        setOpenPlatformDialog(false);
        
        const selectedPlatformConfig = platforms.find(p => p.id === platformId);

        if (!selectedPlatformConfig) {
            console.error(`[AccountsPage] Platform configuration not found for ID: ${platformId}`);
            dispatch(addAlert({type: AlertType.ERROR, message: '无效的平台选择'}));
            return;
        }

        if (!selectedPlatformConfig.isSupported) {
            dispatch(addAlert({type: AlertType.WARNING, message: '该平台暂不支持添加账号，敬请期待！'}));
            return;
        }
        
        setPlatformToAdd(platformId);
        
        if (selectedPlatformConfig.id === '小红书') {
            setOpenXhsLoginInstructionDialog(true);
        }
    };

    const handleOpenXhsLoginWindow = async () => {
        setOpenXhsLoginInstructionDialog(false);
        const result = await handleOpenLoginWindow('https://creator.xiaohongshu.com/new/home?source=official');
        if (result && result.success) {
            setOpenXhsConfirmDialog(true);
        } else {
            setPlatformToAdd(null);
        }
    };

    const handleConfirmXhsLogin = async () => {
        if (!platformToAdd) {
            console.error('未指定要添加的平台');
            dispatch(addAlert({type: AlertType.ERROR, message: '添加账号失败：未知的平台'}));
            return;
        }
        setIsConfirmingLogin(true);
        try {
            const cookies = await handleGetCookiesAndClose();
            if (cookies && cookies.length > 0) {
                await accountApi.create(platformToAdd, cookies);
                dispatch(addAlert({type: AlertType.SUCCESS, message: `${platformToAdd}账号添加成功！`}));
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
                setSelectedPlatform(null);
                loadAccounts(null);
            } else {
                if (!cookies) {
                    dispatch(addAlert({type: AlertType.WARNING, message: `未能获取${platformToAdd} Cookie，请确保已成功登录并重试。`}));
                }
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
            }
        } catch (error) {
            console.error(`确认${platformToAdd}登录或创建账号时发生错误:`, error);
            dispatch(addAlert({type: AlertType.ERROR, message: `添加${platformToAdd}账号失败: ${error.message || '请检查网络或联系管理员'}`}));
            setOpenXhsConfirmDialog(false);
            setPlatformToAdd(null);
        } finally {
            setIsConfirmingLogin(false);
        }
    };

    const handleDeleteAccount = (accountId) => {
        setAccountToDeleteId(accountId);
        setOpenDeleteConfirmDialog(true);
    };

    const handleCloseDeleteConfirmDialog = () => {
        if (isDeleting) return;
        setOpenDeleteConfirmDialog(false);
        setAccountToDeleteId(null);
    };

    const confirmDeleteAccount = async () => {
        if (!accountToDeleteId) return;

        setIsDeleting(true);
        try {
            await accountApi.delete(accountToDeleteId);
            dispatch(addAlert({type: AlertType.SUCCESS, message: '账号删除成功'}));
            handleCloseDeleteConfirmDialog();
            loadAccounts(selectedPlatform);

        } catch (error) {
            console.error('删除账号失败:', error);
            dispatch(addAlert({type: AlertType.ERROR, message: `删除账号失败: ${error.message || '请稍后重试'}`}));
        } finally {
            setIsDeleting(false);
        }
    };

    const handleOpenEditDialog = (account) => {
        setAccountToEdit(account);
        setEditingAccountName(account.name || "");
        setEditingAccountDomain(account.domain || "");
        setOpenEditDialog(true);
    };

    const handleCloseEditDialog = () => {
        if (isSavingEdit) return;
        setOpenEditDialog(false);
        setAccountToEdit(null);
        setEditingAccountName("");
        setEditingAccountDomain("");
    };

    const handleSaveEdit = async () => {
        if (!accountToEdit) return;

        const trimmedName = editingAccountName.trim();
        const selectedDomain = editingAccountDomain;

        if (!trimmedName) {
            dispatch(addAlert({type: AlertType.WARNING, message: '账号名称不能为空'}));
            return;
        }

        if (trimmedName === accountToEdit.name && selectedDomain === (accountToEdit.domain || "")) {
            dispatch(addAlert({type: AlertType.INFO, message: '未检测到任何更改'}));
            handleCloseEditDialog();
            return;
        }

        setIsSavingEdit(true);
        try {
            await accountApi.modify(
                accountToEdit.id_,
                trimmedName !== accountToEdit.name ? trimmedName : undefined,
                undefined,
                selectedDomain !== (accountToEdit.domain || "") ? selectedDomain : undefined,
                undefined,
                undefined
            );
            dispatch(addAlert({type: AlertType.SUCCESS, message: '账号信息更新成功'}));
            handleCloseEditDialog();
            loadAccounts(selectedPlatform);
        } catch (error) {
            console.error('更新账号信息失败:', error);
            dispatch(addAlert({type: AlertType.ERROR, message: `更新账号信息失败: ${error.message || '请稍后重试'}`}));
        } finally {
            setIsSavingEdit(false);
        }
    };

    const getPlatformInfo = (platformId) => {
        return platforms.find(p => p.id === platformId) || {id: platformId, name: platformId, icon: <Link size={20}/>, color: '#cccccc', bgColor: '#f0f0f0'};
    };

    const handlePlatformFilterChange = (platformId) => {
        setSelectedPlatform(platformId);
    };

    const handleRefresh = () => {
        setSelectedPlatform(null);
        loadAccounts(null);
    };

    const getLoginStatusDisplay = (status) => {
        if (status === '在线') {
            return (
                <Tooltip title="在线/已登录">
                    <Chip size="small" icon={<CheckCircle size={14}/>} label="在线" color="success" variant="outlined"/>
                </Tooltip>
            );
        } else if (status === '离线') {
            return (
                <Tooltip title="离线/未登录">
                    <Chip size="small" icon={<XCircle size={14}/>} label="离线" color="warning" variant="outlined"/>
                </Tooltip>
            );
        } else if (status === '封禁') {
            return (
                <Tooltip title="账号异常/被封禁">
                    <Chip size="small" icon={<XCircle size={14}/>} label="封禁" color="error" variant="outlined"/>
                </Tooltip>
            );
        } else {
            return (
                <Tooltip title={`状态未知 ${status ? `(${status})` : ''}`}>
                    <Chip
                        size="small"
                        icon={<AlertCircle size={14}/>}
                        label={status || "未知"}
                        variant="outlined"
                        sx={{borderColor: theme.palette.grey[400], color: theme.palette.grey[600]}}
                    />
                </Tooltip>
            );
        }
    };

    return (
        <Box sx={{p: isMobile ? 1 : 3, maxWidth: '100%'}}>
            <Box sx={{
                display: 'flex',
                flexDirection: isMobile ? 'column' : 'row',
                justifyContent: 'space-between',
                alignItems: isMobile ? 'flex-start' : 'center',
                mb: isMobile ? 2 : 3,
                px: isMobile ? 1 : 0,
                gap: isMobile ? 1 : 0
            }}>
                <Typography
                    variant={isMobile ? "h5" : "h4"}
                    component="h1"
                    sx={{fontWeight: 'bold', mb: isMobile ? 1 : 0}}
                >
                    平台账号管理
                </Typography>
                <Box sx={{
                    display: 'flex',
                    flexDirection: isMobile ? 'column' : 'row',
                    gap: 1,
                    width: isMobile ? '100%' : 'auto'
                }}>
                    <Button
                        variant="contained"
                        startIcon={<Plus size={18}/>}
                        onClick={() => setOpenPlatformDialog(true)}
                        size={isMobile ? "small" : "medium"}
                        fullWidth={isMobile}
                    >
                        添加账号
                    </Button>
                </Box>
            </Box>

            <Paper sx={{mb: 3, overflow: 'hidden'}}>
                <Box
                    sx={{
                        p: isMobile ? '1rem 0.75rem' : 2.5,
                        borderBottom: 1,
                        borderColor: 'divider',
                        overflowX: 'auto',
                        backgroundColor: theme => theme.palette.background.default
                    }}
                >
                    <Stack direction="column" spacing={1}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>平台筛选</Typography>
                        <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: isMobile ? 'repeat(2, 1fr)' : 'repeat(auto-fill, minmax(100px, 1fr))',
                            gap: 1.5,
                            pb: 1
                        }}>
                            <Button
                                variant={selectedPlatform === null ? "contained" : "outlined"}
                                size="small"
                                onClick={() => handlePlatformFilterChange(null)}
                                sx={{
                                    minWidth: 'auto',
                                    height: '36px',
                                    transition: 'all 0.2s ease-in-out',
                                    boxShadow: selectedPlatform === null ? '0 2px 8px rgba(0, 0, 0, 0.1)' : 'none',
                                    '&:hover': {
                                        transform: 'translateY(-1px)',
                                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                                    }
                                }}
                            >
                                全部账号
                            </Button>
                            {platforms.map((platform) => (
                                <Button
                                    key={platform.id}
                                    variant={selectedPlatform === platform.id ? "contained" : "outlined"}
                                    size="small"
                                    startIcon={platform.icon}
                                    onClick={() => handlePlatformFilterChange(platform.id)}
                                    sx={{
                                        minWidth: 'auto',
                                        justifyContent: 'center',
                                        ...getPlatformButtonStyle(
                                            platform,
                                            selectedPlatform === platform.id,
                                            platform.id === '抖音'
                                        )
                                    }}
                                >
                                    {platform.name}
                                </Button>
                            ))}
                        </Box>
                    </Stack>
                </Box>

                <Box sx={{p: isMobile ? '0.75rem' : 2, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', borderBottom: 1, borderColor: 'divider'}}>
                    <Tooltip title="刷新列表">
                         <span style={{display: 'inline-block', marginRight: '8px'}}>
                             <IconButton size="small" onClick={handleRefresh} disabled={isLoading}>
                                 {isLoading ? <CircularProgress size={16}/> : <RefreshCw size={16}/>}
                             </IconButton>
                         </span>
                    </Tooltip>
                    <Typography variant="body2" color="text.secondary">
                        共 {totalAccounts} 个账号
                    </Typography>
                </Box>

                {isLoading ? (
                    <Box sx={{display: 'flex', justifyContent: 'center', p: 4}}>
                        <CircularProgress size={40}/>
                    </Box>
                ) : accounts.length === 0 ? (
                    <Box sx={{p: 4, textAlign: 'center'}}>
                        <Typography variant="body1" color="text.secondary">
                            {'暂无账号信息'}
                        </Typography>
                    </Box>
                ) : isMobile ? (
                    <Box>
                        <Box sx={{p: '0.75rem'}}>
                            {accounts.map((account) => {
                                const platformInfo = getPlatformInfo(account.platform);
                                const isDouyin = account.platform === '抖音';

                                return (
                                    <Card key={account.id_} sx={{
                                        mb: 1.5, 
                                        overflow: 'visible',
                                        transition: 'box-shadow 0.3s ease, transform 0.2s ease',
                                        '&:hover': {
                                            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                                            transform: 'translateY(-2px)'
                                        },
                                        border: '1px solid',
                                        borderColor: 'divider',
                                        borderRadius: '12px',
                                        position: 'relative'
                                    }}>
                                        <CardContent sx={{p: '16px'}}>
                                            <Box sx={{
                                                display: 'flex', 
                                                justifyContent: 'space-between', 
                                                mb: 1,
                                                position: 'relative'
                                            }}>
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 1
                                                }}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        width: '32px',
                                                        height: '32px',
                                                        borderRadius: '50%',
                                                        backgroundColor: platformInfo.bgColor,
                                                        color: platformInfo.color,
                                                        flexShrink: 0
                                                    }}>
                                                        {platformInfo.icon}
                                                    </Box>
                                                    <Typography variant="subtitle1" sx={{
                                                        fontWeight: 600, 
                                                        fontSize: '1rem',
                                                        maxWidth: '150px',
                                                        overflow: 'hidden', 
                                                        textOverflow: 'ellipsis', 
                                                        whiteSpace: 'nowrap'
                                                    }}>
                                                        {account.name || '-'}
                                                    </Typography>
                                                </Box>
                                                
                                                {getLoginStatusDisplay(account.status)}
                                            </Box>
                                            
                                            <Box sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: 0.75,
                                                mt: 1.5,
                                                mb: 0.5
                                            }}>
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.75
                                                }}>
                                                    <Typography variant="caption" sx={{
                                                        color: 'text.secondary',
                                                        fontWeight: 500,
                                                        minWidth: '40px'
                                                    }}>
                                                        平台:
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        color: 'text.primary',
                                                        fontWeight: 500
                                                    }}>
                                                        {platformInfo.name}
                                                    </Typography>
                                                </Box>
                                                
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.75
                                                }}>
                                                    <Typography variant="caption" sx={{
                                                        color: 'text.secondary',
                                                        fontWeight: 500,
                                                        minWidth: '40px'
                                                    }}>
                                                        领域:
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        color: account.domain ? 'text.primary' : 'text.secondary',
                                                        fontStyle: account.domain ? 'normal' : 'italic'
                                                    }}>
                                                        {account.domain || '未设置'}
                                                    </Typography>
                                                </Box>
                                                
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.75
                                                }}>
                                                    <Typography variant="caption" sx={{
                                                        color: 'text.secondary',
                                                        fontWeight: 500,
                                                        minWidth: '40px'
                                                    }}>
                                                        添加:
                                                    </Typography>
                                                    <Typography variant="body2" sx={{color: 'text.secondary'}}>
                                                        {account.create_at ? new Date(account.create_at * 1000).toLocaleDateString('zh-CN') : '-'}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                            
                                            <Box sx={{
                                                display: 'flex',
                                                justifyContent: 'flex-end',
                                                mt: 1.5,
                                                pt: 1,
                                                borderTop: '1px solid',
                                                borderColor: 'divider'
                                            }}>
                                                <Button
                                                    size="small"
                                                    startIcon={<Edit size={14}/>}
                                                    onClick={() => handleOpenEditDialog(account)}
                                                    sx={{
                                                        mr: 1,
                                                        color: 'primary.main',
                                                        textTransform: 'none',
                                                        fontWeight: 500
                                                    }}
                                                >
                                                    编辑
                                                </Button>
                                                <Button
                                                    size="small"
                                                    color="error"
                                                    startIcon={<Trash2 size={14}/>}
                                                    onClick={() => handleDeleteAccount(account.id_)}
                                                    sx={{
                                                        textTransform: 'none',
                                                        fontWeight: 500
                                                    }}
                                                >
                                                    删除
                                                </Button>
                                            </Box>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </Box>
                    </Box>
                ) : (
                    <Box>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell>平台</TableCell>
                                        <TableCell>账号名称</TableCell>
                                        <TableCell>领域</TableCell>
                                        <TableCell>状态</TableCell>
                                        <TableCell>添加时间</TableCell>
                                        <TableCell align="right">操作</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {accounts.map((account) => {
                                        const platformInfo = getPlatformInfo(account.platform);
                                        const isDouyin = account.platform === '抖音';

                                        return (
                                            <TableRow key={account.id_} hover>
                                                <TableCell>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        startIcon={platformInfo.icon}
                                                        onClick={() => handlePlatformFilterChange(account.platform)}
                                                        sx={{
                                                            minHeight: '24px',
                                                            minWidth: 'unset',
                                                            p: '4px 8px',
                                                            fontSize: '0.75rem',
                                                            borderRadius: '6px',
                                                            transition: 'all 0.2s ease',
                                                            ...getPlatformButtonStyle(platformInfo, selectedPlatform === account.platform, isDouyin)
                                                        }}
                                                    >
                                                        {platformInfo.name}
                                                    </Button>
                                                </TableCell>
                                                <TableCell>{account.name || '-'}</TableCell>
                                                <TableCell>{account.domain || '-'}</TableCell>
                                                <TableCell>{getLoginStatusDisplay(account.status)}</TableCell>
                                                <TableCell>
                                                    {account.create_at ? new Date(account.create_at * 1000).toLocaleDateString('zh-CN') : '-'}
                                                </TableCell>
                                                <TableCell align="right">
                                                    <Tooltip title="删除账号">
                                                        <IconButton size="small" color="error" onClick={() => handleDeleteAccount(account.id_)}>
                                                            <Trash2 size={18}/>
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="编辑账号">
                                                        <IconButton size="small" color="primary" onClick={() => handleOpenEditDialog(account)}>
                                                            <Edit size={18}/>
                                                        </IconButton>
                                                    </Tooltip>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                )}
            </Paper>

            <Dialog open={openPlatformDialog} onClose={() => setOpenPlatformDialog(false)} maxWidth="xs" fullWidth>
                <DialogTitle>选择要添加的平台</DialogTitle>
                <DialogContent>
                    <Box sx={{display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 2, mt: 1}}>
                        {platforms.map((platform) => {
                            const isDouyin = platform.id === '抖音';
                            const isDisabled = !platform.isSupported;
                            
                            return (
                                <Button
                                    key={platform.id}
                                    variant="outlined"
                                    startIcon={platform.icon}
                                    fullWidth
                                    onClick={() => handlePlatformSelect(platform.id)}
                                    disabled={isDisabled}
                                    sx={{
                                        justifyContent: 'flex-start',
                                        py: 1.5,
                                        borderRadius: '8px',
                                        transition: 'all 0.2s ease',
                                        color: platform.color === '#ffffff' ? platform.bgColor : platform.color,
                                        borderColor: platform.bgColor,
                                        ...(isDouyin && {border: '1px solid black', color: '#000000'}),
                                        '&:hover': {
                                            borderColor: platform.bgColor,
                                            backgroundColor: `${platform.bgColor}10`,
                                            transform: isDisabled ? 'none' : 'translateY(-2px)',
                                            boxShadow: isDisabled ? 'none' : `0 4px 12px ${platform.bgColor}30`,
                                            ...(isDouyin && {borderColor: 'black'})
                                        },
                                        ...(isDisabled ? {
                                            opacity: 0.6,
                                            cursor: 'not-allowed'
                                        } : {})
                                    }}
                                >
                                    {platform.name}
                                    {!platform.isSupported && (
                                        <Chip 
                                            label="即将上线" 
                                            size="small" 
                                            sx={{ml: 1, fontSize: '0.65rem'}}
                                        />
                                    )}
                                </Button>
                            );
                        })}
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenPlatformDialog(false)}>取消</Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openXhsLoginInstructionDialog} onClose={() => {
                setOpenXhsLoginInstructionDialog(false);
                setPlatformToAdd(null);
            }} maxWidth="sm" fullWidth>
                <DialogTitle sx={{ 
                    pb: 1, 
                    display: 'flex', 
                    alignItems: 'center',
                    gap: 1
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: theme => theme.palette.primary.main,
                        color: '#ffffff',
                        flexShrink: 0
                    }}>
                        <BookOpen size={16}/>
                    </Box>
                    <Typography variant="h6">平台账号登录指引</Typography>
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    <Paper 
                        elevation={0} 
                        sx={{ 
                            p: 2, 
                            mb: 2, 
                            bgcolor: theme => `${theme.palette.primary.main}08`,
                            border: theme => `1px solid ${theme.palette.primary.main}20`,
                            borderRadius: 2
                        }}
                    >
                        <Typography sx={{ fontWeight: 500, mb: 1.5, color: 'text.primary' }}>
                            为确保成功添加{platformToAdd}账号，请严格按照以下步骤操作：
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>1</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        点击下方按钮，在新窗口中完成{platformToAdd}账号登录
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        请使用{platformToAdd}创作者中心账号登录
                                    </Typography>
                                </Box>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>2</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        登录成功后<strong>请勿关闭{platformToAdd}页面</strong>
                                    </Typography>
                                    <Typography variant="caption" sx={{ color: theme => theme.palette.error.main, display: 'block' }}>
                                        系统需要从{platformToAdd}页面获取授权信息，提前关闭将导致添加失败
                                    </Typography>
                                </Box>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>3</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        回到此窗口，点击"下一步：确认已登录"按钮
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        系统将自动获取您的授权信息并完成账号添加
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Paper>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button 
                        onClick={() => {
                            setOpenXhsLoginInstructionDialog(false);
                            setPlatformToAdd(null);
                        }}
                        variant="outlined"
                        sx={{ borderColor: '#d0d0d0', color: 'text.secondary' }}
                    >
                        取消
                    </Button>
                    <Button 
                        variant="contained" 
                        onClick={handleOpenXhsLoginWindow}
                        color="primary"
                    >
                        开始登录{platformToAdd}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openXhsConfirmDialog} onClose={(event, reason) => {
                if (reason && reason === 'backdropClick') return;
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
            }} disableEscapeKeyDown maxWidth="sm" fullWidth>
                <DialogTitle sx={{ 
                    pb: 1, 
                    display: 'flex', 
                    alignItems: 'center',
                    gap: 1
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: theme => theme.palette.primary.main,
                        color: '#ffffff',
                        flexShrink: 0
                    }}>
                        <BookOpen size={16}/>
                    </Box>
                    <Typography variant="h6">确认添加{platformToAdd}账号</Typography>
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                            请确认您已在{platformToAdd}创作者中心完成登录并保持页面打开状态。
                        </Typography>
                        
                        <Box sx={{ 
                            bgcolor: '#f5f5f5', 
                            borderRadius: 1, 
                            p: 2, 
                            mb: 2,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1.5
                        }}>
                            <Box sx={{ color: theme => theme.palette.warning.main }}>
                                <AlertCircle size={24} />
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                                <strong>重要提示：</strong>点击"确认已登录"后，系统将从{platformToAdd}页面获取授权信息。如果您已关闭{platformToAdd}页面或未登录成功，将无法添加账号。
                            </Typography>
                        </Box>
                    </Box>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button 
                        onClick={() => {
                            setOpenXhsConfirmDialog(false);
                            setPlatformToAdd(null);
                        }} 
                        disabled={isConfirmingLogin}
                        variant="outlined"
                        sx={{ borderColor: '#d0d0d0', color: 'text.secondary' }}
                    >
                        取消
                    </Button>
                    <Button 
                        variant="contained" 
                        color="primary"
                        onClick={handleConfirmXhsLogin} 
                        disabled={isConfirmingLogin} 
                        startIcon={isConfirmingLogin ? <CircularProgress size={16} color="inherit"/> : null}
                    >
                        {isConfirmingLogin ? '正在添加账号...' : '确认已登录'}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openDeleteConfirmDialog} onClose={handleCloseDeleteConfirmDialog} aria-labelledby="delete-confirm-dialog-title" aria-describedby="delete-confirm-dialog-description">
                <DialogTitle id="delete-confirm-dialog-title">确认删除账号</DialogTitle>
                <DialogContent>
                    <Typography id="delete-confirm-dialog-description">确定要删除此账号吗？此操作不可恢复。</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDeleteConfirmDialog} disabled={isDeleting}>取消</Button>
                    <Button onClick={confirmDeleteAccount} color="error" variant="contained" disabled={isDeleting} startIcon={isDeleting ? <CircularProgress size={16} color="inherit"/> : null}>
                        {isDeleting ? '删除中...' : '确认删除'}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openEditDialog} onClose={handleCloseEditDialog} aria-labelledby="edit-dialog-title" fullWidth maxWidth="xs">
                <DialogTitle id="edit-dialog-title">编辑账号信息</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        id="name"
                        label="账号名称"
                        type="text"
                        fullWidth
                        value={editingAccountName}
                        onChange={(e) => setEditingAccountName(e.target.value)}
                        sx={{mb: 2}}
                    />
                    <FormControl fullWidth margin="dense">
                        <InputLabel id="domain-select-label">领域</InputLabel>
                        <Select
                            labelId="domain-select-label"
                            id="domain-select"
                            value={editingAccountDomain}
                            label="领域"
                            onChange={(e) => setEditingAccountDomain(e.target.value)}
                        >
                            <MenuItem value="">
                                <em>无 (或清除领域)</em>
                            </MenuItem>
                            {availableDomains.map((domainValue) => (
                                <MenuItem key={domainValue} value={domainValue}>
                                    {domainValue}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseEditDialog} disabled={isSavingEdit}>取消</Button>
                    <Button onClick={handleSaveEdit} variant="contained" color="primary" disabled={isSavingEdit} startIcon={isSavingEdit ? <CircularProgress size={16} color="inherit"/> : null}>
                        {isSavingEdit ? '保存中...' : '保存'}
                    </Button>
                </DialogActions>
            </Dialog>

        </Box>
    );
}
