import time

from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import ProductTraffic, Product


@register_handler('adv_product_traffic')
class AdvProductTrafficApi:
    @auth_required(['advertiser', 'admin'])
    def query_all(self, data):
        """
        **目的**: 查询指定用户广告主的产品列表，并合并相关的流量统计数据和产品图片签名URL。

        **算法描述**:
            - **参数解析**: 获取用户ID (`user_id`)、页码 (`page`) 和每页数量 (`limit`)。
            - **分页查询产品**: 根据 `user_id` 和 `is_deleted=False` 标志，在 `Product` 集合中按创建时间降序查询产品，并根据 `page` 和 `limit` 进行分页，同时计算产品总数 `total`。
            - **准备流量数据**:
                - 提取当前页产品的 ID 列表 (`product_ids`)。
                - 根据 `user_id` 和 `product_ids` 查询 `ProductTraffic` 集合。
                - 将查询到的流量数据构建成一个以 `product_id` 为键的字典 `traffic_map`，方便后续查找。
            - **初始化OSS客户端**: 获取腾讯云OSS客户端实例，用于生成图片签名URL。
            - **组装结果列表**:
                - 遍历分页查询到的每个产品字典 (`product_dict`)。
                - **处理产品图片**:
                    - 获取产品的图片列表 (`images`)。
                    - 如果存在图片，遍历图片信息，为每个 `oss_key` 生成对应的 OSS 签名访问 URL (`signed_url`)，并处理生成失败的情况。
                    - 按 `order` 字段对图片列表进行排序。
                - **合并流量信息**:
                    - 根据 `product_id` 在 `traffic_map` 中查找对应的流量数据。
                    - 如果找到，则将流量统计信息（如 `platforms`, `total_view_count` 等）更新到产品字典中，`last_updated_at` 使用流量数据中的时间。
                    - 如果未找到，则设置默认的流量统计信息（空列表或 0），`last_updated_at` 使用产品的创建时间 (`create_at`)。
                - 添加 `product_id` 字段到产品字典中。
                - 将处理后的产品字典添加到最终的结果列表 `results` 中。
            - **返回响应**: 返回包含处理后的产品列表 (`items`)、产品总数 (`total`)、当前页码 (`page`) 和每页数量 (`limit`) 的字典。
        """
        user_id = data.get('user_id')
        page = data.get('page', 1)
        limit = data.get('limit', 10)

        skip = (page - 1) * limit

        base_query = Product.objects(user_id=user_id, is_deleted=False).order_by('-create_at')

        total = base_query.count()

        products = base_query.skip(skip).limit(limit)
        product_list = docs_to_dict(products)

        product_ids = [p.get('id_') for p in product_list if p.get('id_')]
        product_traffics = ProductTraffic.objects(user_id=user_id, product_id__in=product_ids)
        traffic_map = {str(traffic.product_id): doc_to_dict(traffic) for traffic in product_traffics}

        oss_client = OSSClient.getInstance()

        results = []
        for product_dict in product_list:
            product_id = product_dict.get('id_')
            if not product_id:
                continue

            images = product_dict.get('images', [])
            if images:
                for image_info in images:
                    oss_key = image_info.get('oss_key')
                    if oss_key:
                        try:
                            signed_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                            image_info['signed_url'] = signed_url
                        except Exception as e:
                            print(f"Error generating signed URL for key {oss_key}: {e}")
                            image_info['signed_url'] = None
                images.sort(key=lambda img: img.get('order', 0))

            traffic_data = traffic_map.get(product_id)

            if traffic_data:
                product_dict.update({
                    "platforms": traffic_data.get("platforms", []),
                    "total_view_count": traffic_data.get("total_view_count", 0),
                    "total_like_count": traffic_data.get("total_like_count", 0),
                    "total_comment_count": traffic_data.get("total_comment_count", 0),
                    "total_favorite_count": traffic_data.get("total_favorite_count", 0),
                    "total_share_count": traffic_data.get("total_share_count", 0),
                    "last_updated_at": traffic_data.get("last_updated_at", int(time.time()))
                })
            else:
                product_dict.update({
                    "platforms": [],
                    "total_view_count": 0,
                    "total_like_count": 0,
                    "total_comment_count": 0,
                    "total_favorite_count": 0,
                    "total_share_count": 0,
                    "last_updated_at": product_dict.get("create_at")
                })
            product_dict['product_id'] = product_id
            results.append(product_dict)

        return {
            'items': results,
            'total': total,
            'page': page,
            'limit': limit
        }

    @auth_required(['advertiser', 'admin'])
    def get_total_stats(self, data):
        """
        **目的**: 计算并返回指定用户所有未删除产品的总流量统计数据（观看、点赞、评论、收藏、分享）。

        **算法描述**:
            - **获取用户ID**: 从输入参数 `data` 中提取 `user_id`。
            - **查询产品ID**: 根据 `user_id` 和 `is_deleted=False` 标志，在 `Product` 集合中查询该用户所有未删除产品的 ID 列表。
            - **处理无产品情况**: 如果用户没有任何未删除的产品，直接返回所有统计数据均为 0 的字典。
            - **查询流量数据**: 根据 `user_id` 和之前获得的产品 ID 列表，在 `ProductTraffic` 集合中查询相关的流量记录，仅获取必要的统计字段（如 `total_view_count`, `total_like_count` 等）。
            - **累加统计数据**:
                - 初始化一个包含所有统计字段且初始值为 0 的字典 `total_stats`。
                - 遍历查询到的 `ProductTraffic` 记录。
                - 对于每条记录，将其各项统计数据（如果值为 `None` 则视为 0）累加到 `total_stats` 对应字段上。
            - **返回结果**: 返回包含最终累加统计结果的 `total_stats` 字典。
        """
        user_id = data.get('user_id')

        product_ids = [str(p.id) for p in Product.objects(user_id=user_id, is_deleted=False).only('id')]

        if not product_ids:
            return {
                "total_view_count": 0,
                "total_like_count": 0,
                "total_comment_count": 0,
                "total_favorite_count": 0,
                "total_share_count": 0,
            }

        traffic_records = ProductTraffic.objects(
            user_id=user_id,
            product_id__in=product_ids
        ).only(
            'total_view_count',
            'total_like_count',
            'total_comment_count',
            'total_favorite_count',
            'total_share_count'
        )

        total_stats = {
            "total_view_count": 0,
            "total_like_count": 0,
            "total_comment_count": 0,
            "total_favorite_count": 0,
            "total_share_count": 0,
        }

        for record in traffic_records:
            total_stats["total_view_count"] += record.total_view_count or 0
            total_stats["total_like_count"] += record.total_like_count or 0
            total_stats["total_comment_count"] += record.total_comment_count or 0
            total_stats["total_favorite_count"] += record.total_favorite_count or 0
            total_stats["total_share_count"] += record.total_share_count or 0

        return total_stats
