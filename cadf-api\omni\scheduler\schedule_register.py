"""
#添加在scheduler包中且以scheduler.py结尾的文件
@register_scheduler(trigger='interval', seconds=1)
class DemoScheduler(BaseScheduler):
    def run_task(self):
        syslog.info('run demo')
"""
import importlib
import os

from apscheduler.schedulers.background import BackgroundScheduler
from pytz import timezone

from omni.log.log import olog  # 导入 olog
from omni.scheduler.base_schedule import BaseScheduler

scheduler = BackgroundScheduler(timezone=timezone('Asia/Shanghai'))


# 装饰器函数，用于注册类
def register_scheduler(**trigger_args):
    def decorator(cls):
        enabled = trigger_args.pop('enabled', True)  # 默认启用
        if enabled and issubclass(cls, BaseScheduler) and cls is not BaseScheduler:
            olog.info(f"注册调度器: {cls.__name__}，触发器参数: {trigger_args}")  # 修改为中文日志
            cls().async_start(scheduler, **trigger_args)
        elif not enabled:
            olog.info(f"调度器 {cls.__name__} 已禁用.")  # 修改为中文日志
        return cls

    return decorator


# 加载处理器模块
def load_schedulers():
    schedulers_dir = 'scheduler'  # 处理器模块所在的目录
    if not os.path.exists(schedulers_dir):  # worker不存在就不注册
        return
    for filename in os.listdir(schedulers_dir):  # 遍历目录中的文件
        if filename.endswith('scheduler.py'):  # 只处理以'scheduler.py'结尾的文件
            module_name = f"{schedulers_dir.replace('/', '.')}.{filename[:-3]}"  # 构建模块名称，去掉文件扩展名'.py'
            importlib.import_module(module_name)  # 动态导入模块


def init_scheduler():
    load_schedulers()
    scheduler.start()
