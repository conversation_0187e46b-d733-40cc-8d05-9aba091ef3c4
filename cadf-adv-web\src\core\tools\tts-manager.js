/**
 * TTSManager 类用于管理文本转语音 (TTS) 功能。
 *
 * 使用方法：
 * 1. 导入 ttsManager 实例：
 *    import { ttsManager } from '@/core/tools/tts-manager';
 *
 * 2. 播放文本的语音：
 *    try {
 *      await ttsManager.playTTS('要转换为语音的文本');
 *      // 播放成功
 *    } catch (error) {
 *      // 播放失败，处理错误
 *      console.error(error);
 *    }
 *
 * 3. 停止当前正在播放的语音：
 *    try {
 *      await ttsManager.stopPlayTTS();
 *      // 停止成功
 *    } catch (error) {
 *      // 停止失败，处理错误
 *      console.error(error);
 *    }
 */
import api from "@/core/api/api";
import {audioPlayer} from "@/core/tools/audio-player";

class TTSManager {

    async stopPlayTTS() {
        await audioPlayer.stopAudio();
    }

    async playTTS(text) {
        if (!text) return;
        await this.stopPlayTTS()
        const response = await api({
            resource: 'tts',
            method_name: 'long_text_to_voice',
            text: text,
        });

        if (response.status === 'success' && response.url) {
            await audioPlayer.playAudio(response.url);
        } else {
            throw new Error(response.error_msg || '语音转换失败');
        }
    }
}

export const ttsManager = new TTSManager();