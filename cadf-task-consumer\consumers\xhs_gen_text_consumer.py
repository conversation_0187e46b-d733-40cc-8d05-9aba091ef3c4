from agent.standard_agents.xhs_text_generator import generate_xhs_text, XhsOutput
from config.config import XHS_TEXT_GEN_SET
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from repository.models import AiGeneratedMaterial, Product, AiBasicMaterial


@consume_redis_set(redis_key=XHS_TEXT_GEN_SET, num_threads=1)
def handle_task(message_data):
    """
    处理小红书文本生成任务，简化版。
    """
    ai_generated_material_id = message_data
    olog.info(f"开始处理小红书文本生成任务: {ai_generated_material_id}")
    try:
        gen_material = AiGeneratedMaterial.objects.get(id=ai_generated_material_id)

        # 状态检查
        if gen_material.text_generation_status in ['已完成', '失败']:
            olog.warning(f"任务 {ai_generated_material_id} 已处于最终状态 {gen_material.text_generation_status}，跳过处理。")
            return
        if gen_material.text_generation_status != '生成中':
            gen_material.text_generation_status = '生成中'
            gen_material.save()

        # 获取产品信息
        product = Product.objects.get(id=gen_material.product_id)
        product_info = product.description

        # 获取对标素材信息
        basic_material = AiBasicMaterial.objects.get(id=gen_material.material_id)
        benchmark_title = basic_material.title
        benchmark_content = basic_material.content

        # 直接调用生成逻辑，无需前置校验
        olog.info(f"为 AiGeneratedMaterial {ai_generated_material_id} 调用文本生成...")
        generated_result = generate_xhs_text(
            product_info=product_info,
            benchmark_title=benchmark_title,
            benchmark_content=benchmark_content
        )

        # 更新结果
        gen_material.text_generation_status = '已完成'
        if generated_result.title is not None:
            gen_material.title = generated_result.title
        if generated_result.content is not None:
            gen_material.content = generated_result.content
        gen_material.save()
        olog.info(f"成功完成 AiGeneratedMaterial {ai_generated_material_id} 的文本生成。")
        return
    except Exception as e:
        olog.exception(f"小红书文本生成任务 {ai_generated_material_id} 处理失败: {e}")
        gen_material = AiGeneratedMaterial.objects.get(id=ai_generated_material_id)
        gen_material.text_generation_status = '失败'
        gen_material.text_generation_fail_reason = str(e)
        gen_material.save()
