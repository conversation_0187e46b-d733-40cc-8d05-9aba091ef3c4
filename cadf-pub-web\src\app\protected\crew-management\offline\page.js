"use client";

import {useState, useEffect, useCallback} from 'react';
import {Box, Button, Card, CardContent, Chip, IconButton, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme, CircularProgress, Alert} from '@mui/material';
import {ArrowLeft, Search, Clock} from 'lucide-react';
import {useRouter} from 'next/navigation';
import { crewManagementApi } from '@/api/crew-management-api';

export default function OfflineCrewMembers() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    
    const [crewMembers, setCrewMembers] = useState([]);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const rowsPerPage = 10;

    const fetchCrewMembers = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await crewManagementApi.getCrewList('离线', page, rowsPerPage);
            if (response && response.crew) {
                setCrewMembers(response.crew);
                setTotalCount(response.total_count || 0);
            } else {
                setCrewMembers([]);
                setTotalCount(0);
            }
        } catch (err) {
            console.error("获取离线舰员失败:", err);
            setError(err.message || '加载数据时发生错误');
            setCrewMembers([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    }, [page, rowsPerPage]);

    useEffect(() => {
        fetchCrewMembers();
    }, [page, rowsPerPage]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const getAccountStatusText = (status) => {
        return status === '在线' ? '在线' : (status === '离线' ? '离线' : '封禁');
    };

    const getAccountStatusColor = (status) => {
        switch (status) {
            case '在线':
                return 'success';
            case '离线':
                return 'default';
            default:
                return 'error';
        }
    };

    const getLastLoginDuration = (lastLoginStr) => {
        if (!lastLoginStr) return '未知';
        try {
            const parts = lastLoginStr.split(/[- :]/);
            if (parts.length < 6) return '无效日期';
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1;
            const day = parseInt(parts[2], 10);
            const hour = parseInt(parts[3], 10);
            const minute = parseInt(parts[4], 10);
            const second = parseInt(parts[5], 10);
            
            const lastDate = new Date(year, month, day, hour, minute, second);
            if (isNaN(lastDate.getTime())) {
              return '无效日期';
            }

            const now = new Date();
            const diffTime = now.getTime() - lastDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) return '未来时间?';
            if (diffDays === 0) return '今天';
            if (diffDays === 1) return '昨天';
            if (diffDays < 7) return `${diffDays}天前`;
            if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
            if (diffDays < 365) return `${Math.floor(diffDays / 30)}月前`;
            return `${Math.floor(diffDays / 365)}年前`;
        } catch (e) {
            console.error("解析日期失败:", lastLoginStr, e);
            return '解析错误';
        }
    };

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton 
                    onClick={() => router.back()}
                    sx={{mr: 1}}
                    color="primary"
                >
                    <ArrowLeft />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{
                    fontWeight: 500, 
                    fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}
                }}>
                    离线账户 ({loading ? '...' : totalCount})
                </Typography>
            </Box>

            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        查看所有当前离线的舰员账户列表，了解他们的活跃状态和最后登录时间。
                    </Typography>

                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        mb: 3,
                        gap: 1
                    }}>
                    </Box>

                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                            <CircularProgress />
                        </Box>
                    ) : error ? (
                        <Alert severity="error">{error}</Alert>
                    ) : (
                        <>
                            {isMobile ? (
                                <Box>
                                    {crewMembers.length > 0 ? (
                                        crewMembers.map((member) => (
                                            <Card 
                                                key={member.id_}
                                                sx={{
                                                    mb: 2,
                                                    borderRadius: 2,
                                                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                                                    transition: 'transform 0.2s ease, box-shadow 0.3s ease',
                                                    '&:hover': {
                                                        transform: 'translateY(-2px)',
                                                        boxShadow: '0 6px 16px rgba(0,0,0,0.08)'
                                                    },
                                                    overflow: 'hidden',
                                                    border: '1px solid rgba(0,0,0,0.08)'
                                                }}
                                            >
                                                <Box sx={{
                                                    p: 0.5,
                                                    background: theme.palette.grey[400],
                                                    height: 4
                                                }} />
                                                <CardContent sx={{p: 2}}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        mb: 1.5
                                                    }}>
                                                        <Typography 
                                                            variant="subtitle1" 
                                                            sx={{
                                                                fontWeight: 600,
                                                                fontSize: '1.1rem',
                                                                color: theme.palette.text.primary
                                                            }}
                                                        >
                                                            {member.user_account}
                                                        </Typography>
                                                        <Chip
                                                            label={getAccountStatusText(member.login_status)}
                                                            color={getAccountStatusColor(member.login_status)}
                                                            size="small"
                                                            sx={{
                                                                borderRadius: '4px',
                                                                fontWeight: 500,
                                                                fontSize: '0.7rem'
                                                            }}
                                                        />
                                                    </Box>
                                                    
                                                    <Box sx={{
                                                        display: 'flex', 
                                                        flexDirection: 'column', 
                                                        gap: 1.2,
                                                        background: 'rgba(0,0,0,0.02)',
                                                        p: 1.5,
                                                        borderRadius: 1.5,
                                                        mb: 1.5
                                                    }}>
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center'
                                                        }}>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.secondary,
                                                                fontWeight: 500,
                                                                minWidth: '80px'
                                                            }}>
                                                                平台账号:
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.primary,
                                                                ml: 1
                                                            }}>
                                                                {member.platform_account || '未设置'}
                                                            </Typography>
                                                        </Box>
                                                        
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center'
                                                        }}>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.secondary,
                                                                fontWeight: 500,
                                                                minWidth: '80px'
                                                            }}>
                                                                最后登录:
                                                            </Typography>
                                                            <Box sx={{
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                ml: 1
                                                            }}>
                                                                <Clock size={14} style={{marginRight: '4px'}} color={theme.palette.text.secondary}/>
                                                                <Typography variant="body2" sx={{
                                                                    color: theme.palette.text.primary
                                                                }}>
                                                                    {member.last_login || '未知'}
                                                                </Typography>
                                                            </Box>
                                                        </Box>
                                                    </Box>
                                                    
                                                    <Typography 
                                                        variant="caption" 
                                                        sx={{
                                                            display: 'inline-block',
                                                            color: theme.palette.text.secondary,
                                                            background: theme.palette.grey[100],
                                                            py: 0.5,
                                                            px: 1,
                                                            borderRadius: 5,
                                                            fontWeight: 500
                                                        }}
                                                    >
                                                        {getLastLoginDuration(member.last_login)} 前登录
                                                    </Typography>
                                                </CardContent>
                                            </Card>
                                        ))
                                    ) : (
                                        <Card variant="outlined" sx={{
                                            p: 3, 
                                            textAlign: 'center',
                                            borderRadius: 2,
                                            borderStyle: 'dashed',
                                            background: 'rgba(0,0,0,0.02)'
                                        }}>
                                            <Typography>没有找到匹配的离线账户</Typography>
                                        </Card>
                                    )}
                                </Box>
                            ) : (
                                <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>用户账号</TableCell>
                                                <TableCell>平台账号</TableCell>
                                                <TableCell>账号状态</TableCell>
                                                <TableCell>最后登录时间</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {crewMembers.length > 0 ? (
                                                crewMembers.map((member) => (
                                                    <TableRow key={member.id_} hover>
                                                        <TableCell>{member.user_account}</TableCell>
                                                        <TableCell>{member.platform_account || '未设置'}</TableCell>
                                                        <TableCell>
                                                            <Chip
                                                                label={getAccountStatusText(member.login_status)}
                                                                color={getAccountStatusColor(member.login_status)}
                                                                size="small"
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                                <Clock size={16} color={theme.palette.text.secondary} style={{marginRight: '8px'}} />
                                                                {member.last_login || '未知'}
                                                                {member.last_login && (
                                                                    <Typography variant="caption" sx={{ml: 1, color: 'text.secondary'}}>
                                                                        ({getLastLoginDuration(member.last_login)})
                                                                    </Typography>
                                                                )}
                                                            </Box>
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <TableRow>
                                                    <TableCell colSpan={4} align="center">
                                                        没有找到匹配的离线账户
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            )}

                            {totalCount > rowsPerPage && (
                                <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                                    <Pagination
                                        count={Math.ceil(totalCount / rowsPerPage)}
                                        page={page}
                                        onChange={handlePageChange}
                                        color="primary"
                                        size={isMobile ? "small" : "medium"}
                                        disabled={loading}
                                    />
                                </Stack>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
} 