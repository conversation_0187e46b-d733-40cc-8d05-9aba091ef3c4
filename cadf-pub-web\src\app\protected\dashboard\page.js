"use client";

import {Ava<PERSON>, Box, <PERSON><PERSON>, Card, CardContent, Container, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Grid, LinearProgress, Paper, Stack, TextField, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {Anchor, Award, BookOpen, DollarSign, Lightbulb, ListChecks, Star, TrendingUp, UserCheck, Users} from "lucide-react";
import {useRouter} from 'next/navigation';
import {BarChart} from '@mui/x-charts';
import {useState} from 'react';
import { crewManagementApi } from '@/api/crew-management-api'; // 导入 API
import { useDispatch } from 'react-redux'; // 导入 useDispatch
import { addAlert } from '@/core/components/redux/alert-slice'; // 只导入 addAlert
import { AlertType } from '@/core/components/alert'; // 从正确路径导入 AlertType

// 模拟数据 - 等级与收益
const levelData = [
    {level: 1, dailyIncome: 50, monthlyIncome: 1500},
    {level: 2, dailyIncome: 100, monthlyIncome: 3000},
    {level: 3, dailyIncome: 200, monthlyIncome: 6000},
    {level: 4, dailyIncome: 350, monthlyIncome: 10500},
    {level: 5, dailyIncome: 600, monthlyIncome: 18000},
];

// 用户当前等级
const currentUserLevel = 2;
const nextLevelProgress = 65; // 百分比

export default function Dashboard() {
    const theme = useTheme();
    const router = useRouter();
    const dispatch = useDispatch(); // 获取 dispatch 函数
    const [openFleetDialog, setOpenFleetDialog] = useState(false);
    const [fleetCode, setFleetCode] = useState('');

    const handleOpenFleetDialog = () => {
        setOpenFleetDialog(true);
    };

    const handleCloseFleetDialog = () => {
        setOpenFleetDialog(false);
        setFleetCode('');
    };

    const handleJoinFleet = async () => { // 修改为 async 函数
        // 这里可以添加加入舰队的逻辑
        console.log('尝试加入舰队代码:', fleetCode);
        try {
            const response = await crewManagementApi.joinFleet(fleetCode.trim());
            console.log('加入舰队成功:', response);
            dispatch(addAlert({ type: AlertType.SUCCESS, message: response.message || '成功加入舰队！' })); // 使用 Alert 组件
            handleCloseFleetDialog(); // 关闭对话框
            // 可以根据需要添加其他操作，例如刷新页面或更新状态
        } catch (error) {
            console.error('加入舰队失败:', error);
            dispatch(addAlert({ type: AlertType.ERROR, message: error.message || '加入舰队失败，请检查代码或联系管理员。' })); // 使用 Alert 组件
            // 保持对话框打开，让用户可以修改代码
        }
    };

    // 三个主要功能区域的数据
    const mainFeatures = [
        {
            title: "我的收益",
            description: "查看您的收益情况和支付记录",
            icon: <DollarSign size={32}/>,
            color: theme.palette.primary.main,
            path: "/protected/revenue"
        },
        {
            title: "任务中心",
            description: "浏览和接受新任务，查看进行中的任务",
            icon: <ListChecks size={32}/>,
            color: theme.palette.success.main,
            path: "/protected/tasks/account"
        },
        {
            title: "账号管理",
            description: "管理您的所有社交媒体账号",
            icon: <Users size={32}/>,
            color: theme.palette.info.main,
            path: "/protected/accounts"
        }
    ];

    // 推荐建议数据
    const recommendations = [
        {
            title: "完善您的账号信息",
            description: "增加更多账号详情可以提高任务匹配率",
            icon: <UserCheck size={24}/>
        },
        {
            title: "尝试新的任务类型",
            description: "多样化的内容创作可以增加您的收益",
            icon: <TrendingUp size={24}/>
        },
        {
            title: "定期更新您的作品集",
            description: "保持活跃度可以获得更多任务机会",
            icon: <Lightbulb size={24}/>
        }
    ];

    // 账号画像数据（示例数据）
    const userProfile = {
        completionRate: 85,
        activeAccounts: 3,
        accountTypes: ["小红书", "抖音", "微博"],
        strengths: ["美妆", "生活方式", "旅行"],
        taskPreference: "产品测评"
    };

    // 攻略教程数据
    const tutorials = [
        {
            title: "如何提高任务完成率",
            image: "https://placehold.co/300x200/e3f2fd/1565c0?text=TaskCompletion",
            description: "学习高效完成任务的技巧和方法"
        },
        {
            title: "社交媒体内容创作指南",
            image: "https://placehold.co/300x200/e8f5e9/2e7d32?text=ContentCreation",
            description: "掌握吸引人的内容创作技巧"
        },
        {
            title: "新手指引：平台使用教程",
            image: "https://placehold.co/300x200/fff3e0/e65100?text=BeginnerGuide",
            description: "从零开始了解平台功能与操作方法"
        }
    ];

    return (
        <Box sx={{py: 4}}>
            <Container maxWidth="xl">
                {/* 顶部标题 */}
                <Typography variant="h4" component="h1" gutterBottom sx={{fontWeight: 600, mb: 4}}>
                    控制台
                </Typography>

                {/* 三个主要功能卡片 */}
                <Grid container spacing={3} sx={{mb: 6}}>
                    {mainFeatures.map((feature, index) => (
                        <Grid item xs={12} md={4} key={index}>
                            <Card
                                sx={{
                                    height: '100%',
                                    cursor: 'pointer',
                                    transition: 'transform 0.2s',
                                    '&:hover': {
                                        transform: 'translateY(-4px)',
                                        boxShadow: theme.shadows[4]
                                    }
                                }}
                                onClick={feature.onClick || (() => router.push(feature.path))}
                            >
                                <CardContent sx={{p: 3}}>
                                    <Box sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 2
                                    }}>
                                        <Avatar
                                            sx={{
                                                bgcolor: feature.color,
                                                width: 56,
                                                height: 56,
                                                mr: 2
                                            }}
                                        >
                                            {feature.icon}
                                        </Avatar>
                                        <Typography variant="h5" component="div" sx={{fontWeight: 600}}>
                                            {feature.title}
                                        </Typography>
                                    </Box>
                                    <Typography variant="body1" color="text.secondary">
                                        {feature.description}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>

                {/* 等级预估收益显示 */}
                <Card sx={{mb: 6}}>
                    <CardContent sx={{p: {xs: 1, sm: 2, md: 3}}}>
                        <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mb: {xs: 2, sm: 3}
                        }}>
                            <Award size={20} style={{marginRight: 8}}/>
                            <Typography variant="h5" sx={{fontSize: {xs: '1rem', sm: '1.25rem', md: '1.5rem'}}}>
                                等级预估收益
                            </Typography>
                        </Box>

                        <Divider sx={{mb: {xs: 2, sm: 3}}}/>

                        <Grid container spacing={{xs: 1, sm: 2, md: 3}}>
                            <Grid item xs={12} md={6} lg={4}>
                                <Paper elevation={1} sx={{
                                    p: {xs: 1.5, sm: 2},
                                    height: '100%',
                                    borderRadius: {xs: 2, sm: 3}
                                }}>
                                    <Typography variant="h6" sx={{
                                        mb: {xs: 1.5, sm: 2},
                                        display: 'flex',
                                        alignItems: 'center',
                                        fontSize: {xs: '0.875rem', sm: '1rem', md: '1.25rem'}
                                    }}>
                                        <Star size={16} style={{marginRight: 8}}/>
                                        当前等级
                                    </Typography>

                                    <Box sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: {xs: 2, sm: 3}
                                    }}>
                                        <Typography variant="h3" color="primary.main" sx={{
                                            fontWeight: 'bold',
                                            fontSize: {xs: '2rem', sm: '2.5rem', md: '3rem'}
                                        }}>
                                            {currentUserLevel}
                                        </Typography>
                                        <Typography variant="body1" sx={{
                                            ml: 1,
                                            fontSize: {xs: '0.875rem', sm: '1rem', md: '1.25rem'}
                                        }}>
                                            级创作者
                                        </Typography>
                                    </Box>

                                    <Typography variant="body2" sx={{
                                        mb: 1,
                                        fontSize: {xs: '0.75rem', sm: '0.875rem', md: '1rem'}
                                    }}>
                                        距离下一等级进度:
                                    </Typography>
                                    <Box sx={{mb: 1}}>
                                        <LinearProgress
                                            variant="determinate"
                                            value={nextLevelProgress}
                                            sx={{height: 4, borderRadius: 2}}
                                        />
                                    </Box>
                                    <Box sx={{display: 'flex', justifyContent: 'space-between'}}>
                                        <Typography variant="caption" color="text.secondary" sx={{
                                            fontSize: {xs: '0.75rem', sm: '0.875rem'}
                                        }}>
                                            当前: {currentUserLevel}级
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{
                                            fontSize: {xs: '0.75rem', sm: '0.875rem'}
                                        }}>
                                            下一级: {currentUserLevel + 1}级
                                        </Typography>
                                    </Box>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={6} lg={8}>
                                <Paper elevation={1} sx={{
                                    p: {xs: 1.5, sm: 2},
                                    height: '100%',
                                    borderRadius: {xs: 2, sm: 3}
                                }}>
                                    <Typography variant="h6" sx={{
                                        mb: {xs: 1.5, sm: 2},
                                        display: 'flex',
                                        alignItems: 'center',
                                        fontSize: {xs: '0.875rem', sm: '1rem', md: '1.25rem'}
                                    }}>
                                        <TrendingUp size={16} style={{marginRight: 8}}/>
                                        等级收益对比
                                    </Typography>

                                    <Box sx={{
                                        width: '100%',
                                        height: {xs: 200, sm: 250},
                                        overflowX: 'auto',
                                        '& .MuiChartsAxis-root': {
                                            fontSize: {xs: '0.75rem', sm: '0.875rem'}
                                        }
                                    }}>
                                        <BarChart
                                            series={[
                                                {data: levelData.map(item => item.dailyIncome), label: '日均收益(元)'},
                                                {data: levelData.map(item => item.monthlyIncome / 100), label: '月均收益(百元)'}
                                            ]}
                                            height={250}
                                            xAxis={[{
                                                data: levelData.map(item => `${item.level}级`),
                                                scaleType: 'band',
                                            }]}
                                            colors={['#3f51b5', '#ff9800']}
                                        />
                                    </Box>

                                    <Typography variant="caption" color="text.secondary" sx={{
                                        display: 'block',
                                        textAlign: 'center',
                                        mt: 1,
                                        fontSize: {xs: '0.75rem', sm: '0.875rem'}
                                    }}>
                                        * 数据仅供参考，实际收益会根据任务完成质量和数量有所浮动
                                    </Typography>
                                </Paper>
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                {/* 底部内容区域 */}
                <Grid container spacing={4}>
                    {/* 舰队信息 */}
                    <Grid item xs={12} mb={4}>
                        <Paper sx={{p: 3, height: '100%', overflow: 'hidden', position: 'relative'}}>
                            <Box sx={{
                                position: 'absolute',
                                right: -20,
                                top: -20,
                                opacity: 0.05,
                                transform: 'rotate(15deg)'
                            }}>
                                <Anchor size={150}/>
                            </Box>
                            <Typography variant="h6" component="h2" gutterBottom sx={{fontWeight: 600, display: 'flex', alignItems: 'center'}}>
                                <Anchor size={20} style={{marginRight: 8}}/>
                                舰队中心
                            </Typography>
                            <Divider sx={{mb: 2}}/>
                            <Grid container spacing={2} alignItems="center">
                                <Grid item xs={12} md={7}>
                                    <Typography variant="body1" paragraph>
                                        加入舰队，与其他创作者一起合作完成更高价值的任务。舰队合作可以提高任务完成效率和质量，获得更多收益！
                                    </Typography>
                                    <Box sx={{display: 'flex', gap: 2, mt: 2}}>
                                        <Button
                                            variant="contained"
                                            color="warning"
                                            startIcon={<Anchor size={16}/>}
                                            onClick={handleOpenFleetDialog}
                                        >
                                            输入舰队码加入
                                        </Button>
                                    </Box>
                                </Grid>
                                <Grid item xs={12} md={5}>
                                    <Paper elevation={2} sx={{p: 2, bgcolor: 'background.default', borderRadius: 2}}>
                                        <Typography variant="subtitle2" gutterBottom>舰队优势:</Typography>
                                        <Stack spacing={1}>
                                            <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                                <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                                任务收益提升15%-30%
                                            </Typography>
                                            <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                                <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                                获得专属团队任务
                                            </Typography>
                                            <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                                <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                                共享创作资源与素材
                                            </Typography>
                                        </Stack>
                                    </Paper>
                                </Grid>
                            </Grid>
                        </Paper>
                    </Grid>

                    {/* 推荐建议 */}
                    <Grid item xs={12} md={4}>
                        <Paper sx={{p: 3, height: '100%'}}>
                            <Typography variant="h6" component="h2" gutterBottom sx={{fontWeight: 600, display: 'flex', alignItems: 'center'}}>
                                <Lightbulb size={20} style={{marginRight: 8}}/>
                                推荐建议
                            </Typography>
                            <Divider sx={{mb: 2}}/>
                            <Stack spacing={2}>
                                {recommendations.map((rec, index) => (
                                    <Box key={index} sx={{display: 'flex', alignItems: 'flex-start', p: 1}}>
                                        <Box sx={{mr: 1.5, color: theme.palette.primary.main}}>
                                            {rec.icon}
                                        </Box>
                                        <Box>
                                            <Typography variant="subtitle2" sx={{fontWeight: 600}}>
                                                {rec.title}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {rec.description}
                                            </Typography>
                                        </Box>
                                    </Box>
                                ))}
                            </Stack>
                        </Paper>
                    </Grid>

                    {/* 账号画像 */}
                    <Grid item xs={12} md={4}>
                        <Paper sx={{p: 3, height: '100%'}}>
                            <Typography variant="h6" component="h2" gutterBottom sx={{fontWeight: 600, display: 'flex', alignItems: 'center'}}>
                                <UserCheck size={20} style={{marginRight: 8}}/>
                                账号画像
                            </Typography>
                            <Divider sx={{mb: 2}}/>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">任务完成率</Typography>
                                    <Typography variant="h6" sx={{color: theme.palette.success.main, fontWeight: 600}}>
                                        85%
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">活跃账号数</Typography>
                                    <Typography variant="h6" sx={{color: theme.palette.primary.main, fontWeight: 600}}>
                                        3
                                    </Typography>
                                </Grid>
                                <Grid item xs={12}>
                                    <Typography variant="body2" color="text.secondary" sx={{mt: 1}}>账号类型</Typography>
                                    <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5}}>
                                        {['小红书', '抖音', '微博'].map((type, index) => (
                                            <Typography
                                                key={index}
                                                variant="body2"
                                                sx={{
                                                    bgcolor: theme.palette.background.default,
                                                    px: 1.5,
                                                    py: 0.5,
                                                    borderRadius: 1,
                                                    fontWeight: 500
                                                }}
                                            >
                                                {type}
                                            </Typography>
                                        ))}
                                    </Box>
                                </Grid>
                                <Grid item xs={12}>
                                    <Typography variant="body2" color="text.secondary">内容优势</Typography>
                                    <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5}}>
                                        {['美妆', '生活方式', '旅行'].map((strength, index) => (
                                            <Typography
                                                key={index}
                                                variant="body2"
                                                sx={{
                                                    bgcolor: theme.palette.info.light,
                                                    color: theme.palette.info.contrastText,
                                                    px: 1.5,
                                                    py: 0.5,
                                                    borderRadius: 1,
                                                    fontWeight: 500
                                                }}
                                            >
                                                {strength}
                                            </Typography>
                                        ))}
                                    </Box>
                                </Grid>
                            </Grid>
                        </Paper>
                    </Grid>

                    {/* 攻略教程 */}
                    <Grid item xs={12} md={4}>
                        <Paper sx={{p: 3, height: '100%'}}>
                            <Typography variant="h6" component="h2" gutterBottom sx={{fontWeight: 600, display: 'flex', alignItems: 'center'}}>
                                <BookOpen size={20} style={{marginRight: 8}}/>
                                攻略教程
                            </Typography>
                            <Divider sx={{mb: 2}}/>
                            <Stack spacing={2}>
                                {[
                                    {
                                        title: "如何提高任务完成率",
                                        image: "https://placehold.co/300x200/e3f2fd/1565c0?text=TaskCompletion",
                                        description: "学习高效完成任务的技巧和方法"
                                    },
                                    {
                                        title: "社交媒体内容创作指南",
                                        image: "https://placehold.co/300x200/e8f5e9/2e7d32?text=ContentCreation",
                                        description: "掌握吸引人的内容创作技巧"
                                    },
                                    {
                                        title: "新手指引：平台使用教程",
                                        image: "https://placehold.co/300x200/fff3e0/e65100?text=BeginnerGuide",
                                        description: "从零开始了解平台功能与操作方法"
                                    }
                                ].map((tutorial, index) => (
                                    <Box
                                        key={index}
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            p: 1,
                                            borderRadius: 1,
                                            '&:hover': {
                                                bgcolor: theme.palette.background.default,
                                                cursor: 'pointer'
                                            }
                                        }}
                                    >
                                        <Box
                                            component="img"
                                            src={tutorial.image}
                                            alt={tutorial.title}
                                            sx={{
                                                width: 80,
                                                height: 60,
                                                borderRadius: 1,
                                                mr: 1.5,
                                                objectFit: 'cover'
                                            }}
                                        />
                                        <Box>
                                            <Typography variant="subtitle2" sx={{fontWeight: 600}}>
                                                {tutorial.title}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {tutorial.description}
                                            </Typography>
                                        </Box>
                                    </Box>
                                ))}
                            </Stack>
                        </Paper>
                    </Grid>
                </Grid>
            </Container>

            {/* 舰队加入对话框 */}
            <Dialog
                open={openFleetDialog}
                onClose={handleCloseFleetDialog}
                maxWidth="xs"
                fullWidth
            >
                <DialogTitle sx={{
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    display: 'flex',
                    alignItems: 'center'
                }}>
                    <Anchor size={20} style={{marginRight: 10}}/>
                    加入舰队
                </DialogTitle>
                <DialogContent sx={{pt: 3, pb: 1}}>
                    <Typography variant="body2" paragraph>
                        请输入邀请人提供的舰队代码，加入后可以与团队成员一起合作完成任务，获取更高收益。
                    </Typography>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="舰队代码"
                        type="text"
                        fullWidth
                        variant="outlined"
                        value={fleetCode}
                        onChange={(e) => setFleetCode(e.target.value)}
                        placeholder="例如: FLEET-123456"
                        sx={{mb: 2}}
                    />
                </DialogContent>
                <DialogActions sx={{px: 3, py: 2}}>
                    <Button onClick={handleCloseFleetDialog} color="inherit">
                        取消
                    </Button>
                    <Button
                        onClick={handleJoinFleet}
                        variant="contained"
                        color="warning"
                        disabled={!fleetCode.trim()}
                    >
                        加入舰队
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
} 