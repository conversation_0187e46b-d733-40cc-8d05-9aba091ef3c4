"use client"

import {<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON>, Typo<PERSON>, useMediaQuery, useTheme} from '@mui/material';
import {Bar<PERSON>hart, Download, Monitor, Sparkles, Zap} from 'lucide-react';
import Cookies from "js-cookie";
import {useRouter} from 'next/navigation';
import {useEffect} from 'react';
import {DASHBOARD_PATH, DOWNLOAD_PATH} from "@/config";

export default function Home() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();

    useEffect(() => {
        const token = Cookies.get("access_token");
        if (token) {
            router.push(DASHBOARD_PATH);
        }
    }, [router]);

    return (
        <Box sx={{minHeight: '100vh', bgcolor: 'background.default'}}>
            {/* 英雄区域 */}
            <Box
                sx={{
                    bgcolor: 'primary.light',
                    py: {xs: 8, sm: 10, md: 16},
                    position: 'relative',
                    overflow: 'hidden'
                }}
            >
                <Container maxWidth="lg">
                    <Grid container spacing={{xs: 2, md: 4}} alignItems="center">
                        <Grid item xs={12} md={6}>
                            <Box sx={{
                                position: 'relative',
                                zIndex: 2,
                                textAlign: {xs: 'center', md: 'left'}
                            }}>
                                <Typography
                                    variant="h1"
                                    component="h1"
                                    fontWeight="bold"
                                    gutterBottom
                                    sx={{
                                        fontSize: {xs: '2rem', sm: '2.5rem', md: '3.5rem'},
                                        color: 'primary.contrastText'
                                    }}
                                >
                                    Cyber AD
                                </Typography>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        mb: {xs: 3, md: 4},
                                        fontSize: {xs: '1rem', sm: '1.25rem'},
                                        maxWidth: {xs: '100%', md: '600px'},
                                        mx: {xs: 'auto', md: 0},
                                        color: 'primary.contrastText'
                                    }}
                                >
                                    人工智能驱动的广告平台，让您的创意无限可能
                                </Typography>
                                <Stack
                                    direction={{xs: 'column', sm: 'row'}}
                                    spacing={{xs: 2, sm: 2}}
                                    sx={{
                                        justifyContent: {xs: 'center', md: 'flex-start'},
                                        width: '100%'
                                    }}
                                >
                                    <Button
                                        variant="contained"
                                        size={isMobile ? "medium" : "large"}
                                        startIcon={<Monitor/>}
                                        href="/public/login"
                                        fullWidth={isMobile}
                                        sx={{
                                            px: {xs: 2, sm: 4},
                                            py: {xs: 1, sm: 1.5},
                                            bgcolor: 'background.paper',
                                            color: 'primary.main',
                                            '&:hover': {
                                                bgcolor: 'background.paper',
                                                opacity: 0.9
                                            }
                                        }}
                                    >
                                        访问地址
                                    </Button>
                                    <Button
                                        variant="outlined"
                                        size={isMobile ? "medium" : "large"}
                                        startIcon={<Download/>}
                                        fullWidth={isMobile}
                                        href={DOWNLOAD_PATH}
                                        sx={{
                                            px: {xs: 2, sm: 4},
                                            py: {xs: 1, sm: 1.5},
                                            borderColor: 'background.paper',
                                            color: 'background.paper',
                                            '&:hover': {
                                                borderColor: 'background.paper',
                                                opacity: 0.9
                                            }
                                        }}
                                    >
                                        下载客户端程序
                                    </Button>
                                </Stack>
                            </Box>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Box
                                sx={{
                                    display: {xs: 'block', md: 'block'},
                                    position: 'relative',
                                    zIndex: 1,
                                    mt: {xs: 4, md: 0},
                                    textAlign: 'center'
                                }}
                            >
                                <img
                                    src="https://placehold.co/600x400/e3f2fd/0d47a1?text=AI-Platform"
                                    alt="Cyber AD 平台"
                                    style={{
                                        width: '100%',
                                        maxWidth: '450px',
                                        borderRadius: '16px',
                                        boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
                                    }}
                                />
                            </Box>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 功能特点区域 */}
            <Container maxWidth="lg" sx={{py: {xs: 6, md: 12}}}>
                <Box sx={{textAlign: 'center', mb: {xs: 5, md: 8}}}>
                    <Typography
                        variant="h3"
                        component="h2"
                        fontWeight="bold"
                        gutterBottom
                        sx={{fontSize: {xs: '1.75rem', sm: '2rem', md: '2.5rem'}}}
                    >
                        为什么选择 Cyber AD
                    </Typography>
                    <Typography
                        variant="h6"
                        color="text.secondary"
                        sx={{
                            maxWidth: '800px',
                            mx: 'auto',
                            fontSize: {xs: '0.9rem', sm: '1rem', md: '1.25rem'}
                        }}
                    >
                        我们的平台结合先进人工智能技术，为您的广告营销带来全新体验
                    </Typography>
                </Box>

                <Grid container spacing={{xs: 2, md: 4}}>
                    <Grid item xs={12} sm={6} md={4}>
                        <Card
                            elevation={0}
                            sx={{
                                height: '100%',
                                borderRadius: {xs: 3, md: 4},
                                transition: 'transform 0.3s',
                                '&:hover': {
                                    transform: 'translateY(-8px)'
                                }
                            }}
                        >
                            <CardContent sx={{p: {xs: 3, md: 4}}}>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: {xs: 60, md: 80},
                                        height: {xs: 60, md: 80},
                                        borderRadius: '50%',
                                        bgcolor: 'primary.light',
                                        mb: 3,
                                        mx: 'auto'
                                    }}
                                >
                                    <Sparkles size={isMobile ? 30 : 40} color="#0d47a1"/>
                                </Box>
                                <Typography
                                    variant="h5"
                                    sx={{fontSize: {xs: '1.25rem', md: '1.5rem'}}}
                                    align="center"
                                    gutterBottom
                                >
                                    智能广告创建
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    align="center"
                                    sx={{fontSize: {xs: '0.875rem', md: '1rem'}}}
                                >
                                    利用先进的人工智能技术，自动生成吸引人的广告内容和图像，提高转化率和投资回报率。
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                        <Card
                            elevation={0}
                            sx={{
                                height: '100%',
                                borderRadius: {xs: 3, md: 4},
                                transition: 'transform 0.3s',
                                '&:hover': {
                                    transform: 'translateY(-8px)'
                                }
                            }}
                        >
                            <CardContent sx={{p: {xs: 3, md: 4}}}>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: {xs: 60, md: 80},
                                        height: {xs: 60, md: 80},
                                        borderRadius: '50%',
                                        bgcolor: 'primary.light',
                                        mb: 3,
                                        mx: 'auto'
                                    }}
                                >
                                    <BarChart size={isMobile ? 30 : 40} color="#0d47a1"/>
                                </Box>
                                <Typography
                                    variant="h5"
                                    sx={{fontSize: {xs: '1.25rem', md: '1.5rem'}}}
                                    align="center"
                                    gutterBottom
                                >
                                    精准数据分析
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    align="center"
                                    sx={{fontSize: {xs: '0.875rem', md: '1rem'}}}
                                >
                                    通过实时数据分析和智能洞察，帮助您优化广告策略，精准定位目标受众，最大化广告效果。
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4} sx={{mx: {xs: 'auto', sm: 0}, width: {xs: '100%', sm: 'auto'}}}>
                        <Card
                            elevation={0}
                            sx={{
                                height: '100%',
                                borderRadius: {xs: 3, md: 4},
                                transition: 'transform 0.3s',
                                '&:hover': {
                                    transform: 'translateY(-8px)'
                                }
                            }}
                        >
                            <CardContent sx={{p: {xs: 3, md: 4}}}>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: {xs: 60, md: 80},
                                        height: {xs: 60, md: 80},
                                        borderRadius: '50%',
                                        bgcolor: 'primary.light',
                                        mb: 3,
                                        mx: 'auto'
                                    }}
                                >
                                    <Zap size={isMobile ? 30 : 40} color="#0d47a1"/>
                                </Box>
                                <Typography
                                    variant="h5"
                                    sx={{fontSize: {xs: '1.25rem', md: '1.5rem'}}}
                                    align="center"
                                    gutterBottom
                                >
                                    高效营销自动化
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    align="center"
                                    sx={{fontSize: {xs: '0.875rem', md: '1rem'}}}
                                >
                                    自动化广告投放和优化流程，节省时间和资源，让您的营销团队专注于创意和战略。
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </Container>

            {/* 平台优势区域 */}
            <Box sx={{bgcolor: 'grey.50', py: {xs: 6, md: 12}}}>
                <Container maxWidth="lg">
                    <Box sx={{mb: {xs: 4, md: 6}, textAlign: 'center'}}>
                        <Typography
                            variant="h3"
                            component="h2"
                            fontWeight="bold"
                            gutterBottom
                            sx={{fontSize: {xs: '1.75rem', sm: '2rem', md: '2.5rem'}}}
                        >
                            Cyber AD 平台优势
                        </Typography>
                    </Box>

                    <Box
                        sx={{
                            borderRadius: {xs: 3, md: 4},
                            overflow: 'hidden',
                            boxShadow: '0 8px 40px rgba(0,0,0,0.08)'
                        }}
                    >
                        <img
                            src="https://placehold.co/1200x600/e3f2fd/0d47a1?text=Cyber-AD-Platform"
                            alt="Cyber AD平台"
                            style={{width: '100%'}}
                        />
                    </Box>

                    <Grid container spacing={{xs: 3, md: 4}} sx={{mt: {xs: 3, md: 4}}}>
                        <Grid item xs={12} sm={6} md={4}>
                            <Typography
                                variant="h6"
                                gutterBottom
                                color="primary.main"
                                sx={{
                                    fontSize: {xs: '1.1rem', md: '1.25rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                全面的AI能力
                            </Typography>
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    fontSize: {xs: '0.875rem', md: '1rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                我们的平台集成了最前沿的人工智能技术，包括图像生成、内容创作和数据分析，提供全方位的广告解决方案。
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <Typography
                                variant="h6"
                                gutterBottom
                                color="primary.main"
                                sx={{
                                    fontSize: {xs: '1.1rem', md: '1.25rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                简单易用的界面
                            </Typography>
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    fontSize: {xs: '0.875rem', md: '1rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                即使没有技术背景，您也能轻松使用我们的平台。直观的用户界面和智能工作流程让广告创建变得简单。
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} sx={{mx: {xs: 'auto', sm: 0}, maxWidth: {xs: '100%', sm: '50%', md: '33.33%'}}}>
                            <Typography
                                variant="h6"
                                gutterBottom
                                color="primary.main"
                                sx={{
                                    fontSize: {xs: '1.1rem', md: '1.25rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                可衡量的结果
                            </Typography>
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    fontSize: {xs: '0.875rem', md: '1rem'},
                                    textAlign: {xs: 'center', sm: 'left'}
                                }}
                            >
                                通过详细的分析报告和实时数据追踪，清晰了解每个广告的表现，让您的营销决策更加精准。
                            </Typography>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 底部区域 */}
            <Box sx={{bgcolor: 'background.paper', py: {xs: 4, md: 6}}}>
                <Container maxWidth="lg">
                    <Box sx={{textAlign: 'center'}}>
                        <Typography
                            variant="h4"
                            component="div"
                            fontWeight="bold"
                            gutterBottom
                            sx={{fontSize: {xs: '1.5rem', md: '2rem'}}}
                        >
                            Cyber AD
                        </Typography>
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{mb: {xs: 2, md: 3}}}
                        >
                            人工智能驱动的广告解决方案
                        </Typography>
                        <Stack
                            direction={{xs: 'column', sm: 'row'}}
                            spacing={2}
                            justifyContent="center"
                            sx={{mb: {xs: 2, md: 3}}}
                        >
                            <Button
                                variant="contained"
                                size={isMobile ? "medium" : "large"}
                                startIcon={<Monitor/>}
                                href="/public/login"
                                fullWidth={isMobile}
                            >
                                访问地址
                            </Button>
                            <Button
                                variant="outlined"
                                size={isMobile ? "medium" : "large"}
                                startIcon={<Download/>}
                                fullWidth={isMobile}
                                href={DOWNLOAD_PATH}
                            >
                                下载客户端程序
                            </Button>
                        </Stack>
                        <Divider sx={{my: {xs: 2, md: 3}}}/>
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{fontSize: {xs: '0.75rem', md: '0.875rem'}}}
                        >
                            © {new Date().getFullYear()} Cyber AD. 版权所有
                        </Typography>
                    </Box>
                </Container>
            </Box>
        </Box>
    );
}
