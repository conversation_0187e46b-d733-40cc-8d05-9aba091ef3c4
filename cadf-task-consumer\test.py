import requests
from agent.standard_agents.generate_caption_from_image_text import generate_caption_from_image_text
from agent.standard_agents.add_caption_to_image import add_caption_to_image

if __name__ == "__main__":
    # 从URL读取图片为字节流
    image_url = "https://projects-1323741919.cos.ap-guangzhou.myqcloud.com/cadf/pic/20250515153748fh438O.webp?q-sign-algorithm=sha1&q-ak=AKIDPiyNkx0wlV8Yr14ylWRJJIhzAEAtUxKb&q-sign-time=**********%3B1748500136&q-key-time=**********%3B1748500136&q-header-list=host&q-url-param-list=&q-signature=4b06557ef1d4404f0ce52fb9c8258eac2c621a37"  # 替换为你的图片URL
    response = requests.get(image_url)
    image_bytes = response.content
    additional_text = "这是一款名为“BAJI芭集“防脱发洗发水产品。瓶身设计为黑色，外观简洁大方，采用按压式泵头设计，方便使用。瓶身上方印有品牌标志“BAJI芭集”，并配有英文描述“CLEANING AND STRONG HEALTHY HAIR SHAMPOO”，表明该产品具有清洁和强化头发的功效。中文描述为“蓬松强韧健发洗发露”，强调其能够使头发变得强韧且蓬松。此外，瓶身还标注了“STRONG AND HEALTHY HAIRCLEAR AND FLUFFY”，进一步说明该洗发露能够帮助头发保持健康、清爽和蓬松的状态。整体来看，这款洗发露旨在为用户提供一种高效且温和的护发体验，适合日常使用以维护头发的健康与美丽。"
    # 第一步：生成配文
    caption_result = generate_caption_from_image_text(image_bytes, additional_text)
    print("配文生成结果：", caption_result)

    # 处理caption_result为add_caption_to_image所需格式
    if caption_result and caption_result.get('items'):
        captions = []
        for item in caption_result['items']:
            captions.append({
                'title': item.get('title', ''),
                'contents': item.get('contents', [])
            })
        # 第二步：将配文添加到图片
        result_image_bytes = add_caption_to_image(image_bytes, captions)
        # 保存结果图片
        with open('result_image.png', 'wb') as f:
            f.write(result_image_bytes)
        print("图片处理完成，已保存为 result_image.png")
    else:
        print("未生成有效配文，跳过图片处理。")
