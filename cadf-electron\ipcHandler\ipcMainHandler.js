// ipcMainHandler.js
const {ipcMain} = require('electron');

class IpcMainHandler {
    constructor() {
        this.handlers = new Map();
    }

    handle(channel, handler) {
        if (this.handlers.has(channel)) {
            console.warn(`通道 '${channel}' 的处理程序正在被覆盖`);
        }
        this.handlers.set(channel, handler);
        ipcMain.handle(channel, async (event, ...args) => {
            const handler = this.handlers.get(channel);
            if (handler) {
                try {
                    return await handler(...args);
                } catch (error) {
                    console.error(`通道 '${channel}' 的IPC处理程序出错:`, error);
                    throw error;
                }
            } else {
                console.error(`未找到通道 '${channel}' 的处理程序`);
                throw new Error(`未找到通道 '${channel}' 的处理程序`);
            }
        });
    }

    removeHandler(channel) {
        this.handlers.delete(channel);
        ipcMain.removeHandler(channel);
    }
}

module.exports = new IpcMainHandler();
