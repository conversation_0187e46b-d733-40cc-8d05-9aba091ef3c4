import time
from bson import ObjectId

from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import User
from omni.api.exception import MException
from omni.log.log import olog


@register_handler('captain_management')
class CaptainManagementApi:
    
    @auth_required(['admin'])
    def query_all_users(self, data):
        """查询所有用户，用于舰长管理"""
        # 查询所有用户
        users = User.objects().order_by('-create_at')
        users_list = docs_to_dict(users)
        
        # 为每个用户添加角色信息
        for user in users_list:
            if not user.get('roles'):
                user['roles'] = []
            # 判断是否为舰长
            user['is_captain'] = 'captain' in user.get('roles', [])
            # 判断是否可以成为舰长（具有user或creator权限）
            user['can_be_captain'] = any(role in user.get('roles', []) for role in ['user', 'creator'])
        
        olog.info(f"查询到 {len(users_list)} 个用户")
        return {'users': users_list}
    
    @auth_required(['admin'])
    def promote_to_captain(self, data):
        """将用户提升为舰长"""
        target_user_id = data.get('target_user_id')
        
        # 查询用户
        user = User.objects(id=ObjectId(target_user_id)).first()
        if not user:
            raise MException("用户不存在")
        
        # 检查用户是否已经是舰长
        if 'captain' in user.roles:
            raise MException("该用户已经是舰长")
        
        # 检查用户是否具有user或creator权限
        if not any(role in user.roles for role in ['user', 'creator']):
            raise MException("只有具有user或creator权限的用户才能成为舰长")
        
        # 生成唯一的10位大写英文邀请码
        import random
        import string
        code_length = 10
        characters = string.ascii_uppercase
        while True:
            invite_code = ''.join(random.choice(characters) for _ in range(code_length))
            # 检查邀请码是否已存在
            if not User.objects(crew_invite_code=invite_code).first():
                break
        
        # 添加舰长权限并设置邀请码
        user.roles.append('captain')
        user.crew_invite_code = invite_code
        user.save()
        
        olog.info(f"用户 {user.username} 已被提升为舰长，邀请码为: {invite_code}")
        return {'message': '成功授予舰长权限', 'invite_code': invite_code}
    
    @auth_required(['admin'])
    def revoke_captain(self, data):
        """撤销用户的舰长权限"""
        target_user_id = data.get('target_user_id')
        
        # 查询用户
        user = User.objects(id=ObjectId(target_user_id)).first()
        if not user:
            raise MException("用户不存在")
        
        # 检查用户是否是舰长
        if 'captain' not in user.roles:
            raise MException("该用户不是舰长")
        
        # 移除舰长权限
        user.roles = [role for role in user.roles if role != 'captain']
        user.save()
        
        olog.info(f"用户 {user.username} 的舰长权限已被撤销")
        return {'message': '成功撤销舰长权限'}
    
    @auth_required(['admin'])
    def query_captain_list(self, data):
        """查询所有舰长列表"""
        # 查询所有包含captain角色的用户
        captains = User.objects(roles__contains='captain').order_by('-create_at')
        captain_list = docs_to_dict(captains)
        
        olog.info(f"查询到 {len(captain_list)} 个舰长")
        return {'captains': captain_list}
    
    @auth_required(['admin'])
    def query_promotable_users(self, data):
        """查询可以提升为舰长的用户"""
        # 查询具有user或creator权限，但不是舰长的用户
        users = User.objects().order_by('-create_at')
        promotable_users = []
        
        for user in users:
            user_dict = doc_to_dict(user)
            roles = user_dict.get('roles', [])
            
            # 检查是否具有user或creator权限，且不是舰长
            has_user_or_creator = any(role in roles for role in ['user', 'creator'])
            is_not_captain = 'captain' not in roles
            
            if has_user_or_creator and is_not_captain:
                user_dict['can_be_captain'] = True
                promotable_users.append(user_dict)
        
        olog.info(f"查询到 {len(promotable_users)} 个可提升为舰长的用户")
        return {'users': promotable_users}
