import datetime
import time

from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import (
    CaptainDailyRevenue,
    CrewManagement,
    DailyTaskRevenue,
)
from omni.log.log import olog


@register_scheduler(trigger="cron", hour="7", minute="0")
# @register_scheduler(trigger='interval', seconds=10)  # 每10秒执行一次
class CaptainDailyRevenueScheduler(BaseScheduler):
    def run_task(self):
        """
        **目的**: 定时计算并记录所有舰长基于其名下舰员当日未结算任务收益所获得的佣金。

        **算法描述**:
            - **初始化**: 记录任务开始，获取当前日期零点的时间戳 `today_timestamp`。
            - **获取舰长-舰员关系**: 查询 `CrewManagement` 数据库，获取所有舰长与其管理的舰员列表，存储在 `captains` 字典中（`captain_id` -> `[crew_id, ...]`）。
            - **遍历舰长**: 循环处理每一位舰长：
                - 初始化舰长当日总佣金 `total_captain_revenue_today` 为 0。
                - **遍历舰员**: 循环处理该舰长名下的每一位舰员：
                    - **查询舰员日收益**: 查询 `DailyTaskRevenue` 数据库，获取该舰员在 `today_timestamp` 的所有收益记录 `crew_daily_revenues`。
                    - **计算舰员总收益**: 累加 `crew_daily_revenues` 中的 `daily_revenue` 得到 `crew_total_revenue_today`，并记录对应的收益ID到 `processed_revenue_ids`。
                    - **计算并记录舰长佣金**:
                        - 如果 `crew_total_revenue_today` 大于 0：
                            - 计算舰长佣金 `captain_commission` (舰员总收益的 30%)。
                            - 如果 `captain_commission` 大于 0：
                                - **创建/更新舰长收益记录**: 在 `CaptainDailyRevenue` 数据库中，为当前 `captain_id`, `crew_id`, `today_timestamp` 创建或更新一条记录：设置佣金 `daily_revenue` 为 `captain_commission`，关联的任务收益ID列表 `daily_task_revenue_ids` 为 `processed_revenue_ids`，状态 `status` 为 "未结算"，`settled_at` 为 `None`。使用 `upsert=True` 确保记录存在。
                                - 将 `captain_commission` 累加到 `total_captain_revenue_today`。
                - **记录舰长当日总佣金**: 打印当前舰长当日累计获得的总佣金 `total_captain_revenue_today`。
            - **结束**: 记录任务计算完成。
        """
        olog.info("开始计算舰长每日收益...")

        # 获取当天的开始时间戳 (00:00:00)
        today = datetime.date.today()
        start_of_day_dt = datetime.datetime.combine(today, datetime.time.min)
        today_timestamp = int(start_of_day_dt.timestamp())

        # 查找所有舰长-舰员关系
        all_crews = CrewManagement.objects.all()
        captains = {}
        for crew_relation in all_crews:
            captain_id = crew_relation.captain_user_id
            crew_id = crew_relation.crew_user_id
            if captain_id not in captains:
                captains[captain_id] = []
            captains[captain_id].append(crew_id)

        olog.info(f"找到 {len(captains)} 位舰长。")

        # 为每个舰长计算收益
        for captain_id, crew_ids in captains.items():
            olog.info(f"开始计算舰长 {captain_id} 的收益...")
            total_captain_revenue_today = 0

            for crew_id in crew_ids:
                olog.debug(f"计算舰员 {crew_id} 的收益...")
                # 查找该舰员当天的所有未结算收益记录
                crew_daily_revenues = DailyTaskRevenue.objects(
                    user_id=crew_id,
                    date=today_timestamp,
                )

                crew_total_revenue_today = 0
                processed_revenue_ids = []
                for revenue_record in crew_daily_revenues:
                    crew_total_revenue_today += revenue_record.daily_revenue
                    processed_revenue_ids.append(str(revenue_record.id))

                if crew_total_revenue_today > 0:
                    # 计算舰长佣金 (30%)
                    captain_commission = int(crew_total_revenue_today * 0.3)
                    olog.debug(f"舰员 {crew_id} 当日总收益: {crew_total_revenue_today}, 舰长佣金: {captain_commission}")

                    if captain_commission > 0:
                        # 创建或更新舰长每日收益记录
                        CaptainDailyRevenue.objects(
                            captain_user_id=captain_id,
                            crew_user_id=crew_id,
                            date=today_timestamp
                        ).update_one(
                            set__daily_revenue=captain_commission,
                            set__daily_task_revenue_ids=processed_revenue_ids,
                            set__status="未结算",
                            set__settled_at=None, # 确保未结算时没有结算时间
                            upsert=True # 如果记录不存在则创建
                        )
                        olog.info(f"为舰长 {captain_id} 从舰员 {crew_id} 创建/更新了收益记录，金额: {captain_commission}")
                        total_captain_revenue_today += captain_commission
                    else:
                         olog.debug(f"舰员 {crew_id} 当日收益计算出的舰长佣金为 0 或负数，跳过创建记录。")

                else:
                    olog.debug(f"舰员 {crew_id} 当日无未结算收益。")

            olog.info(f"舰长 {captain_id} 当日总计获得佣金: {total_captain_revenue_today}")

        olog.info("舰长每日收益计算完成。")
