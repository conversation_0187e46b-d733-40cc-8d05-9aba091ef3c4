"use client";

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { ListItem, ListItemButton, ListItemIcon, ListItemText } from '@mui/material';
import { useTheme } from '@mui/material/styles';

export default function SidebarLink({ item, onClick }) {
  const theme = useTheme();
  const pathname = usePathname();
  const isActive = pathname === item.path;

  return (
    <ListItem disablePadding sx={{ my: 0.5 }}>
      <Link href={item.path} style={{ width: '100%', textDecoration: 'none', color: 'inherit' }}>
        <ListItemButton
          onClick={onClick}
          sx={{
            px: 2,
            py: 1.2,
            borderRadius: 1,
            mx: 1.5,
            position: 'relative',
            overflow: 'hidden',
            bgcolor: isActive ? `rgba(${theme.palette.primary.light}, 0.08)` : 'transparent',
            color: isActive ? theme.palette.primary.main : theme.palette.text.primary,
            '&::before': isActive ? {
              content: '""',
              position: 'absolute',
              left: 0,
              top: 0,
              bottom: 0,
              width: 4,
              backgroundColor: theme.palette.primary.main,
              borderRadius: '4px 0 0 4px',
            } : {},
            '& .MuiListItemIcon-root': {
              color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
              minWidth: 36,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            },
            '&:hover': {
              backgroundColor: `rgba(${theme.palette.primary.main}, 0.08)`,
              color: theme.palette.primary.main,
              '& .MuiListItemIcon-root': {
                color: theme.palette.primary.main,
              },
            },
            transition: 'all 0.2s',
          }}
        >
          <ListItemIcon>{item.icon}</ListItemIcon>
          <ListItemText 
            primary={item.text} 
            primaryTypographyProps={{ 
              fontWeight: isActive ? 'medium' : 'regular',
              fontSize: '0.95rem',
            }} 
          />
        </ListItemButton>
      </Link>
    </ListItem>
  );
} 