"use client";

import {usePathname} from 'next/navigation';
import Link from 'next/link';
import {Box, ListItem, ListItemButton, ListItemIcon, ListItemText} from '@mui/material';
import {useTheme} from '@mui/material/styles';

export default function SidebarLink({item, onClick}) {
    const theme = useTheme();
    const pathname = usePathname();
    const isActive = pathname === item.path;
    const isSubMenu = item.isSubMenu;
    const parentIconWidth = item.parentIconWidth || 36;

    return (
        <ListItem disablePadding sx={{my: isSubMenu ? 0.25 : 0.5}}>
            <Link href={item.path} style={{width: '100%', textDecoration: 'none', color: 'inherit'}}>
                <ListItemButton
                    onClick={onClick}
                    sx={{
                        py: isSubMenu ? 0.8 : 1.2,
                        borderRadius: 1,
                        mx: 1.5,
                        position: 'relative',
                        overflow: 'hidden',
                        bgcolor: isActive ? `rgba(${theme.palette.primary.light}, 0.08)` : 'transparent',
                        color: isActive ? theme.palette.primary.main : theme.palette.text.primary,
                        '&::before': isActive ? {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            bottom: 0,
                            width: 4,
                            backgroundColor: theme.palette.primary.main,
                            borderRadius: '4px 0 0 4px',
                        } : {},
                        '&:hover': {
                            backgroundColor: `rgba(${theme.palette.primary.main}, 0.08)`,
                            color: theme.palette.primary.main,
                            '& .MuiListItemIcon-root': {
                                color: theme.palette.primary.main,
                            },
                        },
                        transition: 'all 0.2s',
                        fontSize: isSubMenu ? '0.85rem' : 'inherit',
                        px: 0,
                    }}
                >
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        pl: 2
                    }}>
                        {!isSubMenu ? (
                            <ListItemIcon sx={{
                                minWidth: parentIconWidth,
                                color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
                            }}>
                                {item.icon}
                            </ListItemIcon>
                        ) : (
                            <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                                pl: 2,
                                minWidth: parentIconWidth
                            }}>
                                {item.icon && (
                                    <Box sx={{
                                        color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
                                        display: 'flex',
                                        fontSize: '0.85rem'
                                    }}>
                                        {item.icon}
                                    </Box>
                                )}
                            </Box>
                        )}
                        <ListItemText
                            primary={item.text}
                            primaryTypographyProps={{
                                fontWeight: isActive ? 'medium' : 'regular',
                                fontSize: isSubMenu ? '0.85rem' : '0.95rem',
                            }}
                        />
                    </Box>
                </ListItemButton>
            </Link>
        </ListItem>
    );
} 