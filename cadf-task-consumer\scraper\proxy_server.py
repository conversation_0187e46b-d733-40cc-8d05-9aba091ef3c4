from typing import Optional, Dict

import requests

from omni.log.log import olog


class ProxyServer:
    """代理服务器管理类"""

    # 代理服务器配置常量
    API_URL = "https://share.proxy.qg.net/get?key=37O6NQ24"
    USERNAME = "37O6NQ24"
    PASSWORD = "0C0437B5DA33"

    def __init__(self):
        """初始化代理服务器管理类"""
        self.api_url = self.API_URL
        self.username = self.USERNAME
        self.password = self.PASSWORD

    def get_proxy_server(self) -> Optional[Dict]:
        """获取代理服务器地址
        
        Returns:
            Optional[Dict]: 包含代理服务器信息的字典，如果获取失败则返回None
        """
        try:
            response = requests.get(self.api_url)
            if response.status_code == 200:
                data = response.json()
                if data["code"] == "SUCCESS" and data["data"]:
                    proxy_info = data["data"][0]
                    server = proxy_info["server"]
                    olog.info(f"成功获取代理地址: {server}")
                    return {
                        "server": server,
                        "username": self.username,
                        "password": self.password
                    }
            olog.error("获取代理地址失败")
            return None
        except Exception as e:
            olog.error(f"获取代理地址时发生错误: {str(e)}")
            return None
