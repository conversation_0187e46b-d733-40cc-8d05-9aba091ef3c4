import api from "@/core/api/api";

export const dailyTaskRevenueApi = {
    /**
     * 查询每日收益汇总
     * @param {number} page - 页码
     * @param {number} pageSize - 每页数量
     * @returns {Promise<any>} - 返回包含 items, total_items, page, page_size 的对象
     */
    queryDailySummary: async (page, pageSize) => {
        return await api({
            resource: "daily_task_revenue",
            method_name: "query_daily_summary",
            page,
            page_size: pageSize,
        });
    },

    /**
     * 查询指定日期的收益明细
     * @param {string} date - 日期字符串 (YYYY-MM-DD)
     * @returns {Promise<any>} - 返回包含 items, totalViews, totalRevenue 的对象
     */
    queryDetailsByDate: async (date) => {
        return await api({
            resource: "daily_task_revenue",
            method_name: "query_details_by_date",
            date,
        });
    },
};
