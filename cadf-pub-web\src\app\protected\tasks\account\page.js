"use client";

import {useEffect, useState} from 'react';
import {<PERSON>ert, Avatar, Box, Button, CardActions, CardContent, Chip, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Grid, IconButton, InputAdornment, LinearProgress, Pagination, Paper, Stack, Tab, Tabs, TextField, Tooltip, Typography, useMediaQuery, useTheme} from '@mui/material';
import {AlertTriangle, Award, CheckCircle, ChevronLeft, ChevronRight, Clock, Copy, Download, HelpCircle, Info, Link as LinkIcon, ListChecks, Eye, PowerOff, RefreshCw, Users, XCircle, AlertOctagon} from 'lucide-react';
import ProductCard from '../../../../components/ProductCard';
import {pubPromotionTaskApi} from '@/api/pub-promotion-task-api';
import {useDispatch} from 'react-redux';
import {addAlert} from '@/core/components/redux/alert-slice';
import {AlertType} from '@/core/components/alert';
import { useRouter } from 'next/navigation';


function AccountSelector({
                             accounts,
                             loadingAccounts,
                             errorAccounts,
                             onAccountClick,
                             onViewAcceptedTasksClick,
                             isMobile,
                         }) {
    const theme = useTheme();

    const getStatusColor = (status) => {
        switch (status) {
            case '在线':
                return 'success';
            case '离线':
                return 'warning';
            case '封禁':
                return 'error';
            default:
                return 'default';
        }
    };

    return (
        <Paper
            elevation={1}
            sx={{
                p: {xs: 2, sm: 3},
                mb: {xs: 2, sm: 3, md: 4},
                borderRadius: 2,
                bgcolor: theme.palette.grey[100],
            }}
        >
            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between" sx={{mb: 2}}>
                <Stack direction="row" spacing={1.5} alignItems="center">
                    <Users size={isMobile ? 20 : 24}/>
                    <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{fontWeight: 'medium'}}>
                        选择账户
                    </Typography>
                </Stack>
            </Stack>
            {loadingAccounts && <LinearProgress sx={{mb: 2}}/>}
            {errorAccounts && <Alert severity="error" sx={{mb: 2}}>{errorAccounts}</Alert>}
            {!loadingAccounts && !errorAccounts && accounts.length === 0 && (
                <Typography color="text.secondary" sx={{textAlign: 'center', p: 2}}>
                    暂无可用账户，请先在"账号管理"页面添加。
                </Typography>
            )}
            {!loadingAccounts && !errorAccounts && accounts.length > 0 && (
                <Grid container spacing={2}>
                    {accounts.map((account) => {
                        let 推荐任务Disabled = false;
                        let 推荐任务Title = '查看推荐任务';
                        const reasons = [];

                        if (account.status !== '在线') {
                            推荐任务Disabled = true;
                            reasons.push('账户非在线状态');
                        }

                        const dailyLimitReached = typeof account.current_usage_level === 'number' &&
                                                typeof account.limit === 'number' &&
                                                account.limit > 0 &&
                                                account.current_usage_level >= account.limit;

                        if (dailyLimitReached) {
                            推荐任务Disabled = true;
                            reasons.push('今日已接任务已达上限');
                        }

                        const uncompletedLimitReached = typeof account.uncompleted_tasks_count === 'number' &&
                                                      typeof account.max_uncompleted_limit === 'number' &&
                                                      account.max_uncompleted_limit > 0 &&
                                                      account.uncompleted_tasks_count >= account.max_uncompleted_limit;
                        
                        if (uncompletedLimitReached) {
                            推荐任务Disabled = true;
                            reasons.push('未完成任务已达上限');
                        }

                        if (推荐任务Disabled) {
                            if (reasons.length > 0) {
                                推荐任务Title = reasons.join('；') + '，因此无法查看推荐任务。';
                            } else {
                                // Fallback, though should ideally be covered by specific reasons
                                推荐任务Title = '无法查看推荐任务，请检查账户状态或任务限制。';
                            }
                        }

                        return (
                            <Grid item xs={12} sm={6} md={4} lg={3} key={account.id_}>
                                <Paper
                                    elevation={0}
                                    sx={{
                                        p: 2,
                                        borderRadius: 1.5,
                                        border: '1px solid',
                                        borderColor: 'divider',
                                        bgcolor: 'background.paper',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'space-between',
                                        height: '100%',
                                        '&:hover': {
                                            borderColor: theme.palette.primary.main,
                                            bgcolor: theme.palette.action.hover,
                                        }
                                    }}
                                >
                                    <Box>
                                        <Stack direction="row" spacing={1.5} alignItems="flex-start" sx={{ mb: 1.5 }}>
                                            <Avatar sx={{
                                                width: { xs: 36, sm: 40 },
                                                height: { xs: 36, sm: 40 },
                                                bgcolor: 'primary.light',
                                                fontSize: '1rem',
                                                mt: 0.5 
                                            }}>
                                                {account.name ? account.name.charAt(0).toUpperCase() : '?'}
                                            </Avatar>
                                            <Box flexGrow={1} sx={{overflow: 'hidden'}}>
                                                <Typography
                                                    variant="subtitle1"
                                                    fontWeight="bold"
                                                    sx={{ lineHeight: 1.3, mb: 0.25, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                                    title={account.name || '未命名账号'}
                                                >
                                                    {account.name || '未命名账号'}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.75, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} title={account.domain || '未知领域'}>
                                                    {account.domain || '未知领域'}
                                                </Typography>
                                                <Stack direction="row" spacing={0.75} alignItems="center" flexWrap="wrap">
                                                    <Chip
                                                        label={account.platform}
                                                        size="small"
                                                        variant="outlined"
                                                        sx={{ height: 24, borderRadius: '6px' }}
                                                    />
                                                    {(() => {
                                                        let StatusIconComponent = HelpCircle;
                                                        let statusVariant = 'outlined';
                                                        const statusColor = getStatusColor(account.status);

                                                        if (account.status === '在线') {
                                                            StatusIconComponent = CheckCircle;
                                                            statusVariant = 'filled'; 
                                                        } else if (account.status === '离线') {
                                                            StatusIconComponent = PowerOff;
                                                        } else if (account.status === '封禁') {
                                                            StatusIconComponent = AlertOctagon;
                                                        }

                                                        return (
                                                            <Chip
                                                                icon={<StatusIconComponent size={14} />} 
                                                                label={account.status || '未知'}
                                                                size="small"
                                                                color={statusColor}
                                                                variant={statusVariant}
                                                                sx={{ height: 24, borderRadius: '6px', '.MuiChip-icon': { ml: 0.5, mr: -0.5 } }} 
                                                            />
                                                        );
                                                    })()}
                                                </Stack>
                                            </Box>
                                        </Stack>

                                        <Divider sx={{ my: 1.5 }} />

                                        <Stack spacing={1} sx={{ mb: 1 }}>
                                            {(typeof account.current_usage_level === 'number' && typeof account.limit === 'number' && account.limit > 0) && (() => {
                                                const usageRatio = account.current_usage_level / account.limit;
                                                let chipColor = 'primary';
                                                let chipVariant = 'outlined';

                                                if (usageRatio >= 1) {
                                                    chipColor = 'error';
                                                    chipVariant = 'filled';
                                                } else if (usageRatio > 0.8) {
                                                    chipColor = 'warning';
                                                    chipVariant = 'filled';
                                                }
                                                return (
                                                    <Chip
                                                        icon={<Info size={18} />} 
                                                        label={`今日已接: ${account.current_usage_level} / ${account.limit}`}
                                                        color={chipColor}
                                                        variant={chipVariant}
                                                        sx={{ width: '100%', justifyContent: 'flex-start', height: 32, borderRadius: '8px' }}
                                                    />
                                                );
                                            })()}

                                            {(typeof account.uncompleted_tasks_count === 'number' && typeof account.max_uncompleted_limit === 'number' && account.max_uncompleted_limit > 0) && (() => {
                                                const uncompletedRatio = account.uncompleted_tasks_count / account.max_uncompleted_limit;
                                                let chipColor = 'info';
                                                let chipVariant = 'outlined';

                                                if (uncompletedRatio >= 1) {
                                                    chipColor = 'error';
                                                    chipVariant = 'filled';
                                                } else if (uncompletedRatio > 0.8) {
                                                    chipColor = 'warning';
                                                    chipVariant = 'filled';
                                                }
                                                return (
                                                    <Chip
                                                        icon={<AlertTriangle size={18} />}
                                                        label={`未完成: ${account.uncompleted_tasks_count} / ${account.max_uncompleted_limit}`}
                                                        color={chipColor}
                                                        variant={chipVariant}
                                                        sx={{ width: '100%', justifyContent: 'flex-start', height: 32, borderRadius: '8px' }}
                                                    />
                                                );
                                            })()}

                                            {!(typeof account.current_usage_level === 'number' && typeof account.limit === 'number' && account.limit > 0) &&
                                             !(typeof account.uncompleted_tasks_count === 'number' && typeof account.max_uncompleted_limit === 'number' && account.max_uncompleted_limit > 0) &&
                                             (
                                                <Typography variant="caption" color="text.disabled" sx={{ textAlign: 'center', py: 1 }}>
                                                    暂无任务统计
                                                </Typography>
                                            )}
                                        </Stack>
                                    </Box>
                                    
                                    <Stack direction="column" spacing={1} sx={{width: '100%', mt: 'auto', pt:0.5}}>
                                        <Button 
                                            variant='contained' 
                                            size="small" 
                                            fullWidth 
                                            onClick={() => onAccountClick(account)} 
                                            startIcon={<Eye size={16}/>}
                                            sx={{ borderRadius: 1.5, textTransform: 'none'}}
                                            disabled={推荐任务Disabled}
                                            title={推荐任务Title}
                                        >
                                            推荐任务
                                        </Button>
                                        <Button 
                                            variant='outlined' 
                                            size="small" 
                                            fullWidth 
                                            onClick={() => onViewAcceptedTasksClick(account)} 
                                            startIcon={<ListChecks size={16}/>}
                                            sx={{ borderRadius: 1.5, textTransform: 'none'}}
                                        >
                                            已接任务
                                        </Button>
                                    </Stack>
                                </Paper>
                            </Grid>
                        );
                    })}
                </Grid>
            )}
        </Paper>
    );
}


export default function TasksEntryPointPage() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const dispatch = useDispatch();
    const router = useRouter();

    const [accountsState, setAccountsState] = useState({
        accounts: [],
        loading: true,
        error: null,
    });

    useEffect(() => {
        const fetchAccountsAndStatus = async () => {
            setAccountsState({accounts: [], loading: true, error: null});
            try {
                const response = await pubPromotionTaskApi.queryAccountsWithTaskStatus();
                if (response && response.accounts) {
                    setAccountsState({accounts: response.accounts, loading: false, error: null});
                } else {
                    setAccountsState({accounts: [], loading: false, error: '未能获取有效的账户数据'});
                }
            } catch (error) {
                console.error("获取账户列表及状态失败:", error);
                const errorMsg = `获取账户列表失败: ${error.message || '未知错误'}`;
                setAccountsState({accounts: [], loading: false, error: errorMsg});
            }
        };
        fetchAccountsAndStatus();
    }, []);

    const handleAccountCardClick = (account) => {
        if (account && account.id_) {
            if (account.status !== '在线') {
                 dispatch(addAlert({type: AlertType.WARNING, message: `账户 ${account.name || ''} 非在线状态，无法查看任务市场。`}));
                return;
            }
            const queryParams = new URLSearchParams();
            if (account.id_) queryParams.append('accountId', account.id_);

            router.push(`/protected/tasks/market?${queryParams.toString()}`);
        } else {
            dispatch(addAlert({type: AlertType.ERROR, message: "无效的账户信息"}));
        }
    };

    const handleViewAcceptedTasks = (account) => {
        if (account && account.id_) {
            router.push(`/protected/tasks/accepted/${account.id_}`);
        } else {
             dispatch(addAlert({type: AlertType.INFO, message: "选择账户以查看其已接任务，或稍后实现查看所有已接任务。"}));
        }
    };

    return (
        <Container
            maxWidth="xl"
            sx={{
                py: {xs: 2, sm: 3, md: 4},
                px: {xs: 2, sm: 3, md: 4}
            }}
        >
            <Stack direction="row" alignItems="center" spacing={1} sx={{mb: {xs: 2, sm: 3, md: 4}}}>
                <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Box flexGrow={1}>
                    <Typography variant={isMobile ? "h5" : "h4"} component="h1" sx={{fontWeight: 'bold'}}>
                        任务中心
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{display: {xs: 'none', sm: 'block'}}}>
                        选择一个账户开始管理或接取图文发布任务。
                    </Typography>
                </Box>
            </Stack>

            <AccountSelector
                accounts={accountsState.accounts}
                loadingAccounts={accountsState.loading}
                errorAccounts={accountsState.error}
                onAccountClick={handleAccountCardClick}
                onViewAcceptedTasksClick={handleViewAcceptedTasks}
                isMobile={isMobile}
            />
        </Container>
    );
}
