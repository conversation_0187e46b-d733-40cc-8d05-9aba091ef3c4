from mongoengine import Q

from config.config import XHS_IMAGE_GEN_SET, XHS_TEXT_GEN_SET
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_many_to_redis_set
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import AiGeneratedMaterial, AiGenerationTask


@register_scheduler(trigger='cron', minute='*/5')  # 每5分钟执行一次
class RetryAiGenerationScheduler(BaseScheduler):
    def run_task(self):
        """
        # 目的
        定期检查数据库中状态为"待生成"的AI素材（图片或文本），
        并将这些素材的ID重新推送到相应的Redis任务队列中，以确保它们能够被处理和生成。

        # 算法描述
        1. 先查询所有被删除的任务ID，形成一个排除列表。
        2. 从 `AiGeneratedMaterial` 集合中查询 `is_deleted` 为 `False` 且 (`image_generation_status` 为 "待生成" 或 `text_generation_status` 为 "待生成") 的素材，同时确保素材的task_id不在被删除的任务列表中。
        3. 如果没有找到需要重试的素材，记录日志并提前返回。
        4. 初始化两个列表，分别用于存储需要重试的图片生成任务的素材ID和文本生成任务的素材ID。
        5. 遍历查询到的素材：
           a. 将素材ID（字符串格式）添加到相应的列表中。
           b. 如果素材的 `image_generation_status` 为 "待生成"，则将其ID添加到图片ID列表。
           c. 如果素材的 `text_generation_status` 为 "待生成"，则将其ID添加到文本ID列表。
        6. 如果图片ID列表不为空：
           a. 对列表中的ID进行去重。
           b. 记录准备添加到 Redis Set 的图片任务数量。
           c. 调用 `publish_many_to_redis_set` 函数，将唯一的图片ID发布到 `XHS_IMAGE_GEN_SET` 指定的 Redis Set 中。
           d. 根据发布结果记录成功或失败的日志。
        7. 如果文本ID列表不为空：
           a. 对列表中的ID进行去重。
           b. 记录准备添加到 Redis Set 的文本任务数量。
           c. 调用 `publish_many_to_redis_set` 函数，将唯一的文本ID发布到 `XHS_TEXT_GEN_SET` 指定的 Redis Set 中。
           d. 根据发布结果记录成功或失败的日志。
        8. 记录调度器执行完毕的日志。
        9. 使用 `try-except` 块捕获执行过程中的任何异常，并记录错误日志。
        """
        olog.info("开始执行 AI 生成任务重试调度器...")
        try:
            # 先查询所有被删除的任务ID
            deleted_task_ids = [str(task.id) for task in AiGenerationTask.objects(is_deleted=True)]
            olog.info(f"查询到 {len(deleted_task_ids)} 个被删除的任务。")

            # 查询需要重试的素材，确保其任务未被删除
            query = (Q(image_generation_status="待生成") | Q(text_generation_status="待生成")) & Q(is_deleted=False)
            if deleted_task_ids:
                query = query & (Q(task_id__nin=deleted_task_ids) | Q(task_id=None))
            
            materials_to_retry = AiGeneratedMaterial.objects(query)

            if not materials_to_retry:
                olog.info("没有找到需要重试的 AI 生成任务。")
                return

            image_ids_to_add_to_set = []
            text_ids_to_add_to_set = []

            for material in materials_to_retry:
                material_id_str = str(material.id)
                if material.image_generation_status == "待生成":
                    image_ids_to_add_to_set.append(material_id_str)
                if material.text_generation_status == "待生成":
                    text_ids_to_add_to_set.append(material_id_str)

            if image_ids_to_add_to_set:
                # 去重，以防万一，尽管从数据库查询出的id应该是唯一的
                unique_image_ids = list(set(image_ids_to_add_to_set))
                olog.info(f"准备将 {len(unique_image_ids)} 个图片生成任务 ID 添加/验证到 Redis Set '{XHS_IMAGE_GEN_SET}'。")
                result = publish_many_to_redis_set(XHS_IMAGE_GEN_SET, unique_image_ids)
                if result is not None:
                    olog.info(f"成功向 Redis Set '{XHS_IMAGE_GEN_SET}' 添加了 {result} 个新的图片生成任务 ID。")
                else:
                    olog.error(f"向 Redis Set '{XHS_IMAGE_GEN_SET}' 添加图片生成任务 ID 时发生错误。")

            if text_ids_to_add_to_set:
                # 去重
                unique_text_ids = list(set(text_ids_to_add_to_set))
                olog.info(f"准备将 {len(unique_text_ids)} 个文本生成任务 ID 添加/验证到 Redis Set '{XHS_TEXT_GEN_SET}'。")
                result = publish_many_to_redis_set(XHS_TEXT_GEN_SET, unique_text_ids)
                if result is not None:
                    olog.info(f"成功向 Redis Set '{XHS_TEXT_GEN_SET}' 添加了 {result} 个新的文本生成任务 ID。")
                else:
                    olog.error(f"向 Redis Set '{XHS_TEXT_GEN_SET}' 添加文本生成任务 ID 时发生错误。")

            olog.info("AI 生成任务重试调度器执行完毕。")

        except Exception as e:
            olog.error(f"执行 AI 生成任务重试调度器时出错: {e}", exc_info=True) 