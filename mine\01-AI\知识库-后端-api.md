# 关于 Api 的定义

@register_handler('study_plan') 用于 fastapi 注册相关处理器,只有被这样注册的类才能通过 http 接口访问

@auth_required(['user', 'admin']) 用于认证与鉴权,被标记了的方法必须通过认证与鉴权才能执行,里面的值是代表哪个权限可以执行,默认需要['user', 'admin']

data 里默认含有 user_id,不需要显示获取

save_or_update(StudyPlan, data) 保存数据进 mongodb 数据库,其中 StudyPlan 为 mongoengine 的表定义,data 为数据本身,这个 data 是个 dict

save*or_update(StudyPlan, data,id*) 保存数据进 mongodb 数据库,其中 StudyPlan 为 mongoengine 的表定义,data 为数据本身,id\_为主键,这个 data 是个 dict

保存数据需要用 create 方法

更新数据需要用 modify 方法

删除数据需要用 delete 方法

查询单个数据需要用 query_one 方法

查询多个数据需要用 query_all 方法

MessageSenderFactory.get*sender(Topic.GEN_OUTLINE).send_immediate_message({'id*': id\_})用于向 kafka 发送消息

主动报错使用 raise MException("错误信息")

使用例子

```Python
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict, save_or_update
from repository.models import StudyPlan
from omni.api.exception import MException

@register_handler('study_plan')
class StudyPlanApi:
    @auth_required(['user', 'admin'])
    def create(self, data):
        id_ = save_or_update(StudyPlan, data)
        return {'id_': id_}

    @auth_required(['user', 'admin'])
    def modify(self, data):
        id_ = data['id_']
        save_or_update(StudyPlan, data, id_)
        return {'id_': id_}

    @auth_required(['user', 'admin'])
    def delete(self, data):
        id_ = data.get('id_')
        StudyPlan.objects(id=id_).delete()

    @auth_required(['user', 'admin'])
    def query_one(self, data):
        id_ = data.get('id_')
        study_plan = StudyPlan.objects(id=id_).first()
        return doc_to_dict(study_plan)

    @auth_required(['user', 'admin'])
    def query_all(self, data):
        user_id = data.get('user_id')
        study_plans = StudyPlan.objects(user_id=user_id)
        return docs_to_dict(study_plans)
```
