from agent.product_description_image_recognition_agent import product_description_image_recognition_agent
from agent.product_domain_image_recognition_agent import product_domain_image_recognition_agent
from agent.product_name_image_recognition_agent import product_name_image_recognition_agent
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import save_or_update
from repository.models import DataDictionary


@register_handler('ai_agent')
class AiAgentApi:

    @auth_required(['admin', 'advertiser'])
    def generate_description(self, data):
        """
        根据提供的图片URL列表生成产品描述。
        """
        image_urls = data.get('image_urls')
        if not image_urls or not isinstance(image_urls, list):
            raise MException("请求参数错误: 'image_urls' 必须是一个包含图片URL的列表。")

        try:
            description = product_description_image_recognition_agent(image_urls)
            return {'description': description}
        except Exception as e:
            # 在实际应用中，这里应该添加更详细的日志记录
            raise MException(f"生成产品描述时出错: {e}")

    @auth_required(['admin', 'advertiser'])
    def identify_product_name(self, data):
        """
        根据提供的图片URL列表识别产品名称。
        """
        image_urls = data.get('image_urls')
        if not image_urls or not isinstance(image_urls, list):
            raise MException("请求参数错误: 'image_urls' 必须是一个包含图片URL的列表。")

        try:
            product_name = product_name_image_recognition_agent(image_urls)
            return {'product_name': product_name}
        except Exception as e:
            # 在实际应用中，这里应该添加更详细的日志记录
            raise MException(f"识别产品名称时出错: {e}")

    @auth_required(['admin', 'advertiser'])
    def identify_and_save_domains(self, data):
        """
        根据提供的图片URL列表识别产品领域，并将新领域保存到数据字典。
        """
        image_urls = data.get('image_urls')
        if not image_urls or not isinstance(image_urls, list):
            raise MException("请求参数错误: 'image_urls' 必须是一个包含图片URL的列表。")

        try:
            # 1. 获取现有领域 (从DataDictionary中 category 为 'domain' 的记录)
            existing_domain_docs = DataDictionary.objects(category='domain')
            existing_domains = [doc.key for doc in existing_domain_docs]

            # 2. 调用 Agent 识别领域
            identified_domains = product_domain_image_recognition_agent(image_urls, existing_domains)

            # 3. 保存新识别出的领域到 DataDictionary
            newly_added_domains = []
            for domain in identified_domains:
                # 检查领域是否已存在
                existing_entry = DataDictionary.objects(category='domain', key=domain).first()
                if not existing_entry:
                    domain_data = {
                        'category': 'domain',
                        'key': domain,
                        'value': domain  # 将领域名同时作为 key 和 value 存储
                    }
                    # 使用 save_or_update 创建新条目 (假设没有id_时为创建)
                    save_or_update(DataDictionary, domain_data)
                    newly_added_domains.append(domain)

            # 只返回识别出的领域
            return {'identified_domains': identified_domains}
        except Exception as e:
            # 在实际应用中，这里应该添加更详细的日志记录
            raise MException(f"识别和保存产品领域时出错: {e}")
