import api from "@/core/api/api";

export const accountApi = {
    /**
     * 创建一个新的平台账号
     * @param {string} platform - 平台中文名称 (例如 '小红书')
     * @param {Array<object>} [cookie] - 可选的 Cookie 数组
     * @param {string} cookie[].name - Cookie 名称
     * @param {string} cookie[].value - Cookie 值
     * @param {string} [cookie[].domain] - Cookie 域
     * @param {string} [cookie[].path] - <PERSON>ie 路径
     * @param {boolean} [cookie[].secure] - 是否安全
     * @param {boolean} [cookie[].httpOnly] - 是否 HttpOnly
     * @param {number} [cookie[].expirationDate] - 过期时间戳
     * @returns {Promise<object>} 创建结果，包含 id_
     */
    create: async (
        platform,
        cookie
    ) => {
        const data = {
            platform, // 使用中文平台名称
        };
        if (cookie !== undefined) {
            // 后端会处理这个数组
            data.cookie = cookie;
        }
        return await api({
            resource: "account",
            method_name: "create",
            ...data,
        });
    },

    /**
     * 修改账号信息
     * @param {string} id_ - 账号ID
     * @param {string} [name] - 账号名称
     * @param {string} [platform] - 平台中文名称
     * @param {string} [domain] - 账号领域 (使用数据字典中的 value)
     * @param {Array<object>} [cookie] - Cookie 数组
     * @param {string} [status] - 账号状态 ('在线', '离线', '封禁')
     * @returns {Promise<object>} 修改结果
     */
    modify: async (
        id_,
        name,
        platform,
        domain,
        cookie,
        status
    ) => {
        const data = {
            id_,
        };
        if (name !== undefined) data.name = name;
        if (platform !== undefined) data.platform = platform; // 使用中文平台名称
        if (domain !== undefined) data.domain = domain;
        if (cookie !== undefined) data.cookie = cookie;
        if (status !== undefined) data.status = status;

        if (Object.keys(data).length <= 1) {
            console.warn("修改账号时至少需要提供一个可修改的字段（除了 id_）");
            return Promise.resolve({message: "没有提供可修改的字段"});
        }

        return await api({
            resource: "account",
            method_name: "modify",
            ...data,
        });
    },

    delete: async (id_) => {
        return await api({
            resource: "account",
            method_name: "delete",
            id_,
        });
    },

    queryOne: async (id_) => {
        return await api({
            resource: "account",
            method_name: "query_one",
            id_,
        });
    },

    /**
     * 查询所有账号
     * @param {string | null} [platform=null] - 平台中文名称，用于筛选
     * @returns {Promise<object>} 查询结果，包含 accounts 和 total_count
     */
    queryAll: async (platform = null) => {
        const data = {};
        if (platform) {
            data.platform = platform; // 使用中文平台名称进行筛选
        }
        return await api({
            resource: "account",
            method_name: "query_all",
            ...data
        });
    },
};
