import api from "@/core/api/api";

export const userApi = {
  /**
   * 用户登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<{access_token: string}>} 登录结果
   */
  login: async (username, password) => {
      return await api({
        resource: "user",
        method_name: "login",
        username,
        password,
    });
  },
  
  /**
   * 查询所有用户
   * @returns {Promise<Array>} 用户列表
   */
  queryAll: async () => {
    return await api({
      resource: "user",
      method_name: "query_all",
    });
  },
  
  /**
   * 查询单个用户
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  queryOne: async (userId) => {
    return await api({
      resource: "user",
      method_name: "query_one",
      target_user_id: userId,
    });
  },
  
  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @param {string} userData.username - 用户名
   * @param {string} userData.password - 密码
   * @param {Array<string>} userData.roles - 用户角色
   * @returns {Promise<Object>} 创建结果
   */
  create: async (userData) => {
    return await api({
      resource: "user",
      method_name: "create",
      ...userData,
    });
  },
  
  /**
   * 更新用户
   * @param {Object} userData - 用户数据
   * @param {string} userData.id_ - 用户ID
   * @param {string} [userData.username] - 用户名（可选）
   * @param {string} [userData.password] - 密码（可选）
   * @param {Array<string>} [userData.roles] - 用户角色（可选）
   * @returns {Promise<Object>} 更新结果
   */
  update: async (userData) => {
    return await api({
      resource: "user",
      method_name: "update",
      ...userData,
    });
  },
  
  /**
   * 删除用户
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  delete: async (userId) => {
    return await api({
      resource: "user",
      method_name: "delete",
      id_: userId,
    });
  },
};