import secrets
import string
from datetime import datetime, timedelta
import time # 导入 time 模块

from mongoengine.queryset.visitor import Q

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from bson import ObjectId
from repository.models import User, CrewManagement, Account, DailyTaskRevenue, PromotionTaskDetail


def timestamp_to_str(timestamp: int | float | None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str | None:
    """
    将 Unix 时间戳转换为指定格式的日期时间字符串。

    Args:
        timestamp: Unix 时间戳 (秒)。可以是整数、浮点数或 None。
        format_str: 输出字符串的格式，默认为 "YYYY-MM-DD HH:MM:SS".

    Returns:
        格式化后的日期时间字符串，如果输入为 None 或无效则返回 None。
    """
    if timestamp is None:
        return None
    try:
        # 将时间戳转换为 datetime 对象
        # 确保时间戳是数字类型
        ts = float(timestamp)
        dt_object = datetime.fromtimestamp(ts)
        # 格式化为字符串
        return dt_object.strftime(format_str)
    except (ValueError, TypeError, OSError) as e:
        # 处理无效的时间戳或转换错误
        # print(f"Error converting timestamp {timestamp}: {e}") # 在 API 中通常不直接打印
        return None


def generate_random_code(prefix="CADF", length=6):
    alphabet = string.ascii_uppercase + string.digits
    random_part = ''.join(secrets.choice(alphabet) for i in range(length))
    return f"{prefix}{random_part}"


@register_handler('crew_management')
class CrewManagementApi:
    @auth_required(['captain', 'admin'])
    def get_invite_code(self, data):
        user_id = data.get('user_id')
        captain = User.objects(id=user_id).first()
        if not captain:
            raise MException("舰长用户不存在")

        invite_code = captain.crew_invite_code

        if not invite_code:
            invite_code = self._generate_and_save_code(captain)

        return {'invite_code': invite_code}

    @auth_required(['captain', 'admin'])
    def generate_invite_code(self, data):
        user_id = data.get('user_id')
        captain = User.objects(id=user_id).first()
        if not captain:
            raise MException("舰长用户不存在")

        new_code = self._generate_and_save_code(captain)
        return {'invite_code': new_code}

    def _generate_and_save_code(self, captain):
        new_code = generate_random_code()
        # 理论上需要检查 code 唯一性，但碰撞概率极低，暂忽略
        captain.crew_invite_code = new_code
        captain.save()
        return new_code

    @auth_required(['captain', 'admin'])
    def get_crew_stats(self, data):
        """
        **目的**: 获取指定舰长下所有舰员的状态统计信息，包括在线/离线数量和活跃/不活跃数量。

        **算法描述**:
            - **获取舰员列表**: 根据舰长用户ID (`captain_user_id`) 从 `CrewManagement` 集合中查询其管理的所有舰员用户ID (`crew_user_id`)。
            - **处理无舰员**: 如果舰长没有任何舰员，则直接返回所有统计数量为0。
            - **查询账号状态**: 查询 `Account` 集合，获取步骤1中所有舰员ID对应的账号状态，构建 `user_id` 到 `status` 的映射。
            - **统计在线/离线**: 遍历舰员ID列表，根据账号状态映射判断每个舰员是在线（状态为'在线'）还是离线（其他状态），并分别计数。
            - **查询活跃信息**: 查询 `PromotionTaskDetail` 集合，找出在步骤1的舰员ID列表中，存在 `validation_status` 为 "成功" 且 `publish_at` 在最近两天内的记录的用户ID（去重）。
            - **统计活跃/不活跃**: 活跃舰员数量为上一步去重后的用户ID数量；不活跃舰员数量为总舰员数减去活跃舰员数。
            - **返回统计**: 返回包含 `online_count`, `offline_count`, `active_count`, `inactive_count` 的字典。
        """
        captain_user_id = data.get('user_id')

        # 1. 找出该舰长下的所有舰员ID
        crew_links = CrewManagement.objects(captain_user_id=captain_user_id)
        crew_user_ids = [link.crew_user_id for link in crew_links if link.crew_user_id]

        if not crew_user_ids:
            return {'online_count': 0, 'offline_count': 0, 'active_count': 0, 'inactive_count': 0}

        # 2. 查询这些舰员关联的账号状态
        #    假设一个舰员只管理一个账号，或者我们只关心他名下的任一账号状态
        #    简化处理：查询所有这些 user_id 对应的 Account
        accounts = Account.objects(user_id__in=crew_user_ids)
        account_status_map = {acc.user_id: acc.status for acc in accounts}  # user_id -> status

        online_count = 0
        offline_count = 0
        for crew_id in crew_user_ids:
            status = account_status_map.get(crew_id)
            if status == '在线':
                online_count += 1
            else:  # 离线或封禁都算离线
                offline_count += 1

        # 3. 查询这些舰员是否在 PromotionTaskDetail 中有成功的、近2天发布的记录来判断活跃状态
        two_days_ago = int((datetime.now() - timedelta(days=2)).timestamp())  # 使用时间戳
        active_users = PromotionTaskDetail.objects(
            Q(user_id__in=crew_user_ids) &
            Q(validation_status='成功') &
            Q(publish_at__gte=two_days_ago)
        ).distinct('user_id')

        active_count = len(active_users)
        inactive_count = len(crew_user_ids) - active_count

        return {
            'online_count': online_count,
            'offline_count': offline_count,
            'active_count': active_count,
            'inactive_count': inactive_count
        }

    @auth_required(['captain', 'admin'])
    def get_crew_revenue_ranking(self, data):
        """
        **目的**: 获取指定舰长下所有舰员的收益排行榜，支持分页。

        **算法描述**:
            - **获取舰员列表**: 根据舰长用户ID (`captain_user_id`) 从 `CrewManagement` 集合中查询其管理的所有舰员用户ID (`crew_user_id`)。
            - **处理无舰员**: 如果舰长没有任何舰员，则直接返回空列表和总数0。
            - **查询舰员用户名**: 查询 `User` 集合，获取步骤1中所有舰员ID对应的用户名，构建 `user_id` 到 `username` 的映射。
            - **计算个人排名数据**: 遍历舰员ID列表：
                - **获取用户名**: 从用户名字典中查找当前舰员的用户名。
                - **查询收益记录**: 查询 `DailyTaskRevenue` 集合中该舰员的所有收益记录。
                - **计算总收益**: 对查询到的收益记录按 `daily_revenue` 字段求和。
                - **计算任务数**: 获取该舰员收益记录中不重复的 `promotion_task_detail_id` 数量。
                - **存储单人数据**: 将舰员ID、用户名、总收益和任务数存入临时列表 `all_crew_ranking_data`。
            - **排序**: 将 `all_crew_ranking_data` 列表按照 `revenue` 字段进行降序排序。
            - **分页**: 根据请求参数 `page` 和 `rowsPerPage`，计算分页的起始和结束索引，并从排序后的列表中提取对应页的数据。
            - **返回排名**: 返回包含分页后的舰员排名列表 `crew` 和总舰员数 `total_count` 的字典。
        """
        captain_user_id = data.get('user_id')
        page = int(data.get('page', 1))
        rows_per_page = int(data.get('rowsPerPage', 10))

        # 1. 找出该舰长下的所有舰员ID
        crew_links = CrewManagement.objects(captain_user_id=captain_user_id)
        crew_user_ids = [link.crew_user_id for link in crew_links if link.crew_user_id]

        if not crew_user_ids:
            return {'crew': [], 'total_count': 0}

        # 2. 查询舰员用户名
        users = User.objects(id__in=crew_user_ids).only('id', 'username')
        user_map = {str(user.id): user.username for user in users}

        # 3. 使用非管道查询计算每个舰员的总收益和任务数
        all_crew_ranking_data = []
        for crew_id in crew_user_ids:
            username = user_map.get(crew_id, "未知用户")
            revenue_records = DailyTaskRevenue.objects(user_id=crew_id)

            total_revenue = revenue_records.sum('daily_revenue') or 0
            # 获取不重复的任务 ID 列表，然后计算数量
            task_ids = revenue_records.distinct('promotion_task_detail_id')
            task_count = len(task_ids)

            all_crew_ranking_data.append({
                'id': crew_id,
                'user_account': username,
                'revenue': total_revenue,
                'task_count': task_count
            })

        # 4. 按收益降序排序
        all_crew_ranking_data.sort(key=lambda x: x['revenue'], reverse=True)

        # 5. 分页
        total_count = len(all_crew_ranking_data)
        start_index = (page - 1) * rows_per_page
        end_index = start_index + rows_per_page
        paginated_crew = all_crew_ranking_data[start_index:end_index]

        return {'crew': paginated_crew, 'total_count': total_count}

    @auth_required(['captain', 'admin'])
    def get_crew_list(self, data):
        """
        **目的**: 获取指定舰长下的舰员列表，支持按账号登录状态过滤，并进行分页显示。

        **算法描述**:
            - **获取舰员ID列表**: 根据提供的舰长用户ID (`captain_user_id`) 从 `CrewManagement` 集合中查询其管理的所有舰员用户ID (`crew_user_id`)。
            - **处理无舰员**: 如果舰长没有任何舰员，则直接返回空列表和总数0。
            - **构建账号查询**: 构建 `Account` 查询条件 (`account_query`)，筛选 `user_id` 在舰员ID列表中 (`user_id__in`) 且满足状态过滤条件 (`status_filter`) 的账号。
            - **获取总数**: 执行 `account_query.count()` 获取满足条件的账号总数 (`total_count`)。
            - **处理无结果**: 如果 `total_count` 为0，则直接返回空列表和总数0。
            - **数据库分页查询账号**: 根据请求参数 `page` 和 `rowsPerPage` 计算跳过的记录数 (`skip`) 和每页记录数 (`limit`)。在 `account_query` 上应用 `.skip(skip).limit(limit)` 并使用 `.only('user_id', 'name', 'status', 'last_login_check_at')` 只选择需要的字段，执行查询获取分页后的 `Account` 对象列表 (`paginated_accounts`)。
            - **获取分页后的用户ID**: 从 `paginated_accounts` 中提取 `user_id` 列表 (`final_crew_user_ids`)。
            - **处理分页无用户**: 如果 `final_crew_user_ids` 为空（理论上在 `total_count > 0` 时不应发生，但作为健壮性检查），返回空列表和总数0。
            - **获取用户信息**: 查询 `User` 集合，获取 `final_crew_user_ids` 列表中所有用户的ID和用户名 (`.only('id', 'username')`)，构建 `user_id` 到 `username` 的映射 (`user_map`)。
            - **组合舰员数据**: 遍历分页后的 `paginated_accounts` 列表：
                - 从 `user_map` 中查找当前账号 `user_id` 对应的用户名。
                - 将账号信息 (ID `id_`, 用户名 `user_account`, 平台账号名 `platform_account`, 登录状态 `login_status`, 最后登录时间 `last_login`) 组合成字典，添加到结果列表 `combined_crew_data` 中。
            - **返回结果**: 返回包含分页后的舰员列表 `crew` (`combined_crew_data`) 和之前计算的总舰员数 `total_count` 的字典。
        """
        captain_user_id = data.get('user_id')
        status_filter = data.get('status')  # '在线', '离线', or None for all
        page = int(data.get('page', 1))
        rows_per_page = int(data.get('rowsPerPage', 10))

        # 1. 获取舰长下的所有舰员ID
        crew_links = CrewManagement.objects(captain_user_id=captain_user_id)
        crew_user_ids = [link.crew_user_id for link in crew_links if link.crew_user_id]

        if not crew_user_ids:
            return {'crew': [], 'total_count': 0}

        # 3. 构建 Account 查询 - 包含状态过滤
        account_query = Q(user_id__in=crew_user_ids)
        if status_filter:
            account_query &= Q(status=status_filter)
        
        # 获取总数 (在应用分页前)
        total_count = Account.objects(account_query).count()
        
        if total_count == 0:
            return {'crew': [], 'total_count': 0}

        # 计算分页参数
        skip = (page - 1) * rows_per_page
        limit = rows_per_page

        # 查询分页后的 Account，只选择必要字段
        paginated_accounts = Account.objects(account_query).only(
            'user_id', 'name', 'status', 'last_login_check_at'
        ).skip(skip).limit(limit)

        # 4. 获取最终符合所有条件 (用户+账号状态) 的 User ID (仅分页后的)
        final_crew_user_ids = [acc.user_id for acc in paginated_accounts]

        if not final_crew_user_ids:
             # 这通常不应该发生，因为 total_count > 0，但作为健壮性检查
             return {'crew': [], 'total_count': total_count} 

        # 5. 获取这些用户的用户名
        final_users = User.objects(id__in=final_crew_user_ids).only('id', 'username')
        user_map = {str(user.id): user.username for user in final_users}

        # 6. 组合数据 (基于分页后的账号)
        combined_crew_data = []
        for account in paginated_accounts: # 遍历分页后的账号
            user_id = account.user_id
            username = user_map.get(user_id, "未知用户")
            combined_crew_data.append({
                'id_': user_id,  # 修改字段名 id -> id_
                'user_account': username,
                'platform_account': account.name,  # 假设平台账号名在 account.name
                'login_status': account.status,
                # 时间戳转换为 YYYY-MM-DD HH:MM:SS 格式字符串，如果不存在则为空
                'last_login': timestamp_to_str(account.last_login_check_at) if account.last_login_check_at else None
            })

        # 7. 返回分页结果和总数
        # total_count = len(combined_crew_data) # 不再需要在这里计算 total_count
        # start_index = (page - 1) * rows_per_page # 不再需要计算 index
        # end_index = start_index + rows_per_page
        # paginated_crew = combined_crew_data[start_index:end_index] # 不再需要内存分页

        return {'crew': combined_crew_data, 'total_count': total_count}

    @auth_required(['captain', 'admin'])
    def get_activity_based_crew_list(self, data):
        """
        **目的**: 根据活跃状态（最近是否有成功任务）获取舰员列表，支持分页。

        **参数**:
            - captain_user_id: 舰长用户ID
            - activity_status: 'active' 或 'inactive'
            - page: 页码
            - rowsPerPage: 每页数量

        **算法描述**:
            1. 获取舰长下的所有舰员ID (`all_crew_user_ids`)。
            2. 计算两天前的时间戳。
            3. 查询 PromotionTaskDetail，找出在 `all_crew_user_ids` 中、状态为"成功"且发布时间在近两天的用户ID，得到 `active_user_ids`。
            4. 根据请求的 `activity_status` 确定目标用户ID列表 (`target_user_ids`):
                - 'active': `target_user_ids` = `active_user_ids`
                - 'inactive': `target_user_ids` = `all_crew_user_ids` 中排除 `active_user_ids` 的部分。
            5. 计算目标用户的总数 (`total_count`)。
            6. 如果 `total_count` 为0，直接返回空列表和总数0。
            7. 计算数据库分页参数 `skip` 和 `limit`。
            8. **在数据库层面进行分页查询**: 使用 `target_user_ids` (`id__in`) 结合 `skip` 和 `limit` 查询 `User` 集合，仅获取当前页用户的 `id` 和 `username`，得到 `paginated_users`。
            9. 从 `paginated_users` 中提取当前页的用户ID列表 `paginated_user_ids`，并构建只包含当前页用户的 `user_map`。
            10. 如果 `paginated_user_ids` 为空，返回空列表和 `total_count`。
            11. **仅针对分页后的用户查询账号信息**: 使用 `paginated_user_ids` (`user_id__in`) 查询 `Account` 集合，获取账号名、最后登录时间、登录状态等，构建 `account_map`。
            12. 遍历 `paginated_user_ids`。
            13. 对于每个分页后的用户ID，从 `user_map` 获取用户名，从 `account_map` 获取账号信息，组合成最终的舰员数据。
            14. 返回包含分页后的舰员列表 `crew` 和总数 `total_count` 的字典。
        """
        captain_user_id = data.get('user_id')
        activity_status = data.get('activity_status')  # 'active' or 'inactive'
        page = int(data.get('page', 1))
        rows_per_page = int(data.get('rowsPerPage', 10))

        if activity_status not in ['active', 'inactive']:
            raise MException("无效的活跃状态参数")

        # 1. 获取舰长下的所有舰员ID
        crew_links = CrewManagement.objects(captain_user_id=captain_user_id)
        # 确保 ID 是字符串类型
        all_crew_user_ids = [str(link.crew_user_id) for link in crew_links if link.crew_user_id]

        if not all_crew_user_ids:
            return {'crew': [], 'total_count': 0}

        # 2. 计算两天前的时间戳
        two_days_ago = int((datetime.now() - timedelta(days=2)).timestamp())

        # 3. 查询活跃用户 ID (近两天有成功任务)
        active_users_query = PromotionTaskDetail.objects(
            Q(user_id__in=all_crew_user_ids) &
            Q(validation_status='成功') &
            Q(publish_at__gte=two_days_ago)
        )
        # 确保 active_user_ids 是字符串列表
        active_user_ids = [str(user_id) for user_id in active_users_query.distinct('user_id')]


        # 4. 确定目标用户ID列表
        target_user_ids = []
        if activity_status == 'active':
            target_user_ids = active_user_ids
        else: # inactive
            active_user_ids_set = set(active_user_ids)
            target_user_ids = [uid for uid in all_crew_user_ids if uid not in active_user_ids_set]

        total_count = len(target_user_ids)

        # 5. 如果目标列表为空
        if total_count == 0:
            return {'crew': [], 'total_count': 0}

        # 6. 计算分页参数 (优化点: 先计算分页参数)
        skip = (page - 1) * rows_per_page
        limit = rows_per_page

        # 7. **优化点: 直接在数据库中对目标用户ID进行分页查询 User 信息**
        #   先获取分页后的 User 对象 (包含 id 和 username)
        # 尝试将字符串 ID 转换为 ObjectId，如果它们是有效的 ObjectId 字符串
        try:
            target_object_ids = [ObjectId(uid) for uid in target_user_ids if ObjectId.is_valid(uid)]
            target_ids_for_query = target_object_ids + [uid for uid in target_user_ids if not ObjectId.is_valid(uid)]
        except Exception:
            target_ids_for_query = target_user_ids # 如果转换失败，使用原列表

        paginated_users = User.objects(id__in=target_ids_for_query).only('id', 'username').skip(skip).limit(limit)

        # 8. 提取分页后的用户 ID 和构建初步的 user_map
        paginated_user_ids = [str(user.id) for user in paginated_users]
        user_map = {str(user.id): user.username for user in paginated_users} # 现在 user_map 只包含当前页的用户

        # 9. 如果分页结果为空 (理论上 total_count > 0 时不应发生)
        if not paginated_user_ids:
            return {'crew': [], 'total_count': total_count} # 返回总数，但列表为空

        # 10. **优化点: 仅查询分页后用户的账号信息**
        accounts = Account.objects(user_id__in=paginated_user_ids).only('user_id', 'name', 'last_login_check_at', 'status')
        account_map = {acc.user_id: {'name': acc.name, 'last_login': acc.last_login_check_at, 'login_status': acc.status} for acc in accounts}

        # 11. 组合数据 (基于分页后的用户)
        combined_crew_data = []
        for user_id in paginated_user_ids: # 遍历分页后的 ID
            username = user_map.get(user_id, "未知用户") # 从已分页的用户map获取
            account_info = account_map.get(user_id, {'name': '未找到账号', 'last_login': None, 'login_status': '未知'}) # 从账号map获取
            combined_crew_data.append({
                'id_': user_id,
                'user_account': username,
                'platform_account': account_info['name'],
                'last_login': timestamp_to_str(account_info['last_login']) if account_info['last_login'] else None,
                 'login_status': account_info['login_status']
            })

        # 12. 返回分页结果和总数
        return {'crew': combined_crew_data, 'total_count': total_count}

    @auth_required(['captain', 'admin'])
    def get_crew_count(self, data):
        """获取舰长下的舰员总数"""
        captain_user_id = data.get('user_id')
        if not User.objects(id=captain_user_id).first():
             raise MException("舰长用户不存在")

        crew_count = CrewManagement.objects(captain_user_id=captain_user_id).count()
        return {'crew_count': crew_count}

    # 允许 'creator' 和 'user' 调用此方法
    @auth_required(['creator', 'user', 'admin']) 
    def join_fleet(self, data):
        """
        加入舰队

        Args:
            data (dict): 包含 'fleet_code' 和 'user_id' 的字典
        """
        fleet_code = data.get('fleet_code')
        crew_user_id = data.get('user_id') # 获取当前登录用户的ID

        if not fleet_code:
            raise MException("请输入舰队邀请码")

        # 1. 查找拥有该邀请码的舰长
        captain = User.objects(crew_invite_code=fleet_code).first()
        if not captain:
            raise MException("无效的舰队邀请码")

        captain_user_id = str(captain.id)

        # 2. 检查是否尝试加入自己的舰队
        if captain_user_id == crew_user_id:
            raise MException("不能加入自己的舰队")

        # 3. 检查该用户是否已经是某个舰队的成员
        existing_membership = CrewManagement.objects(crew_user_id=crew_user_id).first()
        if existing_membership:
            # 检查是否已经是这个舰队的成员
            if existing_membership.captain_user_id == captain_user_id:
                raise MException("您已经是该舰队成员")
            else:
                # 可以考虑允许更换舰队，或者提示先退出
                raise MException("您已加入其他舰队，请先退出") 
                # 如果允许更换，可以删除 existing_membership

        # 4. 创建新的舰队成员关系
        new_crew_link = CrewManagement(
            captain_user_id=captain_user_id,
            crew_user_id=crew_user_id,
            create_at=int(time.time())
        )
        new_crew_link.save()

        # 5. 可以在这里考虑是否需要更新用户的角色等信息（如果加入舰队需要特定角色）
        # current_user = User.objects(id=crew_user_id).first()
        # if current_user and 'crew' not in current_user.roles:
        #    current_user.roles.append('crew')
        #    current_user.save()

        return {'message': '成功加入舰队'}
