import Recorder from 'recorder-core';
import 'recorder-core/src/engine/wav';
import fileManager from '@/core/api/cos-api';
import api from '@/core/api/api';
import {TMP_PARENT_KEY} from "@/config";

class AsrManager {
    constructor() {
        this.recorder = null;
        this.isRecording = false;
        this.uploadPath = TMP_PARENT_KEY;
        this.maxRecordingDuration = 60000; // 最大录音时长（毫秒）
        this.recordingTimeout = null;
        this.isInitialized = false; // 新增：初始化状态标志
        this.initializationPromise = null; // 新增：用于处理并发初始化请求
    }

    /**
     * 初始化录音机
     * 这个方法现在会确保初始化只执行一次，并处理并发调用。
     */
    async initRecorder() {
        if (this.isInitialized && this.recorder) {
            return Promise.resolve(); // 已成功初始化
        }

        if (this.initializationPromise) {
            return this.initializationPromise; // 正在初始化中，返回当前的promise
        }

        // 如果存在一个recorder实例但未标记为isInitialized，先尝试关闭它
        if (this.recorder) {
            try {
                this.recorder.close();
            } catch (e) {
                console.warn('Error closing existing recorder instance before re-init:', e);
            }
            this.recorder = null;
        }
        this.isInitialized = false; // 重置状态，准备初始化

        this.initializationPromise = new Promise((resolve, reject) => {
            this.recorder = Recorder({
                type: 'wav',
                sampleRate: 16000,
                bitRate: 16
            });

            this.recorder.open(
                () => {
                    this.isInitialized = true;
                    this.initializationPromise = null; // 初始化完成，清除promise
                    resolve();
                },
                (msg, isUserNotAllow) => {
                    const errorMsg = isUserNotAllow ? '用户拒绝了麦克风权限' : '无法打开麦克风：' + msg;
                    // 初始化失败，清理状态
                    if (this.recorder) {
                        try {
                            this.recorder.close();
                        } catch (e) { /* ignore */ }
                    }
                    this.recorder = null;
                    this.isInitialized = false;
                    this.initializationPromise = null; // 初始化失败，清除promise
                    reject(new Error(errorMsg));
                }
            );
        }).catch(error => {
            // 确保在Promise链中发生任何未捕获的错误时，状态也能被重置
            this.recorder = null;
            this.isInitialized = false;
            this.initializationPromise = null;
            throw error; // 重新抛出错误
        });

        return this.initializationPromise;
    }

    /**
     * 开始录音
     */
    async startRecording() {
        if (this.isRecording) {
            throw new Error('录音已经在进行中');
        }

        // 检查是否已初始化 (应该由VoiceButton的useEffect在页面加载时完成初始化)
        if (!this.isInitialized || !this.recorder) {
            throw new Error('录音机未初始化。请确保麦克风权限已授予，并在页面加载时初始化成功。');
        }

        this.recorder.start();
        this.isRecording = true;

        // 添加录音时长限制
        this.recordingTimeout = setTimeout(() => {
            if (this.isRecording) {
                this.stopRecordingAndRecognize()
                    .catch(error => console.error('自动停止录音失败:', error));
            }
        }, this.maxRecordingDuration);
    }

    /**
     * 停止录音并进行语音识别
     */
    async stopRecordingAndRecognize() {
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }
        if (!this.isRecording || !this.recorder) {
            throw new Error('没有正在进行的录音');
        }

        return new Promise((resolve, reject) => {
            this.recorder.stop(async (blob) => {
                this.isRecording = false;
                try {
                    const result = await this.processRecording(blob);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            }, (msg) => {
                this.isRecording = false;
                reject(new Error('录音停止失败：' + msg));
            });
        });
    }

    /**
     * 处理录音文件并进行识别
     */
    async processRecording(blob) {
        if (!blob || blob.size === 0) {
            throw new Error('录音数据为空');
        }

        try {
            const fileKey = await fileManager.uploadFile(
                new File([blob], `record_${Date.now()}.wav`, {type: 'audio/wav'}),
                this.uploadPath
            );

            const result = await api({
                resource: 'asr',
                method_name: 'long_text_recognition',
                file_key: fileKey
            });

            return result;
        } catch (error) {
            throw new Error(`语音识别处理失败: ${error.message}`);
        }
    }

    /**
     * 销毁录音实例
     */
    destroy() {
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }

        if (this.recorder) {
            if (this.isRecording) {
                try {
                    // 尝试停止录音，但不关心其回调，主要目的是确保停止
                    this.recorder.stop(() => {}, () => {}, false); 
                } catch (e) {
                    console.warn('Error stopping recorder during destroy:', e);
                }
            }
            try {
                this.recorder.close();
            } catch (e) {
                console.warn('Error closing recorder during destroy:', e);
            }
        }
        this.recorder = null;
        this.isRecording = false;
        this.isInitialized = false; // 重置初始化状态
        this.initializationPromise = null; // 清除初始化Promise
    }

    isCurrentlyRecording() {
        return this.isRecording;
    }
}

// 创建单例
const ASRManager = new AsrManager();
export default ASRManager;
