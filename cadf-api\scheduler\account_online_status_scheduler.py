from config.config import XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET  # 更新导入的 Redis Key
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_many_to_redis_set  # 导入发布函数
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import Account  # 导入 Account 模型


@register_scheduler(trigger='cron', hour='3', minute='0')    # 每天凌晨3点执行
# @register_scheduler(trigger='interval', seconds=10)  # 每10秒执行一次
class AccountOnlineStatusCheckScheduler(BaseScheduler):  # 重命名 Class
    def run_task(self):
        """
        **目的**: 定期查询所有账户的 ID，并将这些 ID 发布到指定的 Redis Set 中，**触发后续的爬虫服务来验证这些账户的在线状态**。

        **算法描述**:
            - **查询账户**: 从数据库中查询 `Account` 表，仅获取所有账户的 `id` 字段。
            - **空值处理**: 如果查询结果为空（没有账户），则记录提示信息并提前结束任务。
            - **数据转换**: 将查询到的账户 ID（`Account` 模型实例）列表转换为字符串 ID 列表。
            - **记录日志**: 记录准备添加到 Redis Set 的账户 ID 数量和目标 Set 的名称。
            - **发布到 Redis**: 调用 `publish_many_to_redis_set` 函数，将账户 ID 列表批量添加到由 `XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET` 指定的 Redis Set 中，**以便后续的爬虫服务可以消费这些 ID 进行在线状态检查**。
            - **结果处理**:
                - **成功**: 如果 Redis 操作返回成功添加的新元素数量，则记录成功日志。
                - **失败**: 如果 Redis 操作返回 `None` 或失败，则记录错误日志。
            - **异常处理**: 捕获整个任务执行过程中的任何异常，记录详细错误信息，并包含堆栈跟踪。
        """
        try:
            # 优化：仅选择 id 列以提高性能
            accounts = Account.select(Account.id)
            if not accounts:
                olog.info("没有找到需要检查在线状态的账户。")
                return

            # accounts 现在是只包含 id 的 Model 实例列表
            account_ids = [str(account.id) for account in accounts]
            olog.info(f"准备将 {len(account_ids)} 个账户 ID 添加到 Redis Set '{XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET}' 以进行在线状态检查。")

            result = publish_many_to_redis_set(XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET, account_ids)  # 使用新的 Key

            if result is not None:
                olog.info(f"成功向 Redis Set '{XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET}' 添加了 {result} 个新的账户 ID。")
            else:
                olog.error(f"向 Redis Set '{XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET}' 添加账户 ID 时发生错误。")

        except Exception as e:
            olog.error(f"执行账户在线状态检查任务时出错: {e}", exc_info=True)
