import base64
import io
import time

from openai import OpenAI, RateLimitError, InternalServerError, BadRequestError

from omni.config.config_loader import config_dict
from omni.log.log import olog

IMAGE_QUALITY = "auto"
RETRY_WAIT_SECONDS_DEFAULT = 1


class OpenaiImageEditor:
    def __init__(self, llm_config_key: str):
        olog.info(f"初始化 OpenaiImageEditor，配置键: '{llm_config_key}'")
        self.llm_config_key = llm_config_key
        self.llm_config = config_dict.get("llm", {}).get(llm_config_key)
        if not self.llm_config:
            raise ValueError(f"配置键 '{llm_config_key}' 未找到")

        self.api_key = self.llm_config.get("api_key")
        raw_api_base = self.llm_config.get("api_base")
        self.model_name = self.llm_config.get("model_name")
        self.api_base = (
            (raw_api_base if raw_api_base.endswith("/") else raw_api_base + "/")
            if raw_api_base
            else None
        )

        if not self.api_key or not self.api_base or not self.model_name:
            raise ValueError(f"配置键 '{llm_config_key}' 不完整")

        self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)
        olog.info(f"OpenAI 客户端初始化成功，模型: {self.model_name}")

    def _call_with_retry(self, api_func) -> tuple[list[bytes], str]:
        results = []
        error_type = "success"
        olog.info(f"开始调用 OpenAI 图片接口")
        try:
            retry = True
            while retry:
                retry = False
                try:
                    result = api_func()
                    if not result or not result.data:
                        raise ValueError("API 响应无效")
                    for item_idx, image_item in enumerate(result.data):
                        image = base64.b64decode(image_item.b64_json)
                        results.append(image)
                        olog.info(f"第 {item_idx + 1} 张图片完成")
                except RateLimitError as e:
                    olog.warning(
                        f"速率限制，等待 {RETRY_WAIT_SECONDS_DEFAULT} 秒，异常详情: {e}"
                    )
                    time.sleep(RETRY_WAIT_SECONDS_DEFAULT)
                    retry = True
                except InternalServerError as e:
                    olog.warning(
                        f"遇到暂无可用渠道，等待 {RETRY_WAIT_SECONDS_DEFAULT} 秒后重试，异常详情: {e}"
                    )
                    time.sleep(RETRY_WAIT_SECONDS_DEFAULT)
                    retry = True
                except BadRequestError as e:
                    if "moderation_blocked" in str(e):
                        error_type = "security_blocked"
                        olog.warning(f"请求被安全机制拒绝，不再重试，异常详情: {e}")
                    else:
                        error_type = "unknown_error"
                        olog.exception(f"BadRequestError: {e}")
                        time.sleep(RETRY_WAIT_SECONDS_DEFAULT)
                except Exception as e:
                    error_type = "unknown_error"
                    olog.exception(f"未知异常: {type(e).__name__}: {e}")
        except Exception as e:
            error_type = "unknown_error"
            olog.exception(f"调用 OpenAI 图片接口失败: {e}")
        olog.info(f"调用 OpenAI 图片接口完成，生成 {len(results)} 张图片")
        return results, error_type

    def generate_image_from_image(
            self, image_bytes_list: list[bytes], prompt: str, size: str = "1024x1536"
    ) -> tuple[bytes, str]:
        olog.info(f"开始批量编辑 {len(image_bytes_list)} 张图片，提示: '{prompt}'")
        image_file_objs = [io.BytesIO(img_bytes) for img_bytes in image_bytes_list]
        results, error_type = self._call_with_retry(
            lambda: self.client.images.edit(
                model=self.model_name,
                image=image_file_objs,
                prompt=prompt,
                size=size,
                n=1,
                quality=IMAGE_QUALITY,
            ),
        )
        return (results[0] if results else None, error_type)

    def generate_image_from_prompt(self, prompt: str, size: str = "1024x1536") -> tuple[bytes, str]:
        olog.info(f"开始生成图片，提示: '{prompt}'")
        results, error_type = self._call_with_retry(
            lambda: self.client.images.generate(
                model=self.model_name,
                prompt=prompt,
                size=size,
                n=1,
                quality=IMAGE_QUALITY,
            ),
        )
        return (results[0] if results else None, error_type)
