import re
import json
from datetime import datetime, timed<PERSON><PERSON>

from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from fake_useragent import UserAgent

from config.config import PLAYWRIGHT_HEADLESS, USE_PROXY
from omni.log.log import olog
from .proxy_server import ProxyServer
from .utils import convert_mongo_cookies_to_playwright

"""
检查小红书创作者平台是否已登录
"""

def check_xhs_login_status(mongo_cookies: list[dict]) -> bool:
    """
    检查小红书创作者平台是否已登录。

    Args:
        mongo_cookies: 从 MongoDB 获取的原始 Cookie 列表。

    Returns:
        如果已登录则返回 True，否则返回 False。
    """
    # 直接访问登录页面
    target_url = 'https://creator.xiaohongshu.com/login'
    # 未登录页面的登录框容器选择器 (基于用户提供的HTML)
    login_page_indicator_selector = 'div.login-box-container'

    olog.info("开始检查小红书登录状态...")

    with sync_playwright() as p:
        browser_options = {'headless': PLAYWRIGHT_HEADLESS}
        context_options = {}

        if USE_PROXY:
            proxy_manager = ProxyServer()
            proxy_info = proxy_manager.get_proxy_server()
            if proxy_info:
                context_options["proxy"] = {
                    "server": proxy_info["server"],
                    "username": proxy_info["username"],
                    "password": proxy_info["password"]
                }
                olog.info(f"检查登录状态时使用代理: {proxy_info['server']}")
            else:
                olog.warning("配置了使用代理但无法获取代理信息，将不使用代理。")
        else:
            olog.info("未配置使用代理。")

        try:
            browser = p.chromium.launch(**browser_options)
            ua = UserAgent(browsers=['Chrome', 'Firefox'])
            context = browser.new_context(
                user_agent=ua.random,
                viewport={'width': 1920, 'height': 1080},
                **context_options
            )

            playwright_cookies = []
            if mongo_cookies:
                olog.info("转换并添加 Cookies 用于检查登录状态...")
                try:
                    playwright_cookies = convert_mongo_cookies_to_playwright(mongo_cookies)
                    context.add_cookies(playwright_cookies)
                except Exception as cookie_error:
                    olog.error(f"转换或添加 Cookie 时出错: {cookie_error}，判定为未登录。")
                    browser.close()
                    return False
            else:
                olog.warning("未提供 Cookies，无法检查登录状态，判定为未登录。")
                browser.close()
                return False

            page = context.new_page()
            olog.info(f"正在导航到: {target_url} 以检查登录状态")
            # 导航到登录页
            page.goto(target_url, wait_until='domcontentloaded', timeout=30000)
            # 等待页面加载或可能的重定向
            page.wait_for_timeout(5000) # 等待 5 秒观察页面变化

            # 使用 is_on_login_page 函数进行检查
            if is_on_login_page(page):
                olog.info("检测到登录页面，判定为未登录状态。")
                browser.close()
                return False
            else:
                olog.info(f"当前页面不是登录页（URL: {page.url}），判定为已登录状态。")
                browser.close()
                return True

        except PlaywrightTimeoutError as pte:
            olog.error(f"导航到登录页或等待超时: {pte}，判定为未登录。")
            if 'browser' in locals() and browser.is_connected():
                browser.close()
            return False
        except Exception as e:
            olog.error(f"检查登录状态期间发生其他错误: {e}")
            if 'browser' in locals() and browser.is_connected():
                browser.close()
            return False # 出错时，保守地认为未登录


def is_on_login_page(page) -> bool:
    """
    检查给定的 Playwright Page 对象当前是否在小红书创作者平台的登录页面。

    Args:
        page: 已导航的 Playwright Page 对象。

    Returns:
        如果页面是登录页则返回 True，否则返回 False。
    """
    login_page_indicator_selector = 'div.login-box-container'
    try:
        current_url = page.url
        olog.debug(f"检查页面是否为登录页，当前 URL: {current_url}")

        if "/login" in current_url:
            # 检查登录框是否存在且可见
            try:
                login_box = page.locator(login_page_indicator_selector)
                if login_box.is_visible(timeout=5000): # 使用一个合理的超时时间
                    olog.info(f"URL 包含 '/login' 且登录框可见，判定为登录页。")
                    return True
                else:
                    olog.warning(f"URL 包含 '/login' 但登录框不可见。可能页面未完全加载或状态特殊，判定为非登录页。")
                    return False
            except PlaywrightTimeoutError:
                olog.warning(f"URL 包含 '/login' 但等待登录框可见超时。判定为非登录页。")
                return False
            except Exception as vis_err:
                olog.error(f"检查登录框可见性时出错: {vis_err}，判定为非登录页。")
                return False
        else:
            # URL 不包含 /login，肯定不是登录页
            olog.info(f"当前 URL ({current_url}) 不包含 '/login'，判定为非登录页。")
            return False
    except Exception as e:
        olog.error(f"检查页面是否为登录页时发生错误: {e}")
        return False # 出错时保守判定为非登录页
