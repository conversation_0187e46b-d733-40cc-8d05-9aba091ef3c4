# 定时任务说明

## 爬虫触发任务 (Crawler Triggering Tasks)

**1. `account_crawling_trigger_scheduler.py`**
   - **作用**: 定期获取所有状态为"在线"的账号ID，发布到 Redis Set (`XHS_ACCOUNTS_CRAWL_SET`) 中，以触发后续针对这些账号的笔记互动数据（如点赞、阅读、评论等）的爬虫任务。
   - **执行时间 (Cron)**: `hour='*/2', minute='0'` (每偶数小时整点)
   - **是否爬虫相关**: 是 (触发笔记互动数据爬虫)

**2. `account_online_status_scheduler.py`**
   - **作用**: 定期获取所有账户 ID，发布到 Redis Set (`XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET`) 中，触发后续的爬虫服务来验证这些账户的在线状态。
   - **执行时间 (Cron)**: `hour='3', minute='0'` (每日凌晨 3 点)
   - **是否爬虫相关**: 是 (触账号发在线状态检查爬虫)

## 数据计算与聚合任务 (Data Calculation & Aggregation Tasks)

**3. `adv_product_traffic_scheduler.py`**
   - **作用**: 定时统计每个产品的总流量数据（基于数据库中已有的 `PromotionTaskDetail` 数据），并更新或创建对应的 `ProductTraffic` 记录。
   - **执行时间 (Cron)**: `hour='*/2', minute='0'` (每偶数小时整点)
   - **是否爬虫相关**: 否 (聚合数据库数据，不直接触发爬虫)

**4. `daily_task_revenue_scheduler.py`**
   - **作用**: 定时计算每个推广任务在前一天的浏览量增量和对应收益 (基于数据库中已有的 `AccountMetrics` 数据，考虑上海时区)，并记录到数据库。
   - **执行时间 (Cron)**: `hour='7', minute='0'` (每日早上 7 点)
   - **是否爬虫相关**: 否 (基于数据库数据进行计算，不直接触发爬虫)

**6. `captain_daily_revenue_scheduler.py`**
   - **作用**: 定时计算并记录所有舰长基于其名下舰员当日任务收益所获得的佣金（基于 `DailyTaskRevenue` 数据计算，更新 `CaptainDailyRevenue` 表）。
   - **执行时间 (Cron)**: `hour='7', minute='0'` (每日早上 7 点)
   - **是否爬虫相关**: 否 (基于数据库数据进行计算，不直接触发爬虫)

## 数据同步与更新任务 (Data Synchronization & Update Tasks)

**5. `task_metrics_scheduler.py`**
   - **作用**: 定期更新推广任务详情 (`PromotionTaskDetail`) 的各项指标（浏览量、点赞数等），数据来源于数据库中的 `AccountMetrics` (该表数据由爬虫填充)。
   - **执行时间 (Cron)**: `hour='1-23/2', minute='0'` (每奇数小时整点)
   - **是否爬虫相关**: 否 (消费爬虫产生的数据，不直接触发爬虫)
