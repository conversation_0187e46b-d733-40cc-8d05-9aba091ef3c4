/**
 * 语音识别管理器 (ASR Manager)
 *
 * 提供录音、上传和语音识别的完整解决方案。
 *
 * 使用方法:
 * 1. 导入管理器:
 *    import ASRManager from '@/core/tools/asr-manager';
 *
 * 2. 开始录音:
 *    await ASRManager.startRecording();
 *
 * 3. 停止录音并获取识别结果:
 *    const result = await ASRManager.stopRecordingAndRecognize();
 *
 * 4. 销毁实例（在组件卸载时调用）:
 *    ASRManager.destroy();
 *
 * 注意事项:
 * - 使用前需确保浏览器支持音频录制功能
 * - 首次使用时会请求麦克风权限
 * - 录音格式为 WAV，采样率 16kHz，位深 16bit
 * - 本管理器为单例模式，全局共用一个实例
 */
import Recorder from 'recorder-core';
import 'recorder-core/src/engine/wav';
import fileManager from '@/core/api/cos-api';
import api from '@/core/api/api';
import {TMP_PARENT_KEY} from "@/config";

class AsrManager {
    constructor() {
        this.recorder = null;
        this.isRecording = false;
        this.uploadPath = TMP_PARENT_KEY;
        this.maxRecordingDuration = 60000; // 最大录音时长（毫秒）
        this.recordingTimeout = null;
    }

    /**
     * 初始化录音机
     */
    async initRecorder() {
        if (this.recorder) {
            this.destroy();
        }

        return new Promise((resolve, reject) => {
            this.recorder = Recorder({
                type: 'wav',
                sampleRate: 16000,
                bitRate: 16
            });

            this.recorder.open(
                () => resolve(),
                (msg, isUserNotAllow) => {
                    const error = isUserNotAllow ? '用户拒绝了麦克风权限' : '无法打开麦克风：' + msg;
                    reject(new Error(error));
                }
            );
        });
    }

    /**
     * 开始录音
     */
    async startRecording() {
        if (this.isRecording) {
            throw new Error('录音已经在进行中');
        }

        await this.initRecorder();
        this.recorder.start();
        this.isRecording = true;

        // 添加录音时长限制
        this.recordingTimeout = setTimeout(() => {
            if (this.isRecording) {
                this.stopRecordingAndRecognize()
                    .catch(error => console.error('自动停止录音失败:', error));
            }
        }, this.maxRecordingDuration);
    }

    /**
     * 停止录音并进行语音识别
     */
    async stopRecordingAndRecognize() {
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }
        if (!this.isRecording || !this.recorder) {
            throw new Error('没有正在进行的录音');
        }

        return new Promise((resolve, reject) => {
            this.recorder.stop(async (blob) => {
                this.isRecording = false;
                try {
                    const result = await this.processRecording(blob);
                    this.recorder.close();
                    this.recorder = null;
                    resolve(result);
                } catch (error) {
                    this.recorder.close();
                    this.recorder = null;
                    reject(error);
                }
            }, (msg) => {
                this.isRecording = false;
                reject(new Error('录音停止失败：' + msg));
            });
        });
    }

    /**
     * 处理录音文件并进行识别
     */
    async processRecording(blob) {
        if (!blob || blob.size === 0) {
            throw new Error('录音数据为空');
        }

        try {
            const fileKey = await fileManager.uploadFile(
                new File([blob], `record_${Date.now()}.wav`, {type: 'audio/wav'}),
                this.uploadPath
            );

            const result = await api({
                resource: 'asr',
                method_name: 'long_text_recognition',
                file_key: fileKey
            });

            return result;
        } catch (error) {
            throw new Error(`语音识别处理失败: ${error.message}`);
        }
    }

    /**
     * 销毁录音实例
     */
    destroy() {
        if (this.recorder) {
            if (this.isRecording) {
                this.recorder.stop();
            }
            this.recorder.close();
            this.recorder = null;
        }
        this.isRecording = false;
    }

    isCurrentlyRecording() {
        return this.isRecording;
    }
}

// 创建单例
const ASRManager = new AsrManager();
export default ASRManager;
