import json
import re

from fastapi import APIRouter, Request, Form
from fastapi.responses import Response

from omni.log.log import olog
from omni.redis.redis_client import rc

callback_router = APIRouter()


@callback_router.post('/tencent-tts-callback')
async def tts_callback(request: Request, data: str = Form(None)):
    if not data:
        olog.error("回调数据为空")
        return Response(status_code=200)

    try:
        data_dict = json.loads(data)
    except json.JSONDecodeError:
        olog.error("JSON解析失败")
        return Response(status_code=200)

    task_id = data_dict.get('TaskId')
    if not task_id:
        olog.error("TaskId不存在")
        return Response(status_code=200)

    status = data_dict.get('Status')
    result_url = data_dict.get('ResultUrl')
    error_msg = data_dict.get('ErrorMsg')

    if status == 2:
        olog.info(f"TTS任务成功完成: {task_id}")
        result = {'status': 'success', 'error_msg': None, 'url': result_url}
        olog.debug(f"音频URL已存入Redis: {result_url}")
    else:
        olog.error(f"TTS任务失败: {task_id}, 状态: {status}")
        result = {'status': 'failed', 'error_msg': error_msg or f"任务状态异常: {status}", 'url': None}

    rc.set(f"tts:{task_id}", json.dumps(result))

    return Response(status_code=200)


@callback_router.post('/tencent-asr-callback')
async def asr_callback(request: Request, requestId: str = Form(None), code: str = Form(None), text: str = Form(None), message: str = Form(None)):
    if not requestId:
        olog.error("ASR回调数据中缺少requestId")
        return Response(status_code=200)

    try:
        code_int = int(code) if code else -1
    except (TypeError, ValueError):
        olog.error(f"ASR回调状态码解析失败: {code}")
        code_int = -1

    if code_int == 0:  # 成功
        olog.info(f"ASR任务成功完成: {requestId}")
        raw_text = text
        cleaned_text = re.sub(r'\[\d+:\d+\.\d+,\d+:\d+\.\d+\]', '', raw_text).strip() if raw_text else raw_text
        result_data = {
            'status': 'success',
            'error_msg': None,
            'text': cleaned_text
        }
    else:  # 失败
        error_msg = message or '未知错误'
        olog.error(f"ASR任务失败: {requestId}, 错误码: {code_int}, 错误信息: {error_msg}")
        result_data = {
            'status': 'failed',
            'error_msg': error_msg,
            'text': None
        }

    rc.set(f"asr:{requestId}", json.dumps(result_data))
    return Response(status_code=200)
