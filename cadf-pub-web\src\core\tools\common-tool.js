export function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用 Clipboard API
            navigator.clipboard.writeText(text);
            return true;
        } else {
            // 回退方法：创建一个临时文本区域
            const textArea = document.createElement("textarea");
            textArea.value = text;

            // 使其不在视野内
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);

            textArea.focus();
            textArea.select();

            // 执行复制命令
            const successful = document.execCommand("copy");
            document.body.removeChild(textArea);
            return successful;
        }
    } catch (err) {
        console.error("Unable to copy to clipboard", err);
        return false;
    }
}

export function template(str, dict) {
    return str.replace(/\$\{(\w+)\}/g, (match, key) => {
        return dict.hasOwnProperty(key) ? dict[key] : match;
    });
}

export async function convertFilesToBase64(files) {
    const base64Array = await Promise.all(
        Array.from(files).map((file) => {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result.split(",")[1]); // 拿到base64编码（不含前缀）
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        })
    );

    return base64Array;
}

export async function convertBlobUrlsToBase64(blobUrls) {
    const base64Array = await Promise.all(
        blobUrls.map((blobUrl) => {
            return new Promise((resolve, reject) => {
                fetch(blobUrl)
                    .then((response) => response.blob())
                    .then((blob) => {
                        const reader = new FileReader();
                        reader.onloadend = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(blob);
                    })
                    .catch(reject);
            });
        })
    );

    return base64Array;
}
