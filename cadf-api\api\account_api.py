import time

from mongoengine.queryset.visitor import Q  # 导入 Q 对象用于复杂查询

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict, save_or_update
from repository.models import Account


@register_handler('account')
class AccountApi:
    @auth_required(['creator', 'admin'])
    def create(self, data):
        data['create_at'] = int(time.time())

        # 设置默认状态为 在线
        data['status'] = '在线'

        # 检查平台是否为 choices 中的值
        platform = data.get('platform')
        # 从模型定义中获取有效的 choices
        valid_platforms = getattr(Account.platform, 'choices', None)
        if valid_platforms and platform not in valid_platforms:
            valid_platforms_str = ", ".join(valid_platforms)
            raise MException(f"不支持的平台: {platform}，支持的平台有: {valid_platforms_str}")

        # 可以在这里添加一些默认值或验证，例如 name, homepage_url 等
        # 如果没有提供 name，可以尝试从 cookie 或其他信息生成一个默认名称
        if not data.get('name'):
            data['name'] = f"{data.get('platform', '未知平台')}账号 - {str(time.time())[-4:]}"  # 示例默认名

        id_ = save_or_update(Account, data)
        return {'id_': id_}

    @auth_required(['creator', 'admin'])
    def modify(self, data):
        id_ = data.get('id_')
        if not id_:
            raise MException("缺少必要参数 'id_'")

        existing_account = Account.objects(id=id_, user_id=data['user_id']).first()
        if not existing_account:
            raise MException("找不到要修改的账号或权限不足")

        # 允许修改的字段
        allowed_fields = ['name', 'platform', 'domain', 'cookie', 'status']
        update_data = {k: v for k, v in data.items() if k in allowed_fields}

        # 特殊处理 platform 的校验 (如果提供了 platform)
        if 'platform' in update_data:
            platform = update_data['platform']
            valid_platforms = getattr(Account.platform, 'choices', None)
            if valid_platforms and platform not in valid_platforms:
                valid_platforms_str = ", ".join(valid_platforms)
                raise MException(f"不支持的平台: {platform}，支持的平台有: {valid_platforms_str}")

        # 如果没有提供任何可修改的字段
        if not update_data:
            print("未提供任何需要修改的字段")
            return {'id_': id_}  # 或者抛出异常，取决于业务逻辑

        # 使用 update_one 更新指定字段
        result = Account.objects(id=id_, user_id=data['user_id']).update_one(upsert=False, **{f'set__{k}': v for k, v in update_data.items()})

        if result == 0:
            # 理论上前面已经检查过存在性，这里再检查一次以防万一
            raise MException("更新账号信息失败")

        return {'id_': id_}

    @auth_required(['creator', 'admin'])
    def delete(self, data):
        id_ = data.get('id_')
        if not id_:
            raise MException("缺少必要参数 'id_'")

        deleted_count = Account.objects(id=id_, user_id=data['user_id']).delete()
        if deleted_count == 0:
            raise MException("找不到要删除的账号或权限不足")

    @auth_required(['creator', 'admin'])
    def query_one(self, data):
        id_ = data.get('id_')
        if not id_:
            raise MException("缺少必要参数 'id_'")

        account = Account.objects(id=id_, user_id=data['user_id']).first()
        if not account:
            raise MException("找不到指定的账号或权限不足")
        return doc_to_dict(account)

    @auth_required(['creator', 'admin'])
    def query_all(self, data):
        user_id = data.get('user_id')
        platform_filter = data.get('platform')  # 平台筛选
        # 移除 searchQuery 和分页参数

        # 构建查询条件
        query_filters = Q(user_id=user_id)
        if platform_filter:
            query_filters &= Q(platform=platform_filter)

        # 获取所有符合条件的账户列表
        accounts = Account.objects(query_filters).order_by('-create_at')
        # 获取总数
        total_count = accounts.count()

        return {
            'accounts': docs_to_dict(accounts),
            'total_count': total_count
        }
