import api from "@/core/api/api";

export const pubPromotionTaskApi = {
    // query_product_overview, create_promotion_task, queryPromotionTasks 已移动到 adv-promotion-task-api.js

    // Add other API methods for PromotionTaskDetail here...
    // query_available_tasks is handled by PromotionTaskDetailApi in the backend now
    query_available_tasks: async (page = 0, limit = 10, domain_filter = null) => {
        return await api({
            resource: "promotion_task_detail", // Updated resource name
            method_name: "query_available_tasks",
            page,
            limit,
            domain_filter
        });
    }

    // Add other API methods for PromotionTaskDetail here...
    // e.g., accept_task: async (promotion_task_detail_id, account_id) => { ... }
};
