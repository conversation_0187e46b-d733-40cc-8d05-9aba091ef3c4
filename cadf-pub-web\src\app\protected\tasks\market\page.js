"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
    Alert, Box, Button, Chip, CircularProgress, Container, 
    Divider, Grid, LinearProgress, Pagination, Paper, Stack, Typography, 
    useMediaQuery, useTheme, CardContent, IconButton
} from '@mui/material';
import { 
    Clock, AlertTriangle, ChevronLeft, Home, 
    Tag, Monitor 
} from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useDispatch } from 'react-redux';
import { addAlert } from '@/core/components/redux/alert-slice';
import { AlertType } from '@/core/components/alert';
import NextLink from 'next/link';
import ProductCard from '@/components/ProductCard';

// Task Card component
function MarketTaskCard({ task, onClick, isMobile }) {
    return (
        <Grid item xs={12} sm={6} md={4} lg={3} key={task.id_}>
            <ProductCard 
                images={task.imageUrls} 
                // onClick={() => onClick(task)}
            >
                <CardContent sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <Typography 
                        variant="h6" 
                        gutterBottom 
                        sx={{ 
                            fontWeight: 500,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical',
                        }}
                    >
                        {task.title || '未命名任务'}
                    </Typography>
                    
                    <Stack direction="row" spacing={1} sx={{ mb: 1.5 }}>
                        {task.domain && (
                            <Chip 
                                icon={<Tag size={14} />}
                                label={task.domain} 
                                size="small" 
                                color={task.matchesDomainFilter ? "primary" : "default"}
                                sx={{ height: 24 }} 
                            />
                        )}
                        {task.platform && (
                            <Chip 
                                icon={<Monitor size={14} />}
                                label={task.platform} 
                                size="small" 
                                variant="outlined"
                                sx={{ height: 24 }} 
                            />
                        )}
                    </Stack>
                    
                    {task.deadline && task.deadline !== "N/A" && (
                        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1.5 }}>
                            <Clock size={14} color="text.secondary" />
                            <Typography variant="body2" color="text.secondary">
                                截止: {task.deadline}
                            </Typography>
                        </Stack>
                    )}
                    
                    {task.content && (
                        <Typography 
                            variant="body2" 
                            color="text.secondary" 
                            sx={{ 
                                mb: 2,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                lineHeight: 1.5
                            }}
                        >
                            {task.content}
                        </Typography>
                    )}
                    
                    <Box sx={{ mt: 'auto' }}>
                        <Button 
                            variant="contained" 
                            fullWidth 
                            disableElevation
                            onClick={(e) => { 
                                e.stopPropagation(); 
                                onClick(task); 
                            }} 
                            sx={{ 
                                borderRadius: 1.5,
                                textTransform: 'none',
                                fontWeight: 500
                            }}
                        >
                            接受任务
                        </Button>
                    </Box>
                </CardContent>
            </ProductCard>
        </Grid>
    );
}

function TaskMarketContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const searchParams = useSearchParams();
    const dispatch = useDispatch();

    const accountIdFromQuery = searchParams.get('accountId');
    const pageFromQuery = parseInt(searchParams.get('page') || '1', 10);

    const [accountInfo, setAccountInfo] = useState(null);
    const [loadingAccountInfo, setLoadingAccountInfo] = useState(!!accountIdFromQuery);
    const [tasksState, setTasksState] = useState({ tasks: [], totalCount: 0, loading: true, error: null });
    const [currentPage, setCurrentPage] = useState(pageFromQuery);
    const itemsPerPage = 8;

    useEffect(() => {
        setCurrentPage(parseInt(searchParams.get('page') || '1', 10));
    }, [searchParams]);

    useEffect(() => {
        if (accountIdFromQuery) {
            setAccountInfo(null);
            setLoadingAccountInfo(true);
            const fetchAccountDetails = async () => {
                try {
                    const response = await pubPromotionTaskApi.getAccountInfo(accountIdFromQuery);
                    if (response && response.account_info) {
                        setAccountInfo(response.account_info);
                    } else {
                        dispatch(addAlert({ type: AlertType.ERROR, message: "未能获取账户详细信息。" }));
                        setAccountInfo(null);
                    }
                } catch (err) {
                    console.error("获取账户信息失败:", err);
                    dispatch(addAlert({ type: AlertType.ERROR, message: `获取账户信息失败: ${err.message}` }));
                    setAccountInfo(null);
                }
                setLoadingAccountInfo(false);
            };
            fetchAccountDetails();
        } else {
            setAccountInfo(null);
            setLoadingAccountInfo(false);
        }
    }, [accountIdFromQuery, dispatch]);

    useEffect(() => {
        if (accountIdFromQuery && !loadingAccountInfo) {
            const fetchMarketTasks = async () => {
                setTasksState(prev => ({ ...prev, loading: true, error: null }));
                try {
                    const response = await pubPromotionTaskApi.queryMarketTasks(accountIdFromQuery, currentPage - 1, itemsPerPage);
                    if (response && response.market_tasks) {
                        let fetchedTasks = response.market_tasks.map(task => ({
                            ...task,
                            imageUrls: (Array.isArray(task.imageUrls) && task.imageUrls.length > 0 && !task.imageUrls.some(url=>!url || url.includes('placehold.co'))) 
                                ? task.imageUrls 
                                : ['https://placehold.co/300x400/e0f2fe/0c4a6e?text=Task'],
                        }));
                        setTasksState({ tasks: fetchedTasks, totalCount: response.total_count || 0, loading: false, error: null });
                    } else {
                        setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的任务市场数据' });
                    }
                } catch (error) {
                    console.error(`获取任务市场列表失败:`, error);
                    const errorMsg = `获取任务列表失败: ${error.message || '未知错误'}`;
                    setTasksState({ tasks: [], totalCount: 0, loading: false, error: errorMsg });
                    dispatch(addAlert({ type: AlertType.ERROR, message: errorMsg }));
                }
            };
            fetchMarketTasks();
        } else if (!accountIdFromQuery && !loadingAccountInfo) {
            setTasksState({ tasks: [], totalCount: 0, loading: false, error: null });
        }
    }, [accountInfo, accountIdFromQuery, dispatch, loadingAccountInfo, currentPage]);

    const handlePageChange = (event, value) => {
        setCurrentPage(value);
        const currentQuery = new URLSearchParams(searchParams.toString());
        currentQuery.set('page', value);
        router.push(`${router.pathname}?${currentQuery.toString()}`, { scroll: false });
    };

    const handleAcceptTaskAndNavigate = async (task) => {
        if (!accountIdFromQuery) {
            dispatch(addAlert({ type: AlertType.ERROR, message: "请先选择一个账户来接受任务。" }));
            return;
        }

        try {
            await pubPromotionTaskApi.acceptTask(task.id_, accountIdFromQuery);
            dispatch(addAlert({ type: AlertType.SUCCESS, message: "任务接取成功！正在跳转到任务详情..." }));

            const query = new URLSearchParams();
            query.append('accountId', accountIdFromQuery);
            router.push(`/protected/tasks/detail/${task.id_}?${query.toString()}`);

        } catch (error) {
            console.error("接取任务失败:", error);
            dispatch(addAlert({ type: AlertType.ERROR, message: `接取任务失败: ${error.message || '未知错误'}` }));
        }
    };

    let pageTitle = "任务市场";
    let pageSubtitle = "请选择一个账户以查看其专属任务市场和推荐任务。";
    if (loadingAccountInfo && accountIdFromQuery) {
        pageSubtitle = "正在加载账户信息以优化任务展示...";
    } else if (accountInfo) {
        pageTitle = `任务市场`;
        pageSubtitle = `匹配账户特性的任务已高亮显示。`;
    } else if (accountIdFromQuery && !loadingAccountInfo && !accountInfo) {
        pageSubtitle = "无法加载指定账户信息，请检查账户ID或稍后再试。";
    }

    return (
        <Container maxWidth="xl" sx={{ py: 4 }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Typography variant="caption" color="text.secondary" onClick={() => router.back()} sx={{cursor: 'pointer', '&:hover': {textDecoration: 'underline'}}}>
                    返回任务中心
                </Typography>
            </Stack>
            
            <Box sx={{ mb: 4 }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12}>
                        <Typography variant={isMobile ? "h5" : "h4"} component="h1" sx={{ fontWeight: 600 }}>
                            {pageTitle}
                        </Typography>
                        <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                            {pageSubtitle}
                        </Typography>
                    </Grid>
                </Grid>
            </Box>
            
            {(loadingAccountInfo || tasksState.loading) && (
                <LinearProgress sx={{ mb: 3, borderRadius: 1 }} />
            )}
            
            {tasksState.error && (
                <Alert 
                    severity="error" 
                    variant="outlined"
                    sx={{ 
                        mb: 3, 
                        borderRadius: 2 
                    }}
                >
                    {tasksState.error}
                </Alert>
            )}
            
            {!tasksState.loading && !tasksState.error && tasksState.tasks.length === 0 && (
                <Paper 
                    elevation={0} 
                    sx={{
                        p: 5,
                        textAlign: 'center',
                        borderRadius: 2,
                        backgroundColor: theme.palette.background.default,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <AlertTriangle size={48} style={{ margin: '0 auto 16px', color: theme.palette.text.secondary }} />
                    <Typography variant="h6" gutterBottom>
                        {accountIdFromQuery 
                            ? (accountInfo ? `暂无可展示的任务` : "无法加载任务，请检查账户信息") 
                            : "请选择账户以浏览任务"}
                    </Typography>
                    <Typography color="text.secondary" sx={{ maxWidth: 500, mx: 'auto' }}>
                        {accountIdFromQuery 
                            ? (accountInfo 
                                ? (tasksState.totalCount > 0 
                                    ? `当前条件下共有 ${tasksState.totalCount} 个候选任务。` 
                                    : "当前没有符合条件的任务可供接取，请稍后再试或检查账户设置。")
                                : "无法获取账户信息，任务无法展示。")
                            : "选择一个账户后，这里将展示为您推荐的任务。"}
                    </Typography>
                </Paper>
            )}

            {!tasksState.loading && !tasksState.error && tasksState.tasks.length > 0 && (
                <>
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" color="text.secondary">
                            共找到 {tasksState.totalCount} 个任务
                        </Typography>
                    </Box>
                    
                    <Grid container spacing={3}>
                        {tasksState.tasks.map((task) => (
                            <MarketTaskCard 
                                key={task.id_} 
                                task={task} 
                                onClick={handleAcceptTaskAndNavigate}
                                isMobile={isMobile} 
                            />
                        ))}
                    </Grid>
                    
                    {tasksState.totalCount > itemsPerPage && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                            <Pagination
                                count={Math.ceil(tasksState.totalCount / itemsPerPage)}
                                page={currentPage}
                                onChange={handlePageChange}
                                color="primary"
                                shape="rounded"
                                showFirstButton
                                showLastButton
                            />
                        </Box>
                    )}
                </>
            )}
        </Container>
    );
}

export default function TaskMarketPage() {
    return (
        <Suspense fallback={
            <Container maxWidth="xl" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
                <CircularProgress />
            </Container>
        }>
            <TaskMarketContent />
        </Suspense>
    );
} 