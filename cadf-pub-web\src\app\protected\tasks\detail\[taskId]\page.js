"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import {
    Alert, Box, Button, CardContent, Chip, CircularProgress, Container, Divider, Grid, IconButton,
    LinearProgress, Paper, Stack, TextField, Typography, useMediaQuery, useTheme, InputAdornment
} from '@mui/material';
import { Clock, ChevronLeft, ChevronRight, Home, Download, Copy, Link as LinkIcon, AlertTriangle, CheckCircle, Eye } from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useDispatch, useSelector } from 'react-redux';
import { addAlert } from '@/core/components/redux/alert-slice';
import { AlertType } from '@/core/components/alert';
import NextLink from 'next/link';



function TaskDetailDisplay({ taskData, isMobile, onCopyText, activeImageStep, onImageNext, onImageBack }) {
    const theme = useTheme();
    const imageUrls = taskData?.imageUrls || [];

    return (
        <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: 3,
            mb: 3
        }}>
            {/* 合并后的卡片 - 图片、标题、内容合一 */}
            <Paper 
                elevation={3} 
                sx={{ 
                    borderRadius: 3,
                    overflow: 'hidden',
                    bgcolor: 'background.paper',
                    boxShadow: '0 6px 16px rgba(0,0,0,0.08)'
                }}
            >
                {/* 图片区域 */}
                <Box sx={{ 
                    position: 'relative', 
                    bgcolor: 'grey.50', 
                    height: { xs: 380, sm: 480, md: 550 },
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%' // 确保外层Box占满宽度
                }}>
                    {imageUrls.length > 0 ? (
                        <>
                            <Box
                                component="img"
                                src={imageUrls[activeImageStep]}
                                alt={`${taskData.title} - image ${activeImageStep + 1}`}
                                onError={(e) => { e.target.onerror = null; e.target.src = 'https://placehold.co/600x400/f5f5f5/666666?text=Image+Not+Available'; }}
                                sx={{ 
                                    width: '100%', // 图片宽度占满
                                    height: '100%', // 高度也占满
                                    objectFit: 'contain', // 保持比例填充
                                    display: 'block' 
                                }}
                            />

                            {/* 长按下载提示 (仅手机端) */}
                            {isMobile && (
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        bottom: 16,
                                        left: 0,
                                        right: 0,
                                        textAlign: 'center',
                                        zIndex: 3,
                                        py: 1,
                                        px: 2,
                                        mx: 'auto',
                                        width: 'fit-content',
                                        bgcolor: 'rgba(0,0,0,0.6)',
                                        color: 'white',
                                        borderRadius: 4,
                                        fontSize: '0.75rem',
                                        backdropFilter: 'blur(4px)',
                                        boxShadow: 1,
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 1
                                    }}
                                >
                                    <Download size={14} />
                                    <Typography variant="caption">长按图片可下载保存</Typography>
                                </Box>
                            )}

                            {/* PC端下载按钮 (仅PC端) */}
                            {!isMobile && imageUrls.length > 0 && (
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        bottom: 16,
                                        right: 16, // 定位在右下角
                                        zIndex: 3,
                                    }}
                                >
                                    <IconButton
                                        onClick={() => {
                                            const link = document.createElement('a');
                                            link.href = imageUrls[activeImageStep];
                                            link.download = `image_${activeImageStep + 1}.${imageUrls[activeImageStep].split('.').pop()}`;
                                            document.body.appendChild(link);
                                            link.click();
                                            document.body.removeChild(link);
                                        }}
                                        sx={{
                                            bgcolor: 'rgba(255,255,255,0.8)',
                                            boxShadow: 2,
                                            '&:hover': { bgcolor: 'rgba(255,255,255,1)' }
                                        }}
                                        size="small"
                                    >
                                        <Download size={20} />
                                    </IconButton>
                                </Box>
                            )}
                        </>
                    ) : (
                        <Box sx={{ textAlign: 'center', p: 3 }}>
                            <Box 
                                component="img" 
                                src="https://placehold.co/600x400/f5f5f5/666666?text=No+Images+Available" 
                                alt="No Images" 
                                sx={{ maxWidth: '100%', maxHeight: 300 }}
                            />
                        </Box>
                    )}
                    {imageUrls.length > 1 && (
                        <>
                            <IconButton 
                                onClick={onImageBack} 
                                sx={{ 
                                    position: 'absolute', 
                                    top: '50%', 
                                    left: 16, 
                                    transform: 'translateY(-50%)', 
                                    bgcolor: 'background.paper', 
                                    boxShadow: 2,
                                    '&:hover': { bgcolor: 'background.paper', opacity: 0.9 } 
                                }} 
                                size="medium"
                            >
                                <ChevronLeft />
                            </IconButton>
                            <IconButton 
                                onClick={onImageNext} 
                                sx={{ 
                                    position: 'absolute', 
                                    top: '50%', 
                                    right: 16, 
                                    transform: 'translateY(-50%)', 
                                    bgcolor: 'background.paper', 
                                    boxShadow: 2,
                                    '&:hover': { bgcolor: 'background.paper', opacity: 0.9 } 
                                }} 
                                size="medium"
                            >
                                <ChevronRight />
                            </IconButton>
                            <Box sx={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                position: 'absolute', 
                                bottom: {xs: 48, sm: 55}, // 调整定位，让位于下载提示
                                left: 0, 
                                right: 0,
                                zIndex: 2
                            }}>
                                <Paper elevation={3} sx={{ 
                                    display: 'flex',
                                    p: 0.5,
                                    borderRadius: 5,
                                    bgcolor: 'rgba(255,255,255,0.7)',
                                    backdropFilter: 'blur(4px)'
                                }}>
                                    {imageUrls.map((_, index) => (
                                        <Box 
                                            key={index} 
                                            sx={{ 
                                                width: 8, 
                                                height: 8, 
                                                borderRadius: '50%', 
                                                mx: 0.5, 
                                                bgcolor: index === activeImageStep ? 'primary.main' : 'grey.400',
                                                cursor: 'pointer' 
                                            }} 
                                        />
                                    ))}
                                </Paper>
                            </Box>
                        </>
                    )}
                </Box>

                {/* 标题区域 */}
                <Box sx={{ 
                    p: { xs: 2.5, sm: 3 },
                    position: 'relative',
                    borderBottom: '1px solid',
                    borderColor: 'divider'
                }}>
                    {/* 标题左侧装饰 */}
                    <Box 
                        sx={{ 
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            bottom: 0,
                            width: 6,
                            background: theme.palette.primary.main
                        }} 
                    />
                    
                    {/* 标题栏 - 图文标题和复制按钮 */}
                    <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'space-between',
                        flexWrap: 'wrap', // 允许在小屏幕上换行
                        gap: 1, // 添加间距
                        pl: 2,
                        mb: 1.5
                    }}>
                        <Typography 
                            variant="subtitle1" 
                            sx={{ 
                                fontWeight: 'medium',
                                color: theme.palette.text.secondary,
                            }}
                        >
                            图文标题
                        </Typography>
                        
                        <Button 
                            onClick={() => onCopyText(taskData.title)} 
                            startIcon={<Copy size={16} />} 
                            variant="contained" 
                            color="primary"
                            size="small"
                            sx={{
                                borderRadius: 6,
                                textTransform: 'none',
                                py: 0.7,
                                px: 2,
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                fontSize: '0.85rem',
                                fontWeight: 500,
                                '&:hover': {
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                    background: theme.palette.primary.dark
                                }
                            }}
                        >
                            复制标题
                        </Button>
                    </Box>
                    
                    {/* 标题内容 - 单独一行 */}
                    <Box sx={{ 
                        pl: 2, 
                        width: '100%',
                        maxWidth: { 
                            xs: 'calc(100vw - 80px)', // 限制手机视图宽度
                            sm: '100%'
                        },
                        pr: 2 // 增加右侧边距，防止内容太靠近边缘
                    }}>
                        <Typography 
                            variant="subtitle1" 
                            sx={{ 
                                fontWeight: 'bold',
                                color: theme.palette.text.primary,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: 'block',
                            }}
                        >
                            {taskData.title || '任务标题加载失败'}
                        </Typography>
                    </Box>
                </Box>

                {/* 内容区域 */}
                <Box sx={{ p: 0 }}>
                    {/* 内容标题栏 */}
                    <Box sx={{ 
                        p: { xs: 2, sm: 3 },
                        borderBottom: 1,
                        borderColor: 'divider',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexWrap: 'wrap', // 允许在小屏幕上换行
                        gap: 1, // 添加间距
                    }}>
                        <Typography 
                            variant="subtitle1" 
                            sx={{ 
                                fontWeight: 'medium',
                                color: theme.palette.text.secondary,
                            }}
                        >
                            图文内容
                        </Typography>
                        
                        <Button 
                            onClick={() => onCopyText(taskData.content)} 
                            startIcon={<Copy size={16} />} 
                            variant="contained" 
                            color="primary"
                            size="small"
                            sx={{
                                borderRadius: 6,
                                textTransform: 'none',
                                py: 0.7,
                                px: 2,
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                fontSize: '0.85rem',
                                fontWeight: 500,
                                '&:hover': {
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                    background: theme.palette.primary.dark
                                }
                            }}
                        >
                            复制内容
                        </Button>
                    </Box>
                    
                    {/* 内容正文 */}
                    <Box sx={{ 
                        p: { xs: 3, sm: 3.5 },
                        height: 'calc(1.8em * 5)', // 5行文本的高度
                        position: 'relative',
                        overflow: 'hidden'
                    }}>
                        <Box sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            overflowY: 'auto',
                            p: { xs: 3, sm: 3.5 },
                            pt: 0,
                            pl: 0,
                            pr: { xs: 2, sm: 2 },
                            '&::-webkit-scrollbar': {
                                width: '6px',
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'rgba(0,0,0,0.2)',
                                borderRadius: '6px',
                            },
                        }}>
                            <Typography 
                                variant="body1"
                                sx={{
                                    whiteSpace: 'pre-wrap', 
                                    lineHeight: 1.8,
                                    color: theme.palette.text.primary,
                                }}
                            >
                                {taskData.content || '无详细任务要求。'}
                            </Typography>
                        </Box>
                    </Box>
                </Box>
            </Paper>
        </Box>
    );
}

function LinkSubmissionForm({ taskData, onSubmitLink, isSubmitting, currentLink, onLinkChange, validationError, onRefreshStatus, isRefreshing }) {
    const theme = useTheme();
    return (
        <Paper 
            elevation={2} 
            sx={{ 
                borderRadius: 2, 
                mb: 3,
                overflow: 'hidden'
            }}
        >
            <Box sx={{ 
                p: 2, 
                borderBottom: 1, 
                borderColor: 'divider',
                bgcolor: 'success.light',
                color: 'success.contrastText',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexWrap: 'wrap', // 允许在小屏幕上换行
                gap: 1 // 添加间距
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinkIcon size={20} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                        {taskData.publish_url ? '发布链接' : '提交发布链接'}
                    </Typography>
                </Box>
                {taskData.platform && (
                    <Chip
                        label={`${taskData.platform}`}
                        color="secondary"
                        size="small"
                        sx={{ 
                            height: 28, 
                            borderRadius: 2,
                            fontWeight: 'medium',
                            bgcolor: 'rgba(255,255,255,0.85)',
                            color: 'success.dark',
                            '& .MuiChip-label': { px: 1.5 }
                        }}
                    />
                )}
            </Box>
            <Box sx={{ p: 3, bgcolor: 'background.paper' }}>
                <TextField
                    fullWidth
                    label="发布链接 (例如: https://www.xiaohongshu.com/discovery/item/...)"
                    variant="outlined"
                    value={currentLink}
                    onChange={onLinkChange}
                    placeholder="请粘贴您发布的链接"
                    InputProps={{ 
                        startAdornment: (<InputAdornment position="start"><LinkIcon size={20} /></InputAdornment>),
                        sx: { borderRadius: 1.5 }
                    }}
                    sx={{ mb: 2 }}
                    error={!!validationError}
                    helperText={validationError || `请粘贴您在${taskData.platform || '指定平台'}发布的链接，确保链接可访问。`}
                />
                
                {taskData.validation_status && (
                    <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 1 }}>
                        <Chip
                            icon={taskData.validation_status === '成功' ? <CheckCircle size={16} /> : <AlertTriangle size={16} />}
                            label={`当前验证状态: ${taskData.validation_status}`}
                            color={
                                taskData.validation_status === '成功' ? 'success' :
                                taskData.validation_status === '失败' ? 'error' :
                                taskData.validation_status === '待验证' ? 'warning' : 'default'
                            }
                            variant={taskData.validation_status === '成功' ? 'filled' : 'outlined'}
                            size="medium"
                            sx={{ py: 1, height: 36, borderRadius: 4 }}
                        />
                        <Button
                            variant="outlined"
                            color="primary"
                            onClick={onRefreshStatus}
                            disabled={isRefreshing}
                            startIcon={isRefreshing ? <CircularProgress size={16} color="inherit" /> : <Clock size={16} />}
                            sx={{ 
                                borderRadius: 2,
                                py: 0.8,
                                px: 2,
                                textTransform: 'none',
                                fontWeight: 'medium',
                                flexShrink: 0
                            }}
                            size="small"
                        >
                            {isRefreshing ? '刷新中...' : '刷新状态'}
                        </Button>
                    </Box>
                )}
                
                {taskData.validation_status === '失败' && taskData.validation_details && (
                    <Alert severity="error" variant="outlined" sx={{ mb: 2, borderRadius: 1.5 }}>
                        <Typography variant="body2">
                            失败原因: {taskData.validation_details}
                        </Typography>
                    </Alert>
                )}
                
                <Button
                    variant="contained"
                    color="success"
                    onClick={onSubmitLink}
                    disabled={isSubmitting || !currentLink.trim()}
                    startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <LinkIcon />}
                    sx={{ 
                        borderRadius: 2,
                        py: 1.2,
                        px: 3,
                        textTransform: 'none',
                        fontWeight: 'medium'
                    }}
                    size="large"
                >
                    {isSubmitting ? '正在提交...' : (taskData.publish_url ? '重新提交' : '提交链接验证')}
                </Button>
            </Box>
        </Paper>
    );
}

function TaskDetailPageContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams(); // For reading URL query parameters
    const dispatch = useDispatch();


    const taskId = params.taskId; // This is PromotionTaskDetail ID

    const [taskData, setTaskData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isSubmittingLink, setIsSubmittingLink] = useState(false);
    const [publishLinkInput, setPublishLinkInput] = useState('');
    const [linkValidationError, setLinkValidationError] = useState('');
    const [activeImageStep, setActiveImageStep] = useState(0);
    const [isGivingUpTask, setIsGivingUpTask] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const fetchTask = async (currentTaskId) => {
        setLoading(true); setError(null);
        try {
            const response = await pubPromotionTaskApi.getTaskDetailForView(currentTaskId);
            if (response && response.task_detail) {
                setTaskData(response.task_detail);
                setPublishLinkInput(response.task_detail.publish_url || '');
            } else {
                throw new Error("未能获取有效的任务详情数据");
            }
        } catch (err) {
            console.error("获取任务详情失败:", err);
            setError(err.message || "获取任务详情失败，请重试。");
            dispatch(addAlert({ type: AlertType.ERROR, message: err.message || "获取任务详情失败" }));
        }
        setLoading(false);
    };

    useEffect(() => {
        if (taskId) {
            fetchTask(taskId);
        }
    }, [taskId, dispatch]);

    const handleRefreshStatus = async () => {
        setIsRefreshing(true);
        try {
            await fetchTask(taskId);
            dispatch(addAlert({ type: AlertType.SUCCESS, message: "状态已刷新" }));
        } catch (err) {
            console.error("刷新状态失败:", err);
            // 错误处理已在fetchTask中完成
        }
        setIsRefreshing(false);
    };

    const handleSubmitLink = async () => {
        if (!publishLinkInput.trim()) {
            setLinkValidationError("链接不能为空。");
            return;
        }
        setLinkValidationError('');
        setIsSubmittingLink(true);
        try {
            await pubPromotionTaskApi.updatePublishLink(taskId, publishLinkInput.trim());
            dispatch(addAlert({ type: AlertType.SUCCESS, message: "发布链接已提交成功！" }));
            fetchTask(taskId); // Re-fetch to get updated validation status
        } catch (err) {
            console.error("提交链接失败:", err);
            dispatch(addAlert({ type: AlertType.ERROR, message: `提交链接失败: ${err.message || '未知错误'}` }));
        }
        setIsSubmittingLink(false);
    };

    const handleGiveUpTask = async () => {
        setIsGivingUpTask(true);
        try {
            await pubPromotionTaskApi.giveUpTask(taskId);
            dispatch(addAlert({ type: AlertType.SUCCESS, message: "任务已成功放弃！" }));
            // 放弃任务成功后，重定向到账户任务页面
            router.push('/protected/tasks/account');
        } catch (err) {
            console.error("放弃任务失败:", err);
            dispatch(addAlert({ type: AlertType.ERROR, message: `放弃任务失败: ${err.message || '未知错误'}` }));
            setIsGivingUpTask(false);
        }
    };

    const copyTextToClipboard = (text) => {
        if (!text) return;

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(String(text))
                .then(() => dispatch(addAlert({ type: AlertType.SUCCESS, message: "已复制到剪贴板" })))
                .catch(err => {
                    console.warn("navigator.clipboard.writeText 失败, 尝试回退: ", err);
                    // 回退到 execCommand
                    fallbackCopyTextToClipboard(String(text));
                });
        } else {
            // 如果 navigator.clipboard API 不存在，直接使用回退
            fallbackCopyTextToClipboard(String(text));
        }
    };

    // 回退复制方法
    const fallbackCopyTextToClipboard = (text) => {
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 避免在屏幕上显示 textArea
        textArea.style.position = "fixed"; // 或者 'absolute'
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.width = "2em";
        textArea.style.height = "2em";
        textArea.style.padding = "0";
        textArea.style.border = "none";
        textArea.style.outline = "none";
        textArea.style.boxShadow = "none";
        textArea.style.background = "transparent";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                dispatch(addAlert({ type: AlertType.SUCCESS, message: "已复制到剪贴板 (回退)" }));
            } else {
                dispatch(addAlert({ type: AlertType.ERROR, message: "复制失败 (回退)" }));
            }
        } catch (err) {
            console.error("回退复制失败: ", err);
            dispatch(addAlert({ type: AlertType.ERROR, message: "复制操作不支持或失败" }));
        }

        document.body.removeChild(textArea);
    };

    const handleImageNext = () => setActiveImageStep(prev => (prev + 1) % (taskData?.imageUrls?.length || 1));
    const handleImageBack = () => setActiveImageStep(prev => (prev - 1 + (taskData?.imageUrls?.length || 1)) % (taskData?.imageUrls?.length || 1));

    // Derived state computations - must be before early returns.
    // Use optional chaining for safe access to taskData properties.
    const isTaskAssigned = !!taskData?.user_id;

    if (loading) return <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh', overflowX: 'hidden' }}><CircularProgress size={60} /></Container>;
    if (error) return <Container sx={{ py: 3 }}><Alert severity="error">{error}</Alert></Container>;
    if (!taskData) return <Container sx={{ py: 3 }}><Alert severity="info">未找到任务详情。</Alert></Container>;

    const pageModeTitle = "任务详情";

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 }, px: { xs: 2, sm: 3 }, overflowX: 'hidden' }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Typography variant="caption" color="text.secondary" onClick={() => router.back()} sx={{cursor: 'pointer', '&:hover': {textDecoration: 'underline'}}}>
                    返回
                </Typography>
            </Stack>

            <Typography variant={isMobile ? "h6" : "h5"} component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 2}}>
                {pageModeTitle}
            </Typography>

            <TaskDetailDisplay 
                taskData={taskData} 
                isMobile={isMobile} 
                onCopyText={copyTextToClipboard}
                activeImageStep={activeImageStep}
                onImageNext={handleImageNext}
                onImageBack={handleImageBack}
            />

            {/* 提交发布链接部分 */}
            <LinkSubmissionForm 
                taskData={taskData} 
                onSubmitLink={handleSubmitLink} 
                isSubmitting={isSubmittingLink} 
                currentLink={publishLinkInput} 
                onLinkChange={(e) => setPublishLinkInput(e.target.value)}
                validationError={linkValidationError}
                onRefreshStatus={handleRefreshStatus}
                isRefreshing={isRefreshing}
            />
            
            {/* 放弃任务部分 */}
            {taskData?.validation_status !== '成功' && (
            <Paper 
                elevation={2} 
                sx={{ 
                    borderRadius: 2, 
                    mb: 3,
                    overflow: 'hidden'
                }}
            >
                <Box sx={{ 
                    p: 2, 
                    borderBottom: 1, 
                    borderColor: 'divider',
                    bgcolor: 'error.light',
                    color: 'error.contrastText',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                }}>
                    <AlertTriangle size={20} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                        放弃任务
                    </Typography>
                </Box>
                <Box sx={{ p: 3, bgcolor: 'background.paper' }}>
                    <Box sx={{ 
                        mb: 2, 
                        p: 2, 
                        bgcolor: 'grey.50', 
                        borderRadius: 2, 
                        borderLeft: '4px solid', 
                        borderColor: 'warning.main' 
                    }}>
                        <Typography variant="body1">
                            如果您不想再继续此任务，可以选择放弃。放弃后任务将返回任务市场。
                        </Typography>
                    </Box>
                    

                    
                    <Button
                        variant="outlined"
                        color="error"
                        onClick={handleGiveUpTask}
                        disabled={isGivingUpTask}
                        startIcon={isGivingUpTask ? <CircularProgress size={20} color="inherit" /> : <AlertTriangle />}
                        sx={{ 
                            borderRadius: 2,
                            py: 1.2,
                            px: 3,
                            textTransform: 'none',
                            fontWeight: 'medium',
                            borderWidth: 2
                        }}
                        size="large"
                    >
                        {isGivingUpTask ? '正在放弃...' : '确认放弃任务'}
                    </Button>
                </Box>
            </Paper>
            )}
            
            {!isTaskAssigned && ( // Task free
                <Alert severity="info" variant="filled" sx={{mt:1, borderRadius: 2}}>此任务当前未被分配或已被放弃。您可以从任务市场或账户管理页面查找并接取新任务。</Alert>
            )}

        </Container>
    );
}

export default function TaskDetailPage() {
    return (
        <Suspense fallback={<Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh', overflowX: 'hidden' }}><CircularProgress size={60} /></Container>}>
            <TaskDetailPageContent />
        </Suspense>
    );
} 