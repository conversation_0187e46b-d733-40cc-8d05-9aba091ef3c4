from typing import Optional, List, Dict, Any

from langchain.schema import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent

from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog  # 导入日志模块


class AgentBuilder:
    def __init__(self):
        self.llm = None
        self.server_config = None
        self.prompt = None
        self.tags = []

    def with_llm(self, llm: Optional[str] = None) -> 'AgentBuilder':
        """
        设置语言模型（必填）
        
        Args:
            llm: 语言模型实例
            
        Returns:
            AgentBuilder实例
        """
        self.llm = llm
        return self

    def with_server_config(self, server_config: List[str]) -> 'AgentBuilder':
        """
        设置服务器配置（必填）
        
        Args:
            server_config: MCP服务器URL列表
            
        Returns:
            AgentBuilder实例
        """
        if not server_config:
            raise ValueError("server_config cannot be empty")
        self.server_config = server_config
        return self

    def with_prompt(self, prompt: str) -> 'AgentBuilder':
        """
        设置提示词（必填）
        
        Args:
            prompt: agent的提示词
            
        Returns:
            AgentBuilder实例
        """
        if not prompt:
            raise ValueError("prompt cannot be empty")
        self.prompt = ChatPromptTemplate.from_template(prompt)
        return self

    def add_tag(self, tag: str) -> 'AgentBuilder':
        """
        添加标签
        
        Args:
            tag: 要添加的标签
            
        Returns:
            AgentBuilder实例
        """
        if tag and tag not in self.tags:
            self.tags.append(tag)
        return self

    def _validate_params(self):
        """
        验证必要参数是否设置
        """
        if not self.server_config:
            raise ValueError("server_config must be set using with_server_config()")
        if not self.prompt:
            raise ValueError("prompt must be set using with_prompt()")

    def _get_ai_messages(self, messages_data: Dict[str, List[Dict[str, Any]]]) -> str:
        """
        从消息列表中获取所有AI消息的content，并用换行符连接

        Args:
            messages_data: 包含消息列表的字典数据

        Returns:
            str: 所有AI消息的content内容，用换行符连接
        """
        messages = messages_data.get("messages", [])
        ai_contents = []

        for message in messages:
            if isinstance(message, AIMessage) and message.content:
                ai_contents.append(message.content)

        return "\n\n".join(ai_contents)

    async def execute_async(self, **kwargs):
        """
        异步执行agent任务，支持流式返回结果
        
        Args:
            input: 输入参数字典，包含各种占位符
            
        Returns:
            agent执行结果，以AsyncIterator形式流式返回
        """
        self._validate_params()

        retry_count = 0
        max_retries = 3
        last_error = None

        while retry_count < max_retries:
            try:
                formatted_config = {
                    f"server_{i}": {"url": url, "transport": "sse"}
                    for i, url in enumerate(self.server_config)
                }

                async with MultiServerMCPClient(formatted_config) as client:
                    prompt_content = self.prompt.format_messages(**kwargs)[0].content
                    llm = ChatModelFactory.get_llm(self.llm)
                    agent = create_react_agent(
                        model=llm,
                        tools=client.get_tools(),
                    )

                    if self.tags:
                        agent.config = RunnableConfig(tags=self.tags)
                    result = await agent.ainvoke({"messages": prompt_content})
                    messages_str = self._get_ai_messages(result)
                    return messages_str
            except Exception as e:
                last_error = e
                retry_count += 1
                # 添加日志记录重试信息
                olog.warning(f"Agent执行失败，第{retry_count}次重试。错误信息: {str(e)}")
                if retry_count >= max_retries:
                    olog.error(f"Agent执行失败，已达到最大重试次数({max_retries})。最后错误: {str(e)}")
                    break
        raise last_error
