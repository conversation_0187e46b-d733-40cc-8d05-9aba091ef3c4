# -*- coding: utf-8 -*-
"""
asr:
  secret_id: AKID4A8vX0IPK6iC91lKiuwpX4RdeUBh2Sr5
  secret_key: BA3scHsJYcUDm8NSL38DC3nQnBTenskP
callback:
  asr_callback_url: https://xxx/tencent-asr-callback
"""
import json
import time
import traceback

from tencentcloud.asr.v20190614 import asr_client, models
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile

from omni.config.config_loader import config_dict
from omni.log.log import olog
from omni.redis.redis_client import rc


class TencentASR:
    _instance = None

    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = False

    @classmethod
    def getInstance(cls):
        if cls._instance is None:
            cls._instance = cls()
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        if self._initialized:
            return

        # 从配置中获取 secret_id 和 secret_key
        secret_id = config_dict['asr']['secret_id']
        secret_key = config_dict['asr']['secret_key']

        self.cred = credential.Credential(secret_id, secret_key)

        # 实例化 http 选项
        http_profile = HttpProfile()
        http_profile.endpoint = "asr.tencentcloudapi.com"

        # 实例化 client 选项
        self.client_profile = ClientProfile()
        self.client_profile.httpProfile = http_profile

        # 实例化 AsrClient
        self.client = asr_client.AsrClient(self.cred, "", self.client_profile)

        self._initialized = True

    def long_text_recognition(self, url, timeout=180):
        """
        长文本语音识别（异步方式，通过轮询Redis获取结果）
        Args:
            url: 音频URL
            timeout: 超时时间（秒），默认3分钟
        Returns:
            dict: 包含status、error_msg和text字段的识别结果
            None: 如果任务创建失败或超时
        """
        if not self._initialized:
            raise RuntimeError("TencentASR is not initialized. Call getInstance first.")

        try:
            # 实例化请求对象
            req = models.CreateRecTaskRequest()

            params = {
                "EngineModelType": "16k_zh_large",
                "ChannelNum": 1,  # 单声道
                "ResTextFormat": 0,  # 输出包含标点符号
                "SourceType": 0,  # 音频URL方式
                "Url": url,
                "CallbackUrl": config_dict['callback']['asr_callback_url']  # 使用配置文件中的回调URL
            }

            req.from_json_string(json.dumps(params))

            # 发送请求创建任务
            resp = self.client.CreateRecTask(req)
            resp_json = json.loads(resp.to_json_string())

            # 获取请求ID
            request_id = resp_json.get("Data", {}).get("TaskId")

            if not request_id:
                return None

            # 轮询等待结果
            start_time = time.time()
            while True:
                # 检查是否超时
                if time.time() - start_time > timeout:
                    olog.error(f"ASR task timeout: {request_id}")
                    return {
                        'status': 'failed',
                        'error_msg': 'Recognition timeout',
                        'text': None
                    }

                # 从Redis获取最新状态
                result = rc.get(f"asr:{request_id}")
                if result:
                    result = json.loads(result)
                    # 如果识别完成或失败，返回结果
                    if result['status'] in ['success', 'failed']:
                        return result

                # 继续等待
                time.sleep(0.1)

        except TencentCloudSDKException as err:
            olog.error(f"Create recognition task failed: {traceback.format_exc()}")
            return {
                'status': 'failed',
                'error_msg': str(err),
                'text': None
            }
