<!DOCTYPE html>
<html>
<head>
    <title>下载更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }

        #progress {
            width: 80%;
            height: 20px;
            background-color: #ddd;
            border-radius: 10px;
            overflow: hidden;
        }

        #bar {
            width: 0%;
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.5s ease-in-out;
        }

        #percent {
            margin-top: 10px;
        }
    </style>
</head>
<body>
<h3>正在下载更新...</h3>
<div id="progress">
    <div id="bar"></div>
</div>
<p id="percent">0%</p>
<script>
    const {ipcRenderer} = require('electron');
    const bar = document.getElementById('bar');
    const percent = document.getElementById('percent');

    ipcRenderer.on('download-progress', (event, progress) => {
        bar.style.width = `${progress}%`;
        percent.textContent = `${progress.toFixed(2)}%`;
    });
</script>
</body>
</html>