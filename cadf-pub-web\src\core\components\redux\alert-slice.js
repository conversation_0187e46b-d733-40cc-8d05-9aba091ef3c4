import {createSlice} from '@reduxjs/toolkit';


let nextId = 0;

export const alertSlice = createSlice({
    name: 'alert',
    initialState: {
        alerts: []
    },
    reducers: {
        addAlert: (state, action) => {
            const newAlert = {
                id: nextId++,
                type: action.payload.type,
                message: action.payload.message
            };
            state.alerts.push(newAlert);
        },
        removeAlert: (state, action) => {
            state.alerts = state.alerts.filter(alert => alert.id !== action.payload);
        }
    },
});
const alertReducer = alertSlice.reducer;
export const {addAlert, removeAlert} = alertSlice.actions;
export default alertReducer;
