import api from "@/core/api/api";

export const userApi = {
    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise<{access_token: string}>} 登录结果
     */
    login: async (username, password) => {
        return await api({
            resource: "user",
            method_name: "login",
            username,
            password,
        });
    },

    /**
     * 用户注册
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @param {string} [crewInviteCode] - 舰队邀请码 (可选)
     * @returns {Promise<{message: string, access_token: string, roles: string[], user_id: string}>} 注册结果
     */
    register: async (username, password, crewInviteCode) => {
        // 调用后端 user/creator_register 方法进行注册
        return await api({
            resource: "user",
            method_name: "creator_register",
            username,
            password,
            crew_invite_code: crewInviteCode, // 传递舰队邀请码
        });
    },

    /**
     * 获取用户角色
     * @returns {Promise<{roles: string[]}>} 用户角色列表
     */
    getRoles: async () => {
        return await api({
            resource: "user",
            method_name: "get_roles",
        });
    },
};