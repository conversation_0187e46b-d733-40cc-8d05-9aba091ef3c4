import api from "@/core/api/api";

export const crewManagementApi = {
    getInviteCode: async () => {
        return await api({
            resource: "crew_management",
            method_name: "get_invite_code",
        });
    },
    generateInviteCode: async () => {
        return await api({
            resource: "crew_management",
            method_name: "generate_invite_code",
        });
    },
    // 新增：获取舰员统计数据
    getCrewStats: async () => {
        return await api({
            resource: "crew_management",
            method_name: "get_crew_stats",
        });
    },
    // 新增：获取舰员收益排名 - 修改参数传递方式
    getCrewRevenueRanking: async (page = 1, rowsPerPage = 10) => {
        return await api({
            resource: "crew_management",
            method_name: "get_crew_revenue_ranking",
            page, // 直接传递参数
            rowsPerPage, // 直接传递参数
        });
    },
    // 新增：获取舰员列表 (在线/离线/全部) - 修改参数传递方式
    getCrewList: async (status, page = 1, rowsPerPage = 10) => {
        return await api({
            resource: "crew_management",
            method_name: "get_crew_list",
            status, // 直接传递参数
            page, // 直接传递参数
            rowsPerPage, // 直接传递参数
        });
    },
    // 你可能还需要添加获取舰员列表、详情等API...
    // 新增：根据活跃度获取活跃舰员列表
    getActiveCrewList: async (page = 1, rowsPerPage = 10) => {
        return await api({
            resource: "crew_management",
            method_name: "get_activity_based_crew_list",
            activity_status: "active",
            page,
            rowsPerPage,
        });
    },
    // 新增：根据活跃度获取不活跃舰员列表
    getInactiveCrewList: async (page = 1, rowsPerPage = 10) => {
        return await api({
            resource: "crew_management",
            method_name: "get_activity_based_crew_list",
            activity_status: "inactive",
            page,
            rowsPerPage,
        });
    },
    // 新增：获取舰员总数
    getCrewCount: async () => {
        return await api({
            resource: "crew_management",
            method_name: "get_crew_count",
        });
    },
    // 新增：加入舰队
    joinFleet: async (fleetCode) => {
        return await api({
            resource: "crew_management",
            method_name: "join_fleet",
            fleet_code: fleetCode, // 将舰队代码作为参数传递
        });
    }
};
