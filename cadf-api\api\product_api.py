import time

from mongoengine.queryset.visitor import Q

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.log.log import olog
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict, save_or_update
from repository.models import Product, User


@register_handler('product')
class ProductApi:
    @auth_required(['admin', 'advertiser'])
    def create(self, data: dict):

        if not User.objects(id=data['user_id']).first():
            raise MException(f"用户ID '{data['user_id']}' 不存在")

        data_to_save = data.copy()
        data_to_save['create_at'] = int(time.time())

        id_ = save_or_update(Product, data_to_save)
        return {'id_': id_}

    @auth_required(['admin', 'advertiser'])
    def modify(self, data: dict):
        id_ = data['id_']

        product = Product.objects(id=id_).first()
        if not product:
            raise MException(f"产品ID '{id_}' 不存在")

        save_or_update(Product, data, id_)
        return {'id_': id_}

    @auth_required(['admin', 'advertiser'])
    def delete(self, data: dict):
        id_ = data.get('id_')
        if not id_:
            raise MException("缺少必要的参数 'id_'")

        product = Product.objects(id=id_, is_deleted=False).first()
        if not product:
            raise MException(f"产品ID '{id_}' 不存在或已被删除")
        product.is_deleted = True
        product.save()
        return {'message': '产品删除成功'}

    @auth_required(['admin', 'advertiser'])
    def query_one(self, data: dict):
        id_ = data.get('id_')
        user_id = data.get('user_id')
        if not id_:
            raise MException("缺少必要的参数 'id_'")
        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        product = Product.objects(id=id_, user_id=user_id, is_deleted=False).first()
        if not product:
            raise MException(f"未找到 ID 为 '{id_}' 的产品或无权查看")

        product_dict = doc_to_dict(product)
        oss_client = OSSClient.getInstance()
        if product_dict.get('images'):
            for img_info in product_dict['images']:
                if img_info.get('oss_key'):
                    try:
                        img_info['url'] = oss_client.signed_url(SignedMethod.GET, img_info['oss_key'])
                    except Exception as e:
                        olog.error(f"Error signing URL for key {img_info['oss_key']}: {e}")
                        img_info['url'] = None  # 或者一个默认错误图片URL
                else:
                    img_info['url'] = None  # 或者默认图片URL

        return product_dict

    @auth_required(['admin', 'advertiser'])
    def query_all(self, data: dict):
        page = data.get('page', 0)
        limit = data.get('limit', 5)
        search_filter = data.get('search')
        user_id = data.get('user_id')

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        try:
            page = int(page)
            limit = int(limit)
            if page < 0 or limit <= 0:
                raise ValueError("页码和限制必须为非负数。")
        except (ValueError, TypeError):
            raise MException("分页参数 'page' 和 'limit' 必须是有效的非负整数。")

        mongo_query = Q(user_id=user_id) & Q(is_deleted=False)
        if search_filter:
            mongo_query &= Q(title__icontains=search_filter)

        products_query = Product.objects(mongo_query)

        total_count = products_query.count()

        skip = page * limit

        paginated_products = products_query.order_by('-create_at').skip(skip).limit(limit)
        products_list = docs_to_dict(paginated_products)

        oss_client = OSSClient.getInstance()
        for product_dict in products_list:
            if product_dict.get('images'):
                for img_info in product_dict['images']:
                    if img_info.get('oss_key'):
                        try:
                            img_info['url'] = oss_client.signed_url(SignedMethod.GET, img_info['oss_key'])
                        except Exception as e:
                            olog.error(f"Error signing URL for key {img_info['oss_key']}: {e}")
                            img_info['url'] = None
                    else:
                        img_info['url'] = None

        return {
            'products': products_list,
            'total_count': total_count
        }
