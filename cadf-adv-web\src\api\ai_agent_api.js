import api from "@/core/api/api";

export const aiAgentApi = {
    generateDescription: async (imageUrls) => {
        return await api({
            resource: "ai_agent",
            method_name: "generate_description",
            image_urls: imageUrls, // 参数名与Python对应
        });
    },

    identifyProductName: async (imageUrls) => {
        return await api({
            resource: "ai_agent",
            method_name: "identify_product_name", // 对应后端方法名
            image_urls: imageUrls,
        });
    },

    identifyAndSaveDomains: async (imageUrls) => {
        return await api({
            resource: "ai_agent",
            method_name: "identify_and_save_domains",
            image_urls: imageUrls, // 参数名与Python对应
        });
    },
};
