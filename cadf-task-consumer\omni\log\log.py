"""
local_dir_path:
  log: /workspace/aqua/log
"""
import logging
import socket
from pathlib import Path

import colorlog

from omni.config.config_loader import config_dict


class ColorLogger:
    def __init__(self):
        folder_path = config_dict['local_dir_path']['log']
        process_env = config_dict['process_env']
        hostname = socket.gethostname()

        Path(folder_path).mkdir(parents=True, exist_ok=True)

        file_formatter = logging.Formatter(
            f"%(asctime)s | {hostname} | %(threadName)s | %(levelname)s | %(message)s",
            datefmt='%Y-%m-%d/%H:%M:%S'
        )

        # 为控制台输出添加颜色格式化器
        console_formatter = colorlog.ColoredFormatter(
            f"%(log_color)s%(asctime)s | {hostname} | %(threadName)s | %(levelname)s | %(message)s%(reset)s",
            datefmt='%Y-%m-%d/%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
            secondary_log_colors={},
            style='%'
        )

        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(logging.DEBUG)

        debug_file_handler = logging.FileHandler(f'{folder_path}/debug_log.txt', encoding='utf-8')
        debug_file_handler.setFormatter(file_formatter)
        debug_file_handler.setLevel(logging.DEBUG)

        info_file_handler = logging.FileHandler(f'{folder_path}/info_log.txt', encoding='utf-8')
        info_file_handler.setFormatter(file_formatter)
        info_file_handler.setLevel(logging.INFO)

        warning_file_handler = logging.FileHandler(f'{folder_path}/warning_log.txt', encoding='utf-8')
        warning_file_handler.setFormatter(file_formatter)
        warning_file_handler.setLevel(logging.WARNING)

        error_file_handler = logging.FileHandler(f'{folder_path}/error_log.txt', encoding='utf-8')
        error_file_handler.setFormatter(file_formatter)
        error_file_handler.setLevel(logging.ERROR)

        self.logger = logging.getLogger('mlog')
        self.logger.addHandler(error_file_handler)
        self.logger.addHandler(warning_file_handler)
        self.logger.addHandler(info_file_handler)
        self.logger.addHandler(debug_file_handler)
        if process_env == 'dev':
            self.logger.addHandler(console_handler)
        self.logger.setLevel(logging.DEBUG)


_color_logger = ColorLogger()
olog = _color_logger.logger
