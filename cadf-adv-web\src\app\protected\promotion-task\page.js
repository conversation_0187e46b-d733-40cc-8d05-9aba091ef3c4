"use client"

import {useEffect, useState} from 'react'
import {<PERSON>ert, Box, Button, Chip, CircularProgress, Container, IconButton, LinearProgress, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography} from '@mui/material'
import {useTheme} from '@mui/material/styles'
import {Eye, Plus, Trash2} from 'lucide-react'
import {useRouter} from 'next/navigation'
import {advPromotionTaskApi} from '@/api/adv-promotion-task-api'
import ConfirmationDialog from '@/components/ConfirmationDialog'; // Import the dialog

// Removed mock data

export default function ProductPromotion() {
    const theme = useTheme()
    const router = useRouter()
    const [page, setPage] = useState(1) // MUI Pagination is 1-based
    const rowsPerPage = 10 // More tasks per page

    // --- State for API data --- 
    const [tasks, setTasks] = useState([]); // Holds PromotionTask list
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // --- State for deletion --- 
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [taskIdToDelete, setTaskIdToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false); // Optional: for disabling buttons during delete

    // --- Fetch Tasks Function --- 
    const fetchTasks = async (pageIndex = 0) => {
        setLoading(true);
        setError(null);
        try {
            const response = await advPromotionTaskApi.queryPromotionTasks(
                pageIndex,
                rowsPerPage,
            );
            if (response && response.tasks) {
                setTasks(response.tasks);
                setTotalCount(response.total_count || 0);
            } else {
                setTasks([]);
                setTotalCount(0);
                console.warn("Unexpected API response structure:", response)
            }
        } catch (err) {
            console.error("Failed to fetch tasks:", err);
            const errorMsg = err?.response?.data?.message || err.message || "获取任务列表失败";
            setError(errorMsg);
            setTasks([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    };

    // --- useEffect to fetch data based on page --- 
    useEffect(() => {
        fetchTasks(page - 1); // Fetch based on page index only
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [page]); // Re-fetch only when page changes

    const handlePageChange = (event, newPage) => {
        setPage(newPage)
    }

    const handleCreateNewTask = () => {
        router.push('/protected/promotion-task/create') // Navigate to create page
    }

    // Updated: Opens the confirmation dialog
    const handleDeleteTask = (taskId) => {
        setTaskIdToDelete(taskId);
        setOpenConfirmDialog(true);
    }

    // New: Executes the deletion after confirmation
    const confirmDeleteTask = async () => {
        if (!taskIdToDelete) return;

        setOpenConfirmDialog(false);
        setIsDeleting(true);
        setError(null); // Clear previous errors

        try {
            await advPromotionTaskApi.deletePromotionTask(taskIdToDelete);
            // Optionally add a success notification here if needed
            // Re-fetch tasks for the current page
            await fetchTasks(page - 1);
        } catch (err) {
            console.error("Failed to delete task:", err);
            const errorMsg = err?.response?.data?.message || err.message || "删除任务失败";
            setError(errorMsg); // Show error in the main error display area
            // Optionally add an error notification here
        } finally {
            setIsDeleting(false);
            setTaskIdToDelete(null);
        }
    };

    const totalPages = Math.ceil(totalCount / rowsPerPage);

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{mb: 4}}
            >
                <Typography variant="h4" fontWeight="bold">
                    推广任务中心
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    startIcon={<Plus size={20}/>}
                    onClick={handleCreateNewTask}
                    sx={{
                        px: 4,
                        borderRadius: 2,
                        textTransform: "none",
                        fontWeight: "medium",
                    }}
                >
                    创建新任务
                </Button>
            </Stack>

            <Paper
                elevation={0}
                sx={{
                    mb: 4,
                    overflow: "hidden",
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                }}
            >
                {/* Removed colored header bar for cleaner look */}
                {/* <Box sx={{ p: 2, bgcolor: ..., display: 'flex', ... }}> ... </Box> */}

                {loading ? (
                    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, minHeight: 300}}> {/* Increased minHeight */}
                        <CircularProgress/>
                    </Box>
                ) : error ? (
                    <Box sx={{p: 4}}>
                        <Alert severity="error">{error}</Alert>
                    </Box>
                ) : totalCount === 0 ? (
                    <Box sx={{p: 4, textAlign: "center", minHeight: 200, display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}> {/* Centered empty state */}
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            {"暂无任务记录"}
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<Plus size={16}/>}
                            onClick={handleCreateNewTask}
                            sx={{mt: 2}}
                        >
                            创建第一个任务
                        </Button>
                    </Box>
                ) : (
                    <Box sx={{p: {xs: 1, sm: 2, md: 3}}}> {/* Responsive padding */}
                        <TableContainer>
                            <Table sx={{minWidth: 750}}>
                                <TableHead>
                                    <TableRow sx={{'& th': {fontWeight: 'bold'}}}>
                                        <TableCell>产品</TableCell>
                                        <TableCell>平台</TableCell>
                                        <TableCell align="right">发布数量</TableCell>
                                        <TableCell align="right">已完成</TableCell>
                                        <TableCell>完成率</TableCell>
                                        <TableCell>创建时间</TableCell>
                                        <TableCell align="right">操作</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {tasks.map((task) => { // Use tasks from state
                                        const completionRate = (task.taskCount && task.taskCount > 0)
                                            ? Math.round((task.completedCount / task.taskCount) * 100)
                                            : 0;
                                        return (
                                            <TableRow key={task.id_} hover sx={{'&:last-child td, &:last-child th': {border: 0}}}>
                                                <TableCell sx={{minWidth: 200}}> {/* Min width for product name */}
                                                    <Stack direction="row" spacing={1.5} alignItems="center">
                                                        <Box
                                                            component="img"
                                                            src={task.productImage || 'https://placehold.co/40x40/e0e0e0/bdbdbd?text=?'}
                                                            alt={task.productName}
                                                            sx={{width: 40, height: 40, borderRadius: 1, objectFit: 'cover', flexShrink: 0}}
                                                        />
                                                        <Typography variant="body2" noWrap title={task.productName}>
                                                            {task.productName}
                                                        </Typography>
                                                    </Stack>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip
                                                        label={task.platform || '未知'}
                                                        size="small"
                                                        // color="default"
                                                        variant="outlined"
                                                    />
                                                </TableCell>
                                                <TableCell align="right">
                                                    <Typography variant="body2">
                                                        {task.taskCount ?? 0}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell align="right">
                                                    <Typography variant="body2" color={task.completedCount === task.taskCount ? "success.main" : "text.primary"}>
                                                        {task.completedCount ?? 0}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell sx={{minWidth: 120}}> {/* Min width for progress bar */}
                                                    <Stack direction="row" spacing={1} alignItems="center">
                                                        <Box sx={{width: '100%', maxWidth: 60, mr: 1}}>
                                                            <LinearProgress
                                                                variant="determinate"
                                                                value={completionRate}
                                                                color={completionRate === 100 ? 'success' : 'primary'}
                                                                sx={{
                                                                    height: 6,
                                                                    borderRadius: 3,
                                                                    bgcolor: 'grey.300'
                                                                }}
                                                            />
                                                        </Box>
                                                        <Typography variant="body2" color="text.secondary">
                                                            {completionRate}%
                                                        </Typography>
                                                    </Stack>
                                                </TableCell>
                                                <TableCell sx={{minWidth: 140}}> {/* Min width for date */}
                                                    <Typography variant="body2" color="text.secondary">
                                                        {task.createdAt || 'N/A'}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell align="right">
                                                    <Stack direction="row" spacing={0.5} justifyContent="flex-end"> {/* Reduced spacing */}
                                                        <IconButton
                                                            size="small"
                                                            color="primary"
                                                            onClick={() => router.push(`/protected/promotion-task/${task.id_}`)} // Navigate to detail page
                                                            title="查看详情"
                                                        >
                                                            <Eye size={18}/>
                                                        </IconButton>
                                                        <IconButton
                                                            size="small"
                                                            color="error"
                                                            onClick={() => handleDeleteTask(task.id_)}
                                                            title="删除任务"
                                                            disabled={isDeleting} // Disable while deleting
                                                        >
                                                            <Trash2 size={18}/>
                                                        </IconButton>
                                                    </Stack>
                                                </TableCell>
                                            </TableRow>
                                        )
                                    })}
                                </TableBody>
                            </Table>
                        </TableContainer>

                        {totalPages > 1 && (
                            <Box sx={{display: 'flex', justifyContent: 'center', pt: 3, mt: 2, borderTop: `1px solid ${theme.palette.divider}`}}> {/* Added padding/border */}
                                <Pagination
                                    count={totalPages}
                                    page={page}
                                    onChange={handlePageChange}
                                    color="primary"
                                    showFirstButton
                                    showLastButton
                                    disabled={loading}
                                />
                            </Box>
                        )}
                    </Box>
                )}
            </Paper>

            {/* Add Confirmation Dialog */}
            <ConfirmationDialog
                open={openConfirmDialog}
                onClose={() => setOpenConfirmDialog(false)} // Close without action
                onConfirm={confirmDeleteTask} // Call confirm function
                title="确认删除任务"
                description={`您确定要删除此推广任务吗？此操作将标记任务为已删除，但可能不会立即从所有视图中移除。`}
                confirmText="确认删除"
                cancelText="取消"
                confirmButtonColor="error" // Use error color for delete confirmation
            />

            {/* Footer Info - Optional */}
            {/* <Box sx={{display: "flex", justifyContent: "flex-end"}}> ... </Box> */}
        </Container>
    )
}
