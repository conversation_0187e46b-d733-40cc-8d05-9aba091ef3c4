import api from "@/core/api/api";

export const aiGeneratedMaterialApi = {
    delete: async (id_) => {
        return await api({
            resource: "ai_generated_material",
            method_name: "delete",
            id_,
        });
    },

    queryByTaskId: async (taskId, page = 1, limit = 10) => {
        return await api({
            resource: "ai_generated_material",
            method_name: "query_by_task_id",
            task_id: taskId,
            page: page,
            limit: limit,
        });
    },
    
    queryById: async (id_) => {
        return await api({
            resource: "ai_generated_material",
            method_name: "query_by_id",
            id_,
        });
    },
}; 