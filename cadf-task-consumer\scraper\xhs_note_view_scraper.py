import re
import json
from datetime import datetime, timedelta

from playwright.sync_api import sync_playwright
from fake_useragent import UserAgent

from config.config import PLAYWRIGHT_HEADLESS, USE_PROXY
from omni.log.log import olog
from .xhs_online_status_scraper import is_on_login_page
from .proxy_server import ProxyServer
from .utils import convert_mongo_cookies_to_playwright

"""
抓取自己账号的笔记的阅读量、点赞量、评论量、分享量、收藏量
"""

def xhs_note_view_scraper(mongo_cookies: list[dict]):
    """
    抓取小红书笔记视图（最近一周的笔记数据）。

    Args:
        mongo_cookies: 从 MongoDB 获取的原始 Cookie 列表。
    """
    xhs_url = 'https://creator.xiaohongshu.com/new/note-manager?source=official'

    notes_data = []
    one_week_ago = datetime.now() - timedelta(days=7)
    processed_count = 0
    added_count = 0

    olog.info("开始抓取小红书笔记数据...")

    with sync_playwright() as p:
        browser_options = {'headless': PLAYWRIGHT_HEADLESS}  # 默认为无头模式
        context_options = {} # 初始化 context_options

        if USE_PROXY:
            proxy_manager = ProxyServer()
            proxy_info = proxy_manager.get_proxy_server()
            if proxy_info:
                # browser_options["proxy"] = { # 从 browser_options 移除
                context_options["proxy"] = { # 添加到 context_options
                    "server": proxy_info["server"],
                    "username": proxy_info["username"],
                    "password": proxy_info["password"]
                }
                olog.info(f"抓取笔记视图时使用代理: {proxy_info['server']}")
            else:
                olog.warning("配置了使用代理但无法获取代理信息，将不使用代理。")
        else:
            olog.info("未配置使用代理。")

        try:
            browser = p.chromium.launch(**browser_options)
            ua = UserAgent(browsers=['Chrome', 'Firefox'])
            # 将代理信息和 user_agent/viewport 合并到 context_options
            context_options.update({
                'user_agent': ua.random,
                'viewport': {'width': 1920, 'height': 1080}
            })
            context = browser.new_context(**context_options) # 应用 context_options

            olog.info("转换并添加 Cookies...")
            playwright_cookies = []
            if mongo_cookies:
                try:
                    playwright_cookies = convert_mongo_cookies_to_playwright(mongo_cookies)
                    context.add_cookies(playwright_cookies)
                    olog.info(f"尝试添加 {len(playwright_cookies)} 个转换后的 Cookie。")
                except Exception as cookie_error:
                    olog.error(f"添加或转换 Cookie 时出错: {cookie_error}")
                    # 根据情况决定是否继续，如果 cookie 对后续操作至关重要，可能需要返回
                    # browser.close()
                    # return []
            else:
                olog.warning("未提供 Cookie 列表，可能无法正确抓取需要登录的数据。")

            page = context.new_page()

            # --- 请求头设置结束 ---

            olog.info(f"正在导航到: {xhs_url}")
            page.goto(xhs_url, wait_until='networkidle', timeout=60000)  # 等待网络空闲，增加超时
            olog.info("页面加载完成。")

            # 添加登录检查
            if is_on_login_page(page):
                olog.error("检测到登录页面，无法抓取笔记视图数据。请检查 Cookie 或登录状态。")
                browser.close()
                return []

            # 等待笔记列表容器出现
            notes_container_selector = "div.d-tabs-pane > div.content"
            olog.info(f"等待笔记容器选择器: {notes_container_selector}")
            try:
                page.wait_for_selector(notes_container_selector, timeout=45000)  # 增加等待时间
                olog.info("笔记容器已找到。")
            except Exception as e:
                olog.error(f"等待笔记容器超时或失败: {e}")
                browser.close()
                return []

            # 点击"已发布"选项卡
            published_tab_selector = 'div.tab-title:has-text("已发布")'
            olog.info(f"尝试点击 '已发布' 选项卡: {published_tab_selector}")
            try:
                page.locator(published_tab_selector).click(timeout=10000)
                olog.info("'已发布' 选项卡点击成功。")
                # 等待内容可能因点击而更新
                page.wait_for_timeout(3000) # 等待3秒，确保内容加载
            except Exception as e:
                olog.error(f"点击 '已发布' 选项卡失败: {e}")
                # 可以选择在这里返回或继续尝试抓取全部笔记
                # browser.close()
                # return []

            # --- 滚动加载 ---
            olog.info("开始滚动加载更多笔记...")
            scroll_count = 0
            max_scrolls = 10 # 设置最大滚动次数，防止无限循环
            one_week_ago_reached = False

            # 定义滚动容器的选择器 - 使用笔记容器选择器作为滚动目标
            # scrollable_pane_selector = "div.d-tabs-pane" # 上次尝试
            scrollable_container_selector = notes_container_selector # 使用 div.content 作为滚动目标

            # 获取滚动容器元素
            scrollable_element = page.locator(scrollable_container_selector).first

            if not scrollable_element.is_visible():
                olog.error(f"滚动容器元素 '{scrollable_container_selector}' 不可见，无法执行滚动。")
                # 根据实际情况决定是否退出
                # browser.close()
                # return []
            else:
                olog.info(f"获取到滚动容器元素 '{scrollable_container_selector}'，准备滚动。")


            # 获取初始滚动位置
            last_scroll_top = scrollable_element.evaluate('(element) => element.scrollTop')

            while scroll_count < max_scrolls:
                # 查找笔记依然在 notes_container_selector 内进行
                note_elements_scroll = page.locator(f"{notes_container_selector} > div.note").all()
                if not note_elements_scroll:
                    # 可能刚开始就没有笔记，或者滚动后没有笔记了
                    if scroll_count == 0:
                         olog.info("页面初始加载时未找到笔记元素。")
                    else:
                         olog.info("滚动时未找到更多笔记，停止滚动。")
                    break

                # 检查最后一个笔记的日期
                try:
                    last_note_element = note_elements_scroll[-1]
                    time_str_raw = last_note_element.locator("div.time").inner_text(timeout=3000)
                    match = re.search(r'(\d{4})年(\d{2})月(\d{2})日 (\d{2}):(\d{2})', time_str_raw)
                    if match:
                        year, month, day, hour, minute = map(int, match.groups())
                        publish_time = datetime(year, month, day, hour, minute)
                        if publish_time < one_week_ago:
                            olog.info(f"找到发布于 {publish_time.strftime('%Y-%m-%d')} 的笔记（早于一周前），停止滚动。")
                            one_week_ago_reached = True
                            break
                        else:
                             olog.debug(f"最后一个可见笔记发布于 {publish_time.strftime('%Y-%m-%d %H:%M')}，继续滚动。")
                    else:
                        olog.warning(f"滚动时无法解析最后一个笔记的日期: '{time_str_raw}'，继续滚动。")

                except Exception as e:
                    olog.warning(f"滚动时检查最后一个笔记日期出错: {e}，继续滚动。")

                # 滚动页面 - 滚动指定的 scrollable_element (div.content)
                olog.debug(f"尝试滚动容器 {scrollable_container_selector}")
                scrollable_element.evaluate('(element) => { element.scrollTop = element.scrollHeight; }')
                scroll_count += 1
                olog.debug(f"执行滚动操作，次数: {scroll_count}")
                page.wait_for_timeout(3000) # 稍微增加等待时间

                # 检查滚动位置是否真的改变了
                current_scroll_top = scrollable_element.evaluate('(element) => element.scrollTop')
                # 添加一个小的容差，防止因细微变化导致误判
                if abs(current_scroll_top - last_scroll_top) < 5:
                    olog.info("滚动位置未显著改变，可能已到底部或滚动无效，停止滚动。")
                    break
                last_scroll_top = current_scroll_top

            # 循环结束后的日志记录
            current_scroll_top = scrollable_element.evaluate('(element) => element.scrollTop') # 获取最终滚动位置
            if scroll_count >= max_scrolls:
                 olog.warning(f"达到最大滚动次数 {max_scrolls}，停止滚动。")
            elif one_week_ago_reached:
                 olog.info("因找到一周前笔记而停止滚动。")
            # elif abs(current_scroll_top - last_scroll_top) < 5: # 使用最终的current_scroll_top
            elif page.locator(f"{notes_container_selector} > div.note").count() > 0 and abs(current_scroll_top - last_scroll_top) < 5:
                 olog.info("因滚动位置不再变化而停止滚动。")
            # --- 滚动加载结束 ---


            # 查找所有笔记元素 (滚动后重新获取所有元素)
            note_elements = page.locator(f"{notes_container_selector} > div.note").all()
            olog.info(f"滚动加载后，在页面上找到 {len(note_elements)} 个笔记元素。")

            if not note_elements:
                olog.warning("未找到任何笔记元素，请检查页面结构或 Cookie 是否有效。")

            for note_element in note_elements:
                processed_count += 1
                try:
                    # 提取发布时间并过滤
                    time_str_raw = note_element.locator("div.time").inner_text(timeout=5000)
                    match = re.search(r'(\d{4})年(\d{2})月(\d{2})日 (\d{2}):(\d{2})', time_str_raw)
                    if not match:
                        olog.warning(f"无法从 '{time_str_raw}' 中解析日期，跳过此笔记。")
                        continue

                    year, month, day, hour, minute = map(int, match.groups())
                    publish_time = datetime(year, month, day, hour, minute)

                    if publish_time >= one_week_ago:
                        title_element = note_element.locator("div.title")
                        title = title_element.inner_text(timeout=5000) if title_element.is_visible() else "标题不可见"

                        icon_list = note_element.locator("div.icon_list")
                        icons = icon_list.locator("div.icon")

                        # 提取各项数据，增加健壮性
                        def get_icon_count(index: int) -> int:
                            try:
                                count_str = icons.nth(index).locator("span").inner_text(timeout=3000)
                                return int(count_str)
                            except Exception:
                                olog.warning(f"无法提取第 {index + 1} 个图标的计数，默认为 0。")
                                return 0

                        view_count = get_icon_count(0)
                        comment_count = get_icon_count(1)
                        like_count = get_icon_count(2)
                        favorite_count = get_icon_count(3)
                        share_count = get_icon_count(4)

                        notes_data.append({
                            'title': title.strip(),
                            'publish_time': publish_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'view_count': view_count,
                            'like_count': like_count,
                            'comment_count': comment_count,
                            'share_count': share_count,
                            'favorite_count': favorite_count,
                        })
                        added_count += 1
                        olog.debug(f"添加笔记: {title.strip()} (发布于 {publish_time.strftime('%Y-%m-%d %H:%M')})")
                    # else:
                    # olog.debug(f"跳过较旧的笔记: 发布于 {publish_time.strftime('%Y-%m-%d %H:%M')}")

                except Exception as e:
                    olog.error(f"处理单个笔记时出错: {e}")
                    try:
                        note_html_snippet = note_element.inner_html(timeout=2000)
                        olog.error(f"问题笔记 HTML 片段: {note_html_snippet[:300]}...")
                    except Exception as html_err:
                        olog.error(f"无法获取问题笔记的 HTML: {html_err}")
                    continue

            olog.info(f"处理完成 {processed_count} 个笔记元素，添加了 {added_count} 条一周内的数据。")
            # 使用 json.dumps 格式化输出
            notes_data_str = json.dumps(notes_data, indent=4, ensure_ascii=False)
            olog.info(f"最终抓取的笔记数据:\n{notes_data_str}")
            browser.close()
            olog.info("浏览器已关闭。")

        except Exception as e:
            olog.error(f"Playwright 操作期间发生错误: {e}")
            if 'browser' in locals() and browser.is_connected():
                browser.close()
            return []  # 返回空列表表示出错

    return notes_data
