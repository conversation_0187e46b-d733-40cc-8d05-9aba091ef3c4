import fileManager from "@/core/api/cos-api";
import { PIC_PARENT_KEY } from "@/config";

/**
 * 上传图片文件或Base64字符串到COS指定图片目录下
 * @param {File|string} fileOrBase64 - 图片文件对象或Base64字符串
 * @param {function(number):void} [onProgress] - 上传进度回调函数，参数为 0-100 的百分比
 * @returns {Promise<{key: string, url: string}>} 返回包含文件Key和可访问URL的对象
 * @throws {Error} 上传过程中发生错误时抛出
 */
export async function uploadImage(fileOrBase64, onProgress) {
    console.log("开始上传图片...");
    try {
        let fileKey;
        if (fileOrBase64 instanceof File) {
            console.log("输入类型：File 对象");
            // 处理 File 对象
            fileKey = await fileManager.uploadFile(
                fileOrBase64,
                PIC_PARENT_KEY,
                onProgress
            );
        } else if (typeof fileOrBase64 === 'string' && fileOrBase64.startsWith('data:image')) {
            console.log("输入类型：Base64 字符串");
            // 处理 Base64 字符串
            const match = fileOrBase64.match(/^data:image\/([a-zA-Z+]+);base64,/);
            if (!match || !match[1]) {
                throw new Error("无效的 Base64 图像格式");
            }
            const fileExtension = match[1];
            fileKey = await fileManager.uploadFileFromBase64(
                fileOrBase64,
                fileExtension,
                PIC_PARENT_KEY,
                onProgress
            );
        } else {
            throw new Error("无效的输入类型，需要 File 对象或 Base64 图像字符串");
        }
        console.log(`图片上传成功，Key: ${fileKey}`);
        // 获取并返回签名 URL
        const url = await getImageUrl(fileKey);
        console.log(`获取图片 URL 成功: ${url}`);
        return { key: fileKey, url: url };
    } catch (error) {
        console.error("图片上传失败:", error);
        throw error; // 将错误向上抛出，方便调用者处理
    }
}

/**
 * 根据文件 Key 获取图片的访问 URL
 * @param {string} fileKey - 存储在COS中的文件 Key
 * @returns {Promise<string>} 返回可访问的图片 URL
 * @throws {Error} 获取URL过程中发生错误时抛出
 */
export async function getImageUrl(fileKey) {
    if (!fileKey || typeof fileKey !== 'string') {
        throw new Error("无效的文件 Key");
    }
    try {
        const url = await fileManager.getSignedUrl("GET", fileKey);
        console.log(`获取图片 URL 成功: ${url}`);
        return url;
    } catch (error) {
        console.error("获取图片 URL 失败:", error);
        throw error; // 将错误向上抛出
    }
}
