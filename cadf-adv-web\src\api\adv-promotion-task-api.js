import api from "@/core/api/api";

export const advPromotionTaskApi = {
    query_product_overview: async (search, page = 0, limit = 10) => {
        return await api({
            resource: "adv_promotion_task",
            method_name: "query_product_overview",
            search,
            page,
            limit,
        });
    },

    create_promotion_task: async (product_id, count, platform, start_date, end_date) => {
        return await api({
            resource: "adv_promotion_task",
            method_name: "create_promotion_task",
            product_id,
            count,
            platform,
            start_date, // 格式: "YYYY-MM-DD"
            end_date,   // 格式: "YYYY-MM-DD"
        });
    },

    // Function to query promotion tasks (now PromotionTask)
    queryPromotionTasks: async (page = 0, limit = 10) => {
        return await api({
            resource: "adv_promotion_task",
            method_name: "query_promotion_tasks",
            page: page,
            limit: limit,
        });
    },

    // Function to query promotion task detail (now PromotionTask)
    queryPromotionTaskDetail: async (taskId) => { // taskId now refers to PromotionTask ID
        return await api({
            resource: "adv_promotion_task",
            method_name: "query_promotion_task_detail",
            task_id: taskId, // Pass PromotionTask ID to backend
        });
    },

    // Function to query promotion task materials (paginated)
    queryPromotionTaskMaterials: async (taskId, page = 0, limit = 5) => { // Default limit 5
        return await api({
            resource: "adv_promotion_task",
            method_name: "query_promotion_task_materials",
            task_id: taskId,
            page: page,
            limit: limit,
        });
    },

    // Function to delete a promotion task (marks as deleted)
    deletePromotionTask: async (taskId) => {
        return await api({
            resource: "adv_promotion_task",
            method_name: "delete_promotion_task",
            task_id: taskId,
        });
    },

    // Function to append promotion task materials and optionally update end date
    appendPromotionTask: async (taskId, additionalCount, newEndDate = null) => { // newEndDate format: "YYYY-MM-DD" or null
        return await api({
            resource: "adv_promotion_task",
            method_name: "append_promotion_task",
            task_id: taskId,
            additional_count: additionalCount,
            new_end_date: newEndDate, // Pass null if not updating
        });
    },
};
