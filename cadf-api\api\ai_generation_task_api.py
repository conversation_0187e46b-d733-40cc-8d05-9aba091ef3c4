import time

from config import config
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from omni.mongo.mongo_client import doc_to_dict, save_or_update
from omni.msg_queue.redis_set_publisher import publish_many_to_redis_set
from omni.redis.redis_client import rc
from repository.models import AiGenerationTask, Product, AiGeneratedMaterial, AiBasicMaterial


@register_handler("ai_generation_task")
class AiGenerationTaskApi:
    @auth_required(["admin", "advertiser"])
    def create(self, data):
        user_id = data.get("user_id")
        product_id = data.get("product_id")
        material_ids = data.get("material_ids", [])
        target_quantity = data.get("target_quantity")

        olog.info(f"开始创建AI生成任务, 用户ID: {user_id}, 产品ID: {product_id}")

        # 1. 验证输入参数
        olog.debug("开始验证创建任务输入参数")
        self._validate_create_input(data)
        olog.info("输入参数验证通过")

        # 2. 验证产品和素材的所有权
        olog.debug(f"开始验证产品和素材所有权, 产品ID: {product_id}, 素材IDs: {material_ids}")
        product = self._validate_product_ownership(product_id, user_id)
        self._validate_material_ownership(material_ids, user_id)
        olog.info(f"产品和素材所有权验证通过, 产品ID: {product_id}")

        # 3. 准备任务数据并保存
        olog.debug("开始准备任务属性")
        task_data = self._prepare_task_attributes(data, product)
        olog.info(f"准备保存AI生成任务: {task_data}")
        task_id = save_or_update(AiGenerationTask, task_data)
        olog.info(f"任务保存成功, 任务ID: {task_id}")

        # 4. 准备 AiGeneratedMaterial 对象
        olog.debug(f"开始准备 AiGeneratedMaterial 对象, 任务ID: {task_id}")
        materials_to_create = self._prepare_generated_materials(
            task_id=str(task_id),
            user_id=user_id,
            product_id=product_id,
            target_quantity=target_quantity,
            create_time=task_data["create_at"],
            material_ids=material_ids,
        )
        olog.info(f"准备生成 {len(materials_to_create)} 个素材记录, 任务ID: {task_id}")

        # 5. 保存 Material 并推送到 Redis
        if materials_to_create:
            olog.debug(f"开始保存 Material 并推送到 Redis, 任务ID: {task_id}")
            self._save_materials_and_push_to_redis(materials_to_create)
        else:
            olog.info(f"没有需要创建的 Material 记录, 任务ID: {task_id}")

        olog.info(f"AI生成任务创建成功, 任务ID: {task_id}")
        return {"id_": str(task_id)}

    def _validate_create_input(self, data):
        """验证创建任务所需的输入参数"""
        required_keys = ["product_id", "material_ids", "target_quantity", "user_id"]
        if not all(key in data for key in required_keys):
            missing = [key for key in required_keys if key not in data]
            raise MException(f"缺少必要参数: {', '.join(missing)}")

        material_ids = data.get("material_ids")
        if not isinstance(material_ids, list) or not material_ids:
            raise MException("material_ids 必须是一个非空列表")

        target_quantity = data.get("target_quantity")
        if not isinstance(target_quantity, int) or target_quantity <= 0:
            raise MException("target_quantity 必须是一个正整数")

    def _validate_product_ownership(self, product_id, user_id):
        """验证产品是否存在以及用户是否有权访问"""
        product = Product.objects(id=product_id, user_id=user_id).first()
        if not product:
            raise MException(f"产品不存在或无权访问: {product_id}")
        return product

    def _validate_material_ownership(self, material_ids, user_id):
        """验证素材是否存在以及用户是否有权访问"""
        valid_material_count = AiBasicMaterial.objects(
            id__in=material_ids, user_id=user_id
        ).count()
        if valid_material_count != len(material_ids):
            # 为了安全，不具体指出哪个 ID 无效
            raise MException("一个或多个素材不存在或无权访问")

    def _prepare_task_attributes(self, data, product):
        """准备用于创建 AiGenerationTask 的属性字典"""
        current_time = int(time.time())
        generated_task_name = f"{product.title} - 生成任务"

        task_attributes = {
            "user_id": data.get("user_id"),
            "product_id": data.get("product_id"),
            # Do not save material_ids or target_quantity to the task itself
            "task_name": generated_task_name,
            "create_at": current_time,
            "status": "pending",  # Although status field is removed, keep it for potential future use or remove logic depending on it
            "progress": 0  # Although progress field is removed, keep it for potential future use or remove logic depending on it
        }

        return task_attributes

    def _prepare_generated_materials(
            self, task_id, user_id, product_id, target_quantity, create_time, material_ids
    ):
        """准备 AiGeneratedMaterial 对象列表 (不保存)"""
        materials_to_insert = []
        num_materials = len(material_ids)
        for i in range(target_quantity):
            assigned_material_id = (
                material_ids[i % num_materials] if num_materials > 0 else None
            )
            # 检查 assigned_material_id 是否为 None，如果是，则可能需要记录或处理
            if assigned_material_id is None:
                olog.warning(f"在准备 AiGeneratedMaterial 时，由于 material_ids 为空，material_id 被设置为 None。任务ID: {task_id}, 迭代次数: {i}")

            # 更新 material_data 以匹配新的 AiGeneratedMaterial 模型
            material_data = {
                "task_id": task_id,
                "user_id": user_id,
                "product_id": product_id,
                "material_id": assigned_material_id,
                # 使用新的状态字段和添加新字段的初始化
                "image_generation_status": "待生成",
                "text_generation_status": "待生成",
                "token_consumption": 0,
                "title": "",  # 初始化 title
                "content": "",  # 初始化 content
                "images": [],  # 初始化 images
                "create_at": create_time,
                "is_deleted": False  # 显式设置is_deleted
            }
            materials_to_insert.append(AiGeneratedMaterial(**material_data))
        return materials_to_insert

    def _save_materials_and_push_to_redis(self, materials_to_insert):
        """批量保存 AiGeneratedMaterial 并将 ID 推送到 Redis (使用 redis_set_publisher)"""
        # 检查列表是否为空
        if not materials_to_insert:
            olog.info("没有 AiGeneratedMaterial 需要保存和推送到 Redis")
            return

        olog.info(f"开始批量保存 {len(materials_to_insert)} 个 AiGeneratedMaterial")
        # 批量插入数据库
        inserted_results = AiGeneratedMaterial.objects.insert(materials_to_insert)
        # MongoEngine 的 insert 返回新创建文档的 OID 列表或单个OID (如果只有一个)
        # 确保 inserted_results 是列表
        if not isinstance(inserted_results, list):
            inserted_results = [inserted_results]
        # 从返回的对象中提取 ID
        inserted_ids = [material.id for material in inserted_results]
        olog.info(f"成功批量保存 {len(inserted_ids)} 个 AiGeneratedMaterial, IDs: {[str(oid) for oid in inserted_ids]}")

        # --- 将生成的 Material ID 推送到 Redis Set (使用封装的函数) ---
        # 使用配置中的新键名
        redis_image_set_key = config.XHS_IMAGE_GEN_SET
        redis_text_set_key = config.XHS_TEXT_GEN_SET

        # 将 ObjectId 转换为字符串
        task_ids_to_push = [str(oid) for oid in inserted_ids]

        if task_ids_to_push:
            olog.debug(f"准备将 {len(task_ids_to_push)} 个 Material ID 推送到 Redis Sets '{redis_image_set_key}' 和 '{redis_text_set_key}' (使用 publisher)")

            # 使用 publish_many_to_redis_set 推送到图片生成 Set
            image_result = publish_many_to_redis_set(redis_image_set_key, task_ids_to_push)
            if image_result is not None:
                olog.info(f"成功向 Redis Set '{redis_image_set_key}' 添加了 {image_result} 个新任务 ID。")
            else:
                olog.error(f"向 Redis Set '{redis_image_set_key}' 添加任务 ID 时发生错误。")

            # 使用 publish_many_to_redis_set 推送到文本生成 Set
            text_result = publish_many_to_redis_set(redis_text_set_key, task_ids_to_push)
            if text_result is not None:
                olog.info(f"成功向 Redis Set '{redis_text_set_key}' 添加了 {text_result} 个新任务 ID。")
            else:
                olog.error(f"向 Redis Set '{redis_text_set_key}' 添加任务 ID 时发生错误。")

        else:
            olog.warning("没有 Material ID 需要推送到 Redis")

    @auth_required(["admin", "advertiser"])
    def delete(self, data):
        id_ = data.get("id_")
        if not id_:
            raise MException("缺少必要参数 id_")

        user_id = data.get("user_id")
        # 查询时也应考虑 is_deleted=False，避免重复"删除"
        task = AiGenerationTask.objects(id=id_, user_id=user_id, is_deleted=False).first()
        if not task:
            raise MException("任务不存在或无权删除")

        # --- 新增：删除 Redis Set 中的相关 Material ID (这部分逻辑保留) ---
        task_id_str = str(task.id)
        olog.info(f"开始准备删除与任务 {task_id_str} 相关的 Redis Set 数据")

        # 1. 查询关联的 AiGeneratedMaterial IDs
        related_materials = AiGeneratedMaterial.objects(task_id=task_id_str).only("id")
        material_ids_to_remove = [str(mat.id) for mat in related_materials]

        if material_ids_to_remove:
            olog.debug(f"找到 {len(material_ids_to_remove)} 个关联的 Material ID 需要从 Redis Set 中移除")
            try:
                redis_client = rc
                redis_image_set_key = config.XHS_IMAGE_GEN_SET
                redis_text_set_key = config.XHS_TEXT_GEN_SET

                # 2. 使用 Pipeline 从 Redis Set 中移除
                with redis_client.pipeline() as pipe:
                    pipe.srem(redis_image_set_key, *material_ids_to_remove)
                    pipe.srem(redis_text_set_key, *material_ids_to_remove)
                    results = pipe.execute()
                olog.info(
                    f"从 Redis Set '{redis_image_set_key}' 移除了 {results[0]} 个 ID, "
                    f"从 '{redis_text_set_key}' 移除了 {results[1]} 个 ID"
                )
            except Exception as e:
                # 记录错误，但允许任务删除继续进行，避免阻塞核心功能
                olog.error(f"从 Redis Set 删除 Material ID 时出错: {e}", exc_info=True)
        else:
            olog.info(f"任务 {task_id_str} 没有找到关联的 Material ID 需要从 Redis Set 中移除")
        # --- 结束 Redis Set 清理 ---

        # --- 修改：执行软删除 --- 
        olog.info(f"开始软删除任务, 任务ID: {id_}, 用户ID: {user_id}")
        task.is_deleted = True
        task.save()  # 保存更新
        olog.info(f"成功软删除任务记录, 任务ID: {id_}")
        # --- 结束软删除 ---
        return {"message": "删除成功"}

    @auth_required(["admin", "advertiser"])
    def query_one(self, data):
        id_ = data.get("id_")
        if not id_:
            raise MException("缺少必要参数 id_")

        user_id = data.get("user_id")
        # 添加 is_deleted=False 条件
        task = AiGenerationTask.objects(id=id_, user_id=user_id, is_deleted=False).first()
        if not task:
            raise MException("任务不存在或无权查看")
        return doc_to_dict(task)

    @auth_required(["admin", "advertiser"])
    def query_all(self, data):
        user_id = data.get("user_id")
        # 添加 is_deleted=False 到基础查询参数
        query_params = {"user_id": user_id, "is_deleted": False}

        # 获取前端请求的状态和任务名
        requested_status = data.get("status", "all")  # Default to "all" if not provided
        task_name = data.get("task_name")

        if task_name:
            query_params["task_name__icontains"] = task_name

        # 先查询所有匹配 user_id 和 task_name 的任务
        all_matching_tasks_query = AiGenerationTask.objects(**query_params).order_by("-create_at")

        # --- 在获取计数后进行状态过滤 ---
        processed_task_results = []
        for task in all_matching_tasks_query:  # 遍历所有匹配的任务，而不仅仅是分页后的
            task_dict = doc_to_dict(task)
            task_id_str = str(task.id)

            total_generated_count = AiGeneratedMaterial.objects(task_id=task_id_str).count()
            image_completed_count = AiGeneratedMaterial.objects(
                task_id=task_id_str, image_generation_status='已完成'  # 使用新的状态字段和值
            ).count()
            text_completed_count = AiGeneratedMaterial.objects(
                task_id=task_id_str, text_generation_status='已完成'  # 使用新的状态字段和值
            ).count()

            task_dict["total_generated_count"] = total_generated_count
            task_dict["image_completed_count"] = image_completed_count
            task_dict["text_completed_count"] = text_completed_count
            # task_dict["target_quantity"] is no longer available on the task model

            # 根据计算出的状态判断是否符合请求的状态 (检查图片和文本都完成)
            is_completed = (total_generated_count > 0 and
                            image_completed_count == total_generated_count and
                            text_completed_count == total_generated_count)
            # 简化处理中状态: 任何有生成项但未完成的任务
            is_processing = (total_generated_count > 0 and not is_completed)
            # 简化待处理状态: 任务存在但还没有生成项
            is_pending = (total_generated_count == 0)

            # 根据 requested_status 决定是否添加
            include_task = False
            if requested_status == "all":
                include_task = True
            elif requested_status == "completed" and is_completed:
                include_task = True
            elif requested_status == "processing" and is_processing:
                include_task = True
            elif requested_status == "pending" and is_pending:
                include_task = True
            # Add other status handling if needed

            if include_task:
                processed_task_results.append(task_dict)

        # --- 结束过滤 ---

        # --- 对过滤后的结果进行分页 --- 
        page = data.get("page", 0)
        page_size = data.get("page_size", 10)
        if not isinstance(page, int) or page < 0:
            page = 0
        if not isinstance(page_size, int) or page_size < 1:
            page_size = 10

        total_filtered = len(processed_task_results)
        start_index = page * page_size
        end_index = start_index + page_size
        paginated_results = processed_task_results[start_index:end_index]
        # --- 结束分页 ---

        # 返回分页后的结果和过滤后的总数
        return {"total": total_filtered, "items": paginated_results}
