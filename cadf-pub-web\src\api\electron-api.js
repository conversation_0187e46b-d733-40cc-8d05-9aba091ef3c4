import {invokeElectron, isElectron} from "@/core/tools/electron-bridge";
import {addAlert} from "@/core/components/redux/alert-slice";
import {reduxStore} from "@/components/redux-store";
import {AlertType} from "@/core/components/alert";

async function checkElectronEnvironment() {
    if (!isElectron()) {
        reduxStore.dispatch(
            addAlert({
                type: AlertType.ERROR,
                message: `请在桌面客户端中使用此功能`
            })
        );
        return false;
    }
    return true;
}

async function invokeElectronCommand(command, failureMessagePrefix, ...args) {
    if (!await checkElectronEnvironment()) {
        return null;
    }

    try {
        const result = await invokeElectron(command, ...args);
        console.log(`调用 ${command} 结果:`, result);

        if (result.success) {
            return result;
        } else {
            const errorMessage = `${failureMessagePrefix}: ${result.message || '未知错误'}`;
            console.error(errorMessage);
            reduxStore.dispatch(addAlert({type: AlertType.ERROR, message: errorMessage}));
            return null;
        }
    } catch (error) {
        const errorMessage = `与 Electron 通信失败 (${command}): ${error.message}`;
        console.error(errorMessage, error);
        reduxStore.dispatch(addAlert({type: AlertType.ERROR, message: errorMessage}));
        return null;
    }
}

async function handleOpenLoginWindow(url) {
    if (!url) {
        console.error("handleOpenLoginWindow requires a URL parameter.");
        reduxStore.dispatch(addAlert({type: AlertType.ERROR, message: "缺少登录 URL"}));
        return null;
    }
    console.log(`Calling open-login-window with URL: ${url}`);
    return await invokeElectronCommand("open-login-window", "打开登录窗口失败", url);
}

async function handleGetCookiesAndClose() {
    const result = await invokeElectronCommand("get-cookies-and-close", "获取 Cookie 失败");
    return result?.cookies ?? null;
}

export {handleOpenLoginWindow, handleGetCookiesAndClose, checkElectronEnvironment};
