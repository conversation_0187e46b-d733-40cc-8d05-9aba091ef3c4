import base64
import io
import random

from PIL import Image
from langchain.schema import HumanMessage
from pydantic import BaseModel, Field

from agent.utils.image_text_styler import (
    ImageTextStyler,
)  # ImageTextStyler 现在处理Base64
from omni.llm.agent.structured_output_agent import structured_output_agent
from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog

"""
把文字添加到图片上
"""


# 定义 Pydantic 模型
class TextPositionResult(BaseModel):
    position: str = Field(..., description="文字推荐位置")
    reason: str = Field(..., description="推荐位置的简要理由")

class TextPositionResultList(BaseModel):
    results: list[TextPositionResult] = Field(..., description="每组配文的推荐位置和理由")


def determine_optimal_text_positions(image_bytes: bytes, captions: list[dict]) -> list[str]:
    """
    使用 AI 模型分析图片内容，为多组配文确定最佳位置。

    :param image_bytes: 图片的字节数据
    :param captions: 多组配文 [{'title': ..., 'contents': [...]}, ...]
    :return: 每组配文的推荐位置列表
    """
    olog.info("开始执行 determine_optimal_text_positions (AI模式)...")

    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    img = Image.open(io.BytesIO(image_bytes))
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=img.format)
    base64_str = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")
    mime_type = img.format.lower() if img.format else "png"
    llm_image_input = {
        "type": "image_url",
        "image_url": {"url": f"data:image/{mime_type};base64,{base64_str}"},
    }

    # 组装多组配文的文字信息
    text_blocks = []
    for idx, cap in enumerate(captions):
        title = cap.get('title', '')
        bodies = cap.get('contents', [])
        text_blocks.append(f"第{idx+1}组:\n标题: {title}\n内容: {'; '.join(bodies)}")
    text_info = '\n\n'.join(text_blocks)

    prompt = f"""
# 角色
你是一位专注于图片内容分析的AI视觉专家。

# 多组文字信息
{text_info}

# 任务
请分析当前图片内容，并为每组文字信息分别确定最佳放置位置。具体要求如下：
文字位置应为以下之一：左上、左中、左下、右上、右中、右下。
每组文字在垂直方向（上、中、下）只能选择一个位置，以避免文字重叠
每组文字在水平方向（左、右）错开排列，避免样式单一
推荐位置的简要理由需清晰说明。
返回每组配文的推荐位置和理由，顺序与输入一致。

# 返回值
- results: 每组配文的推荐位置和理由数组
  - position: 文字位置的坐标或区域描述
  - reason: 推荐位置的简要理由
"""

    messages = [
        HumanMessage(content=[{"type": "text", "text": prompt}, llm_image_input])
    ]
    raw_result = llm.invoke(messages).content
    olog.info(f"原始AI输出: {raw_result}")

    # 使用 structured_output_agent 解析 AI 输出
    result = structured_output_agent(
        model_class=TextPositionResultList, raw_data=raw_result
    )
    olog.info(f"多组文字推荐位置: {result}")
    olog.info("determine_optimal_text_positions 执行完毕。")
    return [r.position for r in result.results]


def determine_random_text_positions(captions: list[dict]) -> list[str]:
    """
    随机为每组配文分配不重复的推荐位置，满足：
    1. 文字位置为：左上、左中、左下、右上、右中、右下。
    2. 每组文字在垂直方向（上、中、下）只能选择一个，避免重叠。
    3. 每组文字在水平方向（左、右）错开排列，避免样式单一。
    :param captions: 多组配文 [{'title': ..., 'contents': [...]}, ...]
    :return: 每组配文的推荐位置列表
    """
    olog.info("开始执行 determine_random_text_positions (随机模式)...")
    vertical_groups = {
        "上": ["左上", "右上"],
        "中": ["左中", "右中"],
        "下": ["左下", "右下"],
    }
    vertical_keys = list(vertical_groups.keys())
    n = len(captions)
    if n > 6:
        raise ValueError("配文数量不能超过可用位置数量")
    if n > 3:
        # 超过3组，优先均匀分配到3个垂直方向，再分配左右
        group_order = random.sample(vertical_keys, 3)
        # 先分配每个垂直方向一个
        chosen_verticals = group_order * (n // 3) + group_order[: n % 3]
    else:
        # 不足3组，随机选n个垂直方向
        chosen_verticals = random.sample(vertical_keys, n)
    # 统计每个垂直方向已用左右
    used_lr = {k: [] for k in vertical_keys}
    positions = []
    for idx, v in enumerate(chosen_verticals):
        # 交替分配左右
        lr_options = vertical_groups[v]
        # 优先分配未用过的方向
        available = [p for p in lr_options if p not in used_lr[v]]
        if not available:
            # 如果都用过了，重新开始
            used_lr[v] = []
            available = lr_options
        # 交替分配左右
        if idx % 2 == 0:
            pos = available[0]
        else:
            pos = available[-1]
        used_lr[v].append(pos)
        positions.append(pos)
    olog.info(f"分配的文字位置: {positions}")
    olog.info("determine_random_text_positions 执行完毕。")
    return positions


def add_caption_to_image(
        image_bytes: bytes, captions: list[dict]
) -> bytes:
    """
    将多组文字添加到图片上，并返回处理后的图片字节数据。

    :param image_bytes: 图片的字节数据
    :param captions: 多组配文 [{'title': ..., 'contents': [...]}, ...]
    :return: 处理后的图片字节数据
    """
    olog.info("开始执行 add_caption_to_image...")

    # 调用 determine_random_text_positions 获取每组文字位置
    positions = determine_random_text_positions(captions)
    olog.info(f"文字位置: {positions}")

    styler = ImageTextStyler()
    # 依次将每组配文添加到图片
    for caption, position in zip(captions, positions):
        image_bytes = styler.apply_style(
            image_bytes=image_bytes,
            title=caption.get('title', ''),
            bodies=caption.get('contents', []),
            position=position
        )

    olog.info("add_caption_to_image 执行完毕。")
    return image_bytes
