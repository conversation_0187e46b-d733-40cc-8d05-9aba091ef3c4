"use client";

import {useState, useEffect} from 'react';
import {Box, Button, Card, CardContent, Chip, IconButton, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme, Alert, CircularProgress } from '@mui/material';
import {ArrowLeft, Search, UserX, Clock, AlertTriangle} from 'lucide-react';
import {useRouter} from 'next/navigation';
import { crewManagementApi } from '@/api/crew-management-api';

export default function InactiveCrewMembers() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    
    const [crewMembers, setCrewMembers] = useState([]);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const rowsPerPage = 10;

    useEffect(() => {
        const fetchInactiveCrew = async () => {
            setLoading(true);
            setError(null);
            try {
                const response = await crewManagementApi.getInactiveCrewList(page, rowsPerPage);
                if (response && response.crew) {
                    setCrewMembers(response.crew);
                    setTotalCount(response.total_count || 0);
                } else {
                    throw new Error('获取不活跃舰员数据失败');
                }
            } catch (err) {
                setError(err.message || '加载数据时发生错误');
                setCrewMembers([]);
                setTotalCount(0);
            } finally {
                setLoading(false);
            }
        };

        fetchInactiveCrew();
    }, [page]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const getLastLoginDuration = (lastLogin) => {
        if (!lastLogin) return '未知';
        const lastDate = new Date(lastLogin.replace(/-/g, '/'));
        const now = new Date();
        const diffDays = Math.floor((now - lastDate) / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else if (diffDays < 30) {
            return `${Math.floor(diffDays / 7)}周前`;
        } else {
            return `${Math.floor(diffDays / 30)}月前`;
        }
    };

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton 
                    onClick={() => router.back()}
                    sx={{mr: 1}}
                    color="primary"
                >
                    <ArrowLeft />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{
                    fontWeight: 500, 
                    fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}
                }}>
                    不活跃舰员 ({totalCount})
                </Typography>
            </Box>

            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        查看所有不活跃舰员列表。不活跃舰员是指在最近两天内没有成功发布任务记录的舰员。您可以查看他们的最后登录时间。
                    </Typography>

                    {loading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                            <CircularProgress />
                        </Box>
                    )}

                    {error && (
                        <Alert severity="error" sx={{ my: 2 }}>{error}</Alert>
                    )}

                    {!loading && !error && (
                        <>
                        {isMobile ? (
                            <Box>
                                {crewMembers.length > 0 ? (
                                    crewMembers.map((member) => (
                                        <Card 
                                            key={member.id_} 
                                            elevation={1}
                                            sx={{
                                                mb: 2.5, 
                                                borderRadius: 2,
                                                position: 'relative',
                                                overflow: 'visible',
                                                transition: 'all 0.2s ease-in-out',
                                                '&:hover': {
                                                    boxShadow: '0 6px 12px rgba(0,0,0,0.08)'
                                                },
                                                border: '1px solid rgba(200, 200, 200, 0.12)',
                                                background: 'linear-gradient(to right bottom, #ffffff, #fafafa)'
                                            }}
                                        >
                                            <Box sx={{
                                                position: 'absolute',
                                                top: -8,
                                                right: 16,
                                                zIndex: 1
                                            }}>
                                                <Chip
                                                    label="不活跃"
                                                    size="small"
                                                    sx={{
                                                        bgcolor: 'error.main',
                                                        color: 'white',
                                                        fontWeight: 500,
                                                        borderRadius: '12px',
                                                        px: 1,
                                                        '& .MuiChip-label': {
                                                            px: 0.5
                                                        }
                                                    }}
                                                    icon={<UserX size={14} color="#fff" />}
                                                />
                                            </Box>
                                            <CardContent sx={{p: 2.5}}>
                                                <Typography variant="h6" sx={{
                                                    fontWeight: 600, 
                                                    mb: 2,
                                                    color: 'text.primary',
                                                    display: 'flex',
                                                    alignItems: 'center'
                                                }}>
                                                    {member.user_account}
                                                </Typography>
                                                
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    p: 1.5,
                                                    bgcolor: 'background.paper',
                                                    borderRadius: 1.5,
                                                    mb: 2,
                                                    border: '1px solid rgba(0,0,0,0.06)'
                                                }}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        flex: 1
                                                    }}>
                                                        <Typography variant="caption" color="text.secondary" sx={{mb: 0.5}}>
                                                            平台账号
                                                        </Typography>
                                                        <Typography variant="body2" fontWeight={500}>
                                                            {member.platform_account || '未设置'}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                                
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    mb: 2,
                                                    p: 1.5,
                                                    bgcolor: 'rgba(255, 72, 66, 0.04)',
                                                    borderRadius: 1.5,
                                                    border: '1px solid rgba(255, 72, 66, 0.12)'
                                                }}>
                                                    <Clock size={18} color="#FF4842" style={{marginRight: '12px', flexShrink: 0}} />
                                                    <Box>
                                                        <Typography variant="caption" color="error.main" sx={{fontWeight: 500}}>
                                                            最后登录时间
                                                        </Typography>
                                                        <Typography variant="body2" fontWeight={500}>
                                                            {member.last_login || '无记录'}
                                                        </Typography>
                                                        {member.last_login && (
                                                            <Typography variant="caption" color="error.main" sx={{display: 'block'}}>
                                                                {getLastLoginDuration(member.last_login)}
                                                            </Typography>
                                                        )}
                                                    </Box>
                                                </Box>
                                                
                                                <Box sx={{
                                                    display: 'flex',
                                                    justifyContent: 'flex-start',
                                                    gap: 2
                                                }}>
                                                </Box>
                                            </CardContent>
                                        </Card>
                                    ))
                                ) : (
                                    <Card variant="outlined" sx={{
                                        p: 3, 
                                        textAlign: 'center', 
                                        borderRadius: 2,
                                        bgcolor: 'background.paper'
                                    }}>
                                        <AlertTriangle size={40} color="#FF4842" style={{marginBottom: '16px', opacity: 0.6}} />
                                        <Typography>没有找到匹配的不活跃舰员</Typography>
                                    </Card>
                                )}
                            </Box>
                        ) : (
                            <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>用户账号</TableCell>
                                            <TableCell>平台账号</TableCell>
                                            <TableCell>状态</TableCell>
                                            <TableCell>最后登录时间</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {crewMembers.length > 0 ? (
                                            crewMembers.map((member) => (
                                                <TableRow key={member.id_} hover>
                                                    <TableCell>{member.user_account}</TableCell>
                                                    <TableCell>{member.platform_account || '未设置'}</TableCell>
                                                    <TableCell>
                                                        <Chip
                                                            label="不活跃"
                                                            color="error"
                                                            size="small"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        {member.last_login || '无记录'}
                                                        {member.last_login && (
                                                            <Typography variant="caption" display="block" color="text.secondary">
                                                                {getLastLoginDuration(member.last_login)}
                                                            </Typography>
                                                        )}
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={4} align="center">
                                                    没有找到不活跃舰员
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        )}
                        </>
                    )}

                    {totalCount > rowsPerPage && (
                        <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                            <Pagination
                                count={Math.ceil(totalCount / rowsPerPage)}
                                page={page}
                                onChange={handlePageChange}
                                color="primary"
                                size={isMobile ? "small" : "medium"}
                            />
                        </Stack>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
} 