import api from '@/core/api/api';

export const advProductTrafficApi = {
  
  queryAll: async ({ page = 1, limit = 5 } = {}) => { 
    return await api({
      resource: 'adv_product_traffic',
      method_name: 'query_all',
      page: page,
      limit: limit
    });
  },

  getTotalStats: async () => {
    return await api({
      resource: 'adv_product_traffic',
      method_name: 'get_total_stats',
    });
  },
};
