"use client"

import {use, useEffect, useState} from 'react'
import {Box, Button, CardContent, Chip, Container, Divider, Grid, LinearProgress, Pagination, Paper, Stack, Typography} from '@mui/material'
import {useTheme} from '@mui/material/styles'
import {ChevronLeft, Plus, RefreshCw, Trash2} from 'lucide-react'
import {useRouter} from 'next/navigation'
import ProductCard from '@/components/ProductCard'
import {advPromotionTaskApi} from '@/api/adv-promotion-task-api'
import ConfirmationDialog from '@/components/ConfirmationDialog';


export default function TaskDetail({params}) {
    const theme = useTheme()
    const router = useRouter()
    const [task, setTask] = useState(null)
    const [isLoading, setIsLoading] = useState(true)
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [error, setError] = useState(null);
    const resolvedParams = use(params)
    const [materials, setMaterials] = useState([]);
    const [isLoadingMaterials, setIsLoadingMaterials] = useState(false);
    const [totalMaterialCount, setTotalMaterialCount] = useState(0);
    const [materialPage, setMaterialPage] = useState(1);
    const itemsPerPage = 5;
    const [isDeleting, setIsDeleting] = useState(false);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);


    const fetchTaskDetails = async (taskId) => {
        if (!taskId) {
            setError("未提供任务ID");
            setIsLoading(false);
            return;
        }
        setError(null);
        try {
            const response = await advPromotionTaskApi.queryPromotionTaskDetail(taskId);


            if (response && typeof response === 'object' && Object.keys(response).length > 0) {
                setTask(response);
            } else {

                console.error('API响应无效或为空:', response);
                setError("获取任务详情失败: 响应数据无效或为空");
                setTask(null);
            }
        } catch (err) {
            console.error('获取任务详情失败:', err);

            const errorMessage = err?.response?.data?.message || err?.message || "获取任务详情时发生未知错误";
            setError(errorMessage);
            setTask(null);
        }
    };


    const fetchMaterials = async (taskId, page) => {
        if (!taskId) return;
        setIsLoadingMaterials(true);
        setError(null);
        try {
            const materialResponse = await advPromotionTaskApi.queryPromotionTaskMaterials(
                taskId,
                page - 1,
                itemsPerPage
            );
            if (materialResponse && Array.isArray(materialResponse.materials)) {
                setMaterials(materialResponse.materials);
                setTotalMaterialCount(materialResponse.total_count || 0);
            } else {
                console.error('获取素材列表失败: 响应数据无效', materialResponse);
                setError("获取素材列表失败: 响应数据无效");
                setMaterials([]);
                setTotalMaterialCount(0);
            }
        } catch (err) {
            console.error('获取素材列表失败:', err);
            const errorMessage = err?.response?.data?.message || err?.message || "获取素材列表时发生未知错误";
            setError(`获取素材列表失败: ${errorMessage}`);
            setMaterials([]);
            setTotalMaterialCount(0);
        } finally {
            setIsLoadingMaterials(false);
        }
    };


    useEffect(() => {
        const initialLoad = async () => {
            setIsLoading(true);
            await fetchTaskDetails(resolvedParams.id);
            setIsLoading(false);
        }
        initialLoad();
    }, [resolvedParams.id]);


    useEffect(() => {
        if (task && resolvedParams.id) {
            fetchMaterials(resolvedParams.id, materialPage);
        }
    }, [task, resolvedParams.id, materialPage]);


    const handleRefreshStatus = async () => {
        setIsRefreshing(true);
        await fetchTaskDetails(resolvedParams.id);
        await fetchMaterials(resolvedParams.id, materialPage);
        setIsRefreshing(false);
    };


    const handleDeleteTask = async () => {
        setOpenConfirmDialog(false);
        setIsDeleting(true);
        try {
            await advPromotionTaskApi.deletePromotionTask(resolvedParams.id);
            router.push('/protected/promotion-task');
        } catch (err) {
            console.error('删除任务失败:', err);
            const errorMessage = err?.response?.data?.message || err?.message || "删除任务时发生未知错误";
            setError(`删除失败: ${errorMessage}`);
            setIsDeleting(false);
        }
    };


    if (isLoading) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <LinearProgress/>
                <Typography align="center" sx={{mt: 2}}>加载任务详情中...</Typography>
            </Container>
        );
    }


    if (error) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{width: '100%', mt: 10, textAlign: 'center'}}>
                    <Typography variant="h5" color="error">
                        加载失败: {error}
                    </Typography>
                    <Button
                        variant="outlined"
                        startIcon={<ChevronLeft/>}
                        onClick={() => router.push('/protected/promotion-task')}
                        sx={{mt: 2}}
                    >
                        返回任务列表
                    </Button>
                </Box>
            </Container>
        );
    }


    if (!task || Object.keys(task).length === 0) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{width: '100%', mt: 10, textAlign: 'center'}}>
                    <Typography variant="h5" color="text.secondary">
                        未找到任务详情。
                    </Typography>
                    <Button
                        variant="outlined"
                        startIcon={<ChevronLeft/>}
                        onClick={() => router.push('/protected/promotion-task')}
                        sx={{mt: 2}}
                    >
                        返回任务列表
                    </Button>
                </Box>
            </Container>
        )
    }


    const completionRate = (task.taskCount && task.taskCount > 0)
        ? Math.round((task.completedCount / task.taskCount) * 100)
        : 0;


    const totalPages = Math.ceil(totalMaterialCount / itemsPerPage);


    const handlePageChange = (event, value) => {
        setMaterialPage(value);
    }

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>

            <Stack direction="row" spacing={2} alignItems="center" sx={{mb: 4}}>
                <Button
                    variant="outlined"
                    startIcon={<ChevronLeft/>}
                    onClick={() => router.push('/protected/promotion-task')}
                >
                    返回
                </Button>
                <Typography variant="h4" fontWeight="bold">
                    任务详情
                </Typography>
            </Stack>


            <Paper
                elevation={0}
                sx={{
                    p: 3,
                    mb: 4,
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                }}
            >
                <Grid container spacing={3}>

                    <Grid item xs={12} md={4}>
                        <Stack direction="row" spacing={2} alignItems="center">
                            <Box
                                component="img"
                                src={task.productImage || 'https://placehold.co/80x80/e3f2fd/1976d2?text=N/A'}
                                alt={task.productName}
                                sx={{width: 80, height: 80, borderRadius: 2, objectFit: 'cover'}}
                            />
                            <Box>
                                <Typography variant="h5" fontWeight="bold" gutterBottom>
                                    {task.productName || '未知产品'}
                                </Typography>
                                <Chip
                                    label={task.platform || '未知平台'}
                                    color="primary"
                                    size="small"
                                    variant="outlined"
                                    sx={{mr: 1}}
                                />
                            </Box>
                        </Stack>
                        <Typography variant="body2" color="text.secondary" sx={{mt: 2}}>
                            {task.description || '暂无任务描述。'}
                        </Typography>
                    </Grid>


                    <Grid item xs={12} md={4}>
                        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                            发布进度
                        </Typography>
                        <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
                            <Box sx={{width: '100%', mr: 1}}>
                                <LinearProgress
                                    variant="determinate"
                                    value={completionRate}
                                    sx={{height: 10, borderRadius: 1}}
                                />
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                                {completionRate}%
                            </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                            创建时间: {task.createdAt || '未知'}
                        </Typography>
                    </Grid>


                    <Grid item xs={12} md={4}>
                        <Stack spacing={1}>
                            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                                素材统计
                            </Typography>
                            <Stack direction="row" justifyContent="space-between">
                                <Typography variant="body2">素材总数:</Typography>
                                <Typography variant="body2" fontWeight="bold">{totalMaterialCount}</Typography>
                            </Stack>
                            <Stack direction="row" justifyContent="space-between">
                                <Typography variant="body2">已发布:</Typography>
                                <Typography variant="body2" fontWeight="bold" color="success.main">
                                    {task.completedCount ?? 0}
                                </Typography>
                            </Stack>
                            <Stack direction="row" justifyContent="space-between">
                                <Typography variant="body2">发布中:</Typography>
                                <Typography variant="body2" fontWeight="bold" color="primary.main">
                                    {task.inProgressCount ?? 0}
                                </Typography>
                            </Stack>
                        </Stack>
                    </Grid>
                </Grid>


                <Divider sx={{my: 3}}/>
                <Stack direction="row" spacing={2} justifyContent="flex-end">


                    <Button
                        variant="outlined"
                        startIcon={<RefreshCw/>}
                        onClick={handleRefreshStatus}
                        disabled={isRefreshing}
                    >
                        {isRefreshing ? '刷新中...' : '刷新状态'}
                    </Button>

                    <Button
                        variant="outlined"
                        startIcon={<Plus/>}
                        onClick={() => {
                            router.push(`/protected/promotion-task/append/${resolvedParams.id}`);
                        }}
                    >
                        追加推广
                    </Button>


                    <Button
                        variant="outlined"
                        color="error"
                        startIcon={<Trash2/>}
                        onClick={() => setOpenConfirmDialog(true)}
                        disabled={isDeleting || isRefreshing}
                    >
                        {isDeleting ? '删除中...' : '删除任务'}
                    </Button>
                </Stack>
            </Paper>


            <Box sx={{mb: 3}}>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                    素材列表
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    总计 {totalMaterialCount} 个素材
                </Typography>
            </Box>


            {isLoadingMaterials ? (
                <LinearProgress/>
            ) : materials.length > 0 ? (
                <Grid container spacing={3}>
                    {materials.map((material) => (
                        <Grid item xs={12} sm={6} md={4} lg={2.4} key={material.id_}>
                            <ProductCard
                                images={material.images && material.images.length > 0 ? material.images : ['https://placehold.co/300x400/e0f2fe/0c4a6e?text=NoImage']}
                                onClick={() => {
                                }}
                            >
                                <CardContent>
                                    <Typography variant="subtitle1" fontWeight="medium" gutterBottom noWrap>
                                        {material.title || '无标题'}
                                    </Typography>
                                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                                        <Chip
                                            label={material.status || '未知'}
                                            size="small"
                                            color={material.status === '已发布' ? 'success' : 'primary'}
                                            variant={material.status === '已发布' ? 'outlined' : 'filled'}
                                        />
                                        {material.publishedAt && (
                                            <Typography variant="caption" color="text.secondary">
                                                {material.publishedAt}
                                            </Typography>
                                        )}
                                    </Stack>
                                </CardContent>
                            </ProductCard>
                        </Grid>
                    ))}
                </Grid>
            ) : (
                <Typography color="text.secondary" align="center" sx={{mt: 4}}>
                    暂无素材信息。{error && <Typography color="error" variant="caption"><br/>({error})</Typography>}
                </Typography>
            )}


            {totalPages > 1 && (
                <Box sx={{display: 'flex', justifyContent: 'center', mt: 4}}>
                    <Pagination
                        count={totalPages}
                        page={materialPage}
                        onChange={handlePageChange}
                        color="primary"
                        disabled={isLoadingMaterials || isRefreshing}
                    />
                </Box>
            )}

            <ConfirmationDialog
                open={openConfirmDialog}
                onClose={() => setOpenConfirmDialog(false)}
                onConfirm={handleDeleteTask}
                title="确认删除任务"
                description={`您确定要删除推广任务 "${task?.productName || '此任务'}" 吗？此操作不可撤销。`}
                confirmText="确认删除"
                cancelText="取消"
            />
        </Container>
    )
}