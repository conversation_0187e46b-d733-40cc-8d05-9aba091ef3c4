import time

from omni.log.log import olog
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import (
    Product,
    PromotionTask,
    PromotionTaskDetail,
    ProductTraffic,
)


@register_scheduler(trigger="cron", hour="*/2", minute="0")  # 每偶数小时整点执行一次
# @register_scheduler(trigger='interval', seconds=10)  # 每10秒执行一次
class AdvTrafficStatisticsScheduler(BaseScheduler):
    def run_task(self):
        """
        **目的**: 定时统计每个未删除产品的总流量数据，并更新或创建对应的 `ProductTraffic` 记录。

        **算法描述**:
            - **获取产品**: 查询数据库中所有 `is_deleted` 为 `False` 的 `Product` 文档。
            - **遍历产品**: 对获取到的每个 `Product` 文档执行以下操作：
                - **提取信息**: 获取产品的 `id` (转为字符串 `product_id_str`) 和 `user_id` (`user_id_str`)。
                - **查询关联任务**: 根据 `product_id_str` 查询所有关联且未删除的 `PromotionTask` 文档。
                - **处理无关联任务**: 如果没有找到关联的推广任务，则跳过当前产品，处理下一个。
                - **提取任务ID**: 从查询到的任务中提取所有 `id` (转为字符串)，存入 `relevant_task_ids` 列表。
                - **查询任务详情**: 使用 `relevant_task_ids` 列表查询所有相关的 `PromotionTaskDetail` 文档。
                - **处理无任务详情**: 如果没有找到关联的任务详情，则跳过当前产品，处理下一个。
                - **创建任务ID到平台映射**: 从之前查询到的 `PromotionTask` 文档中，创建一个从任务ID (字符串) 到平台名称 (`platform`) 的映射 `task_id_to_platform`。
                - **初始化统计**: 初始化总浏览量 (`total_view_count`)、总点赞量 (`total_like_count`)、总评论量 (`total_comment_count`)、总收藏量 (`total_favorite_count`)、总分享量 (`total_share_count`) 为0，并创建一个空集合 `platforms_set` 用于存储平台名称。
                - **累加统计数据**: 遍历查询到的 `PromotionTaskDetail` 文档：
                    - 将每个详情的 `view_count`, `like_count`, `comment_count`, `favorite_count`, `share_count` (如果值为 `None` 则视为0) 加到对应的总计数变量上。
                    - 使用 `detail.promotion_task_id` 在 `task_id_to_platform` 映射中查找对应的平台名称。
                    - 如果找到平台名称，将其添加到 `platforms_set` 集合中（集合自动去重）。
                - **转换平台列表**: 将 `platforms_set` 集合转换为列表 `platforms_list`。
                - **更新或创建流量记录**: 使用 `user_id_str` 和 `product_id_str` 作为查询条件，在 `ProductTraffic` 集合中执行 `update_one` 操作：
                    - 使用 `upsert=True` 选项，如果记录已存在则更新，不存在则创建。
                    - 更新或设置的字段包括：`platforms` (`platforms_list`), `total_view_count`, `total_like_count`, `total_comment_count`, `total_favorite_count`, `total_share_count`, 以及 `last_updated_at` (当前时间的 Unix 时间戳)。
            - **记录日志**: 在任务开始和结束时，以及处理每个产品和更新其流量数据后，记录相应的日志信息。
        """
        olog.info("开始执行广告主流量统计任务...")

        products = Product.objects(is_deleted=False)
        total_products = products.count()
        olog.info(f"发现 {total_products} 个需要处理的产品。")
        processed_count = 0

        for product in products:
            processed_count += 1
            olog.info(f"正在处理第 {processed_count}/{total_products} 个产品...")

            product_id_str = str(product.id)
            user_id_str = product.user_id

            olog.debug(f"开始处理产品: {product_id_str} (用户: {user_id_str})")

            # 1. 根据 product_id 找到相关的 PromotionTask
            relevant_tasks = PromotionTask.objects(product_id=product_id_str, is_deleted=False)
            relevant_task_ids = [str(task.id) for task in relevant_tasks]

            if not relevant_task_ids:
                olog.debug(f"产品 {product_id_str} 没有关联的推广任务，跳过。")
                continue

            # 2. 使用 promotion_task_id 查询 PromotionTaskDetail
            task_details = PromotionTaskDetail.objects(promotion_task_id__in=relevant_task_ids)

            if not task_details:
                olog.debug(f"产品 {product_id_str} (任务IDs: {relevant_task_ids}) 没有关联的任务详情，跳过。")
                continue

            task_id_to_platform = {
                str(task.id): task.platform for task in relevant_tasks if task.platform
            }

            total_view_count = 0
            total_like_count = 0
            total_comment_count = 0
            total_favorite_count = 0
            total_share_count = 0
            platforms_set = set()

            for detail in task_details:
                total_view_count += detail.view_count or 0
                total_like_count += detail.like_count or 0
                total_comment_count += detail.comment_count or 0
                total_favorite_count += detail.favorite_count or 0
                total_share_count += detail.share_count or 0

                platform = task_id_to_platform.get(detail.promotion_task_id)
                if platform:
                    platforms_set.add(platform)

            platforms_list = list(platforms_set)

            olog.debug(
                f"产品 {product_id_str} 查询结果: views={total_view_count}, likes={total_like_count}, comments={total_comment_count}, favs={total_favorite_count}, shares={total_share_count}, platforms={platforms_list}"
            )

            ProductTraffic.objects(
                user_id=user_id_str, product_id=product_id_str
            ).update_one(
                set__platforms=platforms_list,
                set__total_view_count=total_view_count,
                set__total_like_count=total_like_count,
                set__total_comment_count=total_comment_count,
                set__total_favorite_count=total_favorite_count,
                set__total_share_count=total_share_count,
                set__last_updated_at=int(time.time()),
                upsert=True,
            )
            olog.info(f"产品 {product_id_str} 的流量统计数据已更新.")

        olog.info("广告主流量统计任务执行完毕.")
