import importlib
import time

from omni.config.config_loader import config_dict
from omni.log.log import olog
from omni.scheduler.schedule_register import init_scheduler

User = importlib.import_module('repository.models').User


def _run_uvicorn(options):
    import multiprocessing
    import uvicorn

    workers = (multiprocessing.cpu_count() * 2) + 1

    uvicorn.run(
        "omni.api.gateway:app",
        host="0.0.0.0",
        port=options["port"],
        workers=workers,
        timeout_keep_alive=options["timeout"],
    )


def _run_uvicorn_dev(options):
    import uvicorn

    uvicorn.run(
        "omni.api.gateway:app",
        host="0.0.0.0",
        port=options["port"],
    )


def _init_admin_user():
    username = config_dict.get('admin_user', {}).get('username', 'admin')
    password = config_dict.get('admin_user', {}).get('password', 'y708sWekpvoRdIpF')
    user = User.objects(username=username).first()
    if not user:
        user = User(
            username=username,
            password=password,
            roles=['admin'],
            create_at=time.time(),
        )
        user.save()


def _start_scheduler():
    init_scheduler()
    olog.info('=======Scheduler启动完毕=======')


def _start_api_server():
    process_env = config_dict.get('process_env', 'dev')
    port = config_dict.get('server', {}).get('port', 5000)
    timeout = config_dict.get('server', {}).get('timeout', 30)
    threads = config_dict.get('server', {}).get('threads', 8)

    options = {
        'port': port,
        'timeout': timeout,
        'threads': threads
    }

    olog.info('=======Api启动完毕=======')
    if process_env == 'dev':
        _run_uvicorn_dev(options)
    else:
        _run_uvicorn(options)


def start_all():
    _init_admin_user()
    _start_scheduler()
    _start_api_server()
