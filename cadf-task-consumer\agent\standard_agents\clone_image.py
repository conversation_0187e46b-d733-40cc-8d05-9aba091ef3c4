from agent.utils.openai_image_edit import OpenaiImageEditor
from omni.log.log import olog

"""
克隆图片智能体
"""

def clone_image(image_bytes: bytes) -> bytes:
    """
    根据给定的图片 bytes 编辑图片。
    :param image_bytes: 图片 bytes
    :return: 编辑后的图片 bytes
    """
    prompt_clean_scene = """
    移除图片中所有艺术字体，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素，确保场景干净。
    """
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    # edited_images 现在是一个元组 (bytes, error_type)
    edited_image_data, error_type = editor.generate_image_from_image(
        image_bytes_list=[image_bytes],
        prompt=prompt_clean_scene,
    )
    if error_type != "success" or not edited_image_data:
        olog.error(f"编辑图片失败，错误类型: {error_type}")
        return None

    olog.info(f"成功编辑图片 (clean_scene_images)，返回图片数据。")
    return edited_image_data
    