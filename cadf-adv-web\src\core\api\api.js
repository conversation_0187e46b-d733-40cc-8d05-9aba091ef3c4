"use client";

import { reduxStore } from "@/components/redux-store";
import { LOGIN_PATH } from "@/config";
import { AlertType } from "@/core/components/alert";
import { addAlert } from "@/core/components/redux/alert-slice";
import axios from "axios";
import Cookies from "js-cookie";
import { BASE_URL } from "../../../env";

const api = async (params = {}) => {
  const access_token = Cookies.get("access_token");

  const headers = {
    "Content-Type": "application/json",
    ...(access_token && { Authorization: access_token }),
  };

  const url = `${BASE_URL}/api`;

  const response = await axios({
    method: "post",
    url,
    headers,
    data: params,
  });

  const data = response.data;

  switch (data.code) {
    case 401:
      throw new Error(data.message);
    case 403:
      reduxStore.dispatch(
        addAlert({ type: AlertType.ERROR, message: "请先登录！" })
      );
      Cookies.remove("access_token");
      window.location.href = LOGIN_PATH;
      return {};
    case 500:
      throw new Error(data.message);
    default:
      return data.data;
  }
};

export default api;
