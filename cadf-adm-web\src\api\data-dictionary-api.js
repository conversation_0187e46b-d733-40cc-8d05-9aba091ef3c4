import api from "@/core/api/api";

export const dataDictionaryApi = {
  // 查询所有数据字典条目
  queryAll: async (category) => {
    return await api({
      resource: "data_dictionary",
      method_name: "query_all",
      category,
    });
  },

  // 创建数据字典条目
  create: async (category, key, value) => {
    return await api({
      resource: "data_dictionary",
      method_name: "create",
      category,
      key,
      value,
    });
  },

  // 更新数据字典条目
  update: async (id_, category, key, value) => {
    return await api({
      resource: "data_dictionary",
      method_name: "update",
      id_,
      category,
      key,
      value,
    });
  },

  // 删除数据字典条目
  delete: async (id_) => {
    return await api({
      resource: "data_dictionary",
      method_name: "delete",
      id_,
    });
  },

  // 获取所有类别
  getCategories: async () => {
    return await api({
      resource: "data_dictionary",
      method_name: "get_categories",
    });
  },

  // 根据ID获取数据字典条目
  getById: async (id_) => {
    return await api({
      resource: "data_dictionary",
      method_name: "get_by_id",
      id_,
    });
  },
};
