"""
mongo:
  url: mongodb://aqua:<EMAIL>:27017/aqua
"""
from bson import ObjectId
from mongoengine import connect, EmbeddedDocumentField, ListField

from omni.config.config_loader import config_dict


def init_mongo_client():
    connect(host=config_dict['mongo']['url'])


def docs_to_dict(docs):
    documents_list = []
    for doc in docs:
        doc = doc_to_dict(doc)
        documents_list.append(doc)
    return documents_list


def convert_object_id(value):
    """递归地将 ObjectId 转换为字符串"""
    if isinstance(value, ObjectId):
        return str(value)
    elif isinstance(value, list):
        return [convert_object_id(item) for item in value]
    elif isinstance(value, dict):
        return {k: convert_object_id(v) for k, v in value.items()}
    return value


def doc_to_dict(doc):
    if not doc:
        return {}

    # 如果 doc 不是字典类型，先转换为字典
    if not isinstance(doc, dict):
        doc = doc.to_mongo().to_dict()

    # 转换所有的 ObjectId
    doc = convert_object_id(doc)

    # 处理 '_id' 字段
    if '_id' in doc:
        doc['id_'] = doc['_id']
        del doc['_id']

    return doc


# 已验证的数据转化为可被mongoengine保存的数据
def mongo_data_intersection(clazz, data):
    defined_fields = set(clazz._fields.keys())
    data_keys = set(data.keys())
    intersection_keys = defined_fields.intersection(data_keys)
    intersection_data = {key: data[key] for key in intersection_keys}
    return intersection_data


def save_or_update(model, data, doc_id=None):
    def filter_data(data, fields):
        filtered = {}
        for key, value in data.items():
            if key in fields:
                field = fields[key]
                if isinstance(field, EmbeddedDocumentField) and isinstance(value, dict):
                    filtered[key] = filter_data(value, field.document_type._fields)
                elif isinstance(field, ListField) and isinstance(field.field, EmbeddedDocumentField) and isinstance(value, list):
                    filtered[key] = [filter_data(item, field.field.document_type._fields) for item in value]
                else:
                    filtered[key] = value
        return filtered

    allowed_fields = model._fields
    filtered_data = filter_data(data, allowed_fields)

    if doc_id:
        document = model.objects(id=doc_id).first()
        if document:
            document.update(**filtered_data)
        else:
            document = model(**filtered_data)
            document.save()
    else:
        document = model(**filtered_data)
        document.save()
    return str(document['id'])
