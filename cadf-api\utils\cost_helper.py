import time
from decimal import Decimal
from repository.models import CostUserAccount, CostRechargeRecord, CostConsumptionRecord
from config.config import COST_PROJECTS
from omni.log.log import olog



def process_recharge(user_id, amount, recharge_method="手工"):
    """
    一键处理充值：创建充值记录 + 更新用户余额

    Args:
        user_id: 用户ID
        amount: 充值金额
        recharge_method: 充值方式，默认"手工"

    Returns:
        dict: 包含充值记录ID和更新后的余额
    """
    olog.info(f"开始处理用户 {user_id} 充值 {amount} 元，充值方式：{recharge_method}")

    # 获取或创建用户账户
    user_account = CostUserAccount.objects(user_id=user_id).first()
    if not user_account:
        user_account = CostUserAccount(
            user_id=user_id,
            balance=Decimal('0'),
            updated_at=int(time.time())
        )
        user_account.save()
        olog.info(f"为用户 {user_id} 创建新账户")

    # 创建充值记录
    recharge_record = CostRechargeRecord(
        cost_user_account_id=user_id,
        amount=Decimal(str(amount)),
        recharge_method=recharge_method,
        status="成功",
        created_at=int(time.time()),
        updated_at=int(time.time())
    )
    recharge_record.save()
    olog.info(f"创建充值记录：{recharge_record.id}")

    # 更新用户余额
    user_account.balance += Decimal(str(amount))
    user_account.updated_at = int(time.time())
    user_account.save()

    olog.info(f"用户 {user_id} 充值成功，当前余额：{user_account.balance} 元")

    return {
        "recharge_record_id": str(recharge_record.id),
        "new_balance": float(user_account.balance)
    }


def process_consumption(user_id, project_name, amount, description=""):
    """
    一键处理消费：检查余额 + 扣除余额 + 创建消费记录

    Args:
        user_id: 用户ID
        project_name: 项目名称（中文），可选值：图片生成、流量结算
        amount: 消费金额
        description: 消费描述

    Returns:
        dict: 包含消费记录ID和剩余余额
    """
    # 验证项目名称是否有效
    if project_name not in COST_PROJECTS:
        olog.error(f"无效的项目名称：{project_name}，有效项目：{list(COST_PROJECTS.keys())}")
        raise Exception(f"无效的项目名称：{project_name}")

    olog.info(f"开始处理用户 {user_id} 消费 {amount} 元，项目：{project_name}")

    # 获取或创建用户账户
    user_account = CostUserAccount.objects(user_id=user_id).first()
    if not user_account:
        user_account = CostUserAccount(
            user_id=user_id,
            balance=Decimal('0'),
            updated_at=int(time.time())
        )
        user_account.save()
        olog.info(f"为用户 {user_id} 创建新账户")

    # 检查余额是否充足
    consumption_amount = Decimal(str(amount))
    if user_account.balance < consumption_amount:
        olog.error(f"用户 {user_id} 余额不足，当前余额：{user_account.balance}，需要：{consumption_amount}")
        raise Exception(f"余额不足，当前余额：{user_account.balance}元，需要：{consumption_amount}元")

    # 扣除余额
    user_account.balance -= consumption_amount
    user_account.updated_at = int(time.time())
    user_account.save()

    # 创建消费记录
    consumption_record = CostConsumptionRecord(
        cost_user_account_id=user_id,
        project_name=project_name,
        amount=consumption_amount,
        consumption_type="一次性",
        description=description,
        created_at=int(time.time())
    )
    consumption_record.save()

    olog.info(f"用户 {user_id} 消费成功，消费金额：{consumption_amount}，剩余余额：{user_account.balance}")

    return {
        "consumption_record_id": str(consumption_record.id),
        "remaining_balance": float(user_account.balance)
    }
