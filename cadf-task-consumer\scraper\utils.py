from omni.log.log import olog
import re
from typing import Literal


def convert_mongo_cookies_to_playwright(mongo_cookies: list[dict]) -> list[dict]:
    """将从 MongoDB 获取的 Cookie 字典列表转换为 Playwright 兼容的格式列表"""
    playwright_cookies = []
    for mongo_cookie in mongo_cookies:
        try:
            playwright_cookie = {
                'name': mongo_cookie.get('name'),
                'value': mongo_cookie.get('value'),
                'domain': mongo_cookie.get('domain'),
                'path': mongo_cookie.get('path'),
                'secure': mongo_cookie.get('secure', False),
                'httpOnly': mongo_cookie.get('httpOnly', False),
                'expires': -1  # 默认会话 Cookie
            }

            # 处理 expires 字段
            if not mongo_cookie.get('session', True) and 'expirationDate' in mongo_cookie:
                try:
                    # expirationDate 可能是浮点数或整数形式的 Unix 时间戳 (秒)
                    playwright_cookie['expires'] = int(mongo_cookie['expirationDate'])
                except (ValueError, TypeError):
                    playwright_cookie['expires'] = -1  # 无效日期视为会话 Cookie

            # 处理 sameSite 字段
            mongo_same_site = mongo_cookie.get('sameSite', 'unspecified').lower()
            if mongo_same_site == 'no_restriction':
                playwright_cookie['sameSite'] = 'None'
            elif mongo_same_site == 'lax':
                playwright_cookie['sameSite'] = 'Lax'
            elif mongo_same_site == 'strict':
                playwright_cookie['sameSite'] = 'Strict'
            else:  # 处理 'unspecified' 或其他未知值
                if playwright_cookie.get('secure'): # Check if 'secure' key exists and is True
                    playwright_cookie['sameSite'] = 'None'
                else:
                    playwright_cookie['sameSite'] = 'Lax'

            # 添加过滤后的 cookie
            playwright_cookies.append({k: v for k, v in playwright_cookie.items() if v is not None})
        except Exception as e:
            # 可以选择记录转换单个 cookie 时的错误
            olog.warning(f"转换单个 Cookie 时出错: {e}, Cookie 数据: {mongo_cookie}")
            continue # 跳过有问题的 cookie

    return playwright_cookies


def extract_url_from_text(text: str, platform_name: Literal["小红书"]) -> str | None:
    """
    根据平台名称从分享文本中提取对应的 URL。

    Args:
        text: 分享文本内容。
        platform_name: 平台名称，目前支持 "小红书"。

    Returns:
        提取到的 URL 字符串，如果无法匹配则返回 None。
    """
    if not isinstance(text, str):
        return None  # 输入必须是字符串

    url_match = None
    if platform_name == "小红书":
        url_match = re.search(r"(https?://(?:www\.xiaohongshu\.com|xhslink\.com)/[^\s，]+)", text)
    return url_match.group(0) if url_match else None