from typing import Optional  # 导入Optional类型

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from omni.llm.chat.chat_model_factory import ChatModelFactory

PROMPT = """
给我讲一个关于{topic}的笑话
{format_instructions}
"""


class Joke(BaseModel):
    """用于向用户讲笑话的模型。"""

    setup: str = Field(description="笑话的铺垫部分")
    punchline: str = Field(description="笑话的包袱部分")
    rating: Optional[int] = Field(default=None, description="笑话的评分，从1到10")


def example_structured_output():
    """使用LangChain的ChatPromptTemplate来生成关于特定主题的笑话。"""

    prompt = ChatPromptTemplate.from_template(PROMPT)
    llm = ChatModelFactory.get_llm()
    parser = PydanticOutputParser(pydantic_object=Joke)

    chain = prompt | llm | parser
    chain = chain.with_retry()
    params = {"topic": "猫咪", "format_instructions": parser.get_format_instructions()}
    result = chain.invoke(params)
    print(result)
