from langchain.agents import Agent<PERSON>xecutor, create_tool_calling_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool

from omni.llm.chat.chat_model_factory import ChatModelFactory


@tool(return_direct=True)
def add(a: int, b: int) -> int:
    """将两个数相加并返回结果。"""
    return a + b


def example_tool_call():
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个有用的助手"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}")
    ])
    llm = ChatModelFactory.get_llm()
    tools = [add]

    agent = create_tool_calling_agent(llm, tools, prompt)
    agent_executor = AgentExecutor(agent=agent, tools=tools)
    agent_executor = agent_executor.with_retry()
    params = {"input": "你能提供哪些工具?"}
    result = agent_executor.invoke(params)
    print(result.get("output"))
