import base64

import httpx
from langchain_core.messages import HumanMessage

from omni.llm.chat.chat_model_factory import ChatModelFactory
from utils.image_tools import convert_png_base64_encode

PROMPT = """
# 角色
你是一名图像识别专家。

# 背景
你收到了多张关于某个产品或服务的图片。

# 任务
根据输入的多张图片内容，识别图片中展示的产品或服务的**具体名称**。

# 约束
1.  整合所有图片的信息进行识别。
2.  如果图片中没有明确的产品名称，请根据产品的外观和特征进行推断。
3.  只返回产品名称，不要包含其他描述性文字。

# 返回值
返回识别出的产品名称文本。
"""


def product_name_image_recognition_agent(image_urls: list[str]) -> str:
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")

    image_data = [
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{convert_png_base64_encode(url)}"
            },
        }
        for url in image_urls
    ]

    human_message_content = [
        {"type": "text", "text": PROMPT},
        *image_data,
    ]

    messages = [HumanMessage(content=human_message_content)]

    chain = llm

    result = chain.invoke(messages)
    return result.content.strip()
