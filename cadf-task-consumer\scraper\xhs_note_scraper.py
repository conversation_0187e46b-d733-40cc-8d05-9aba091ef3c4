import json
import re
from datetime import datetime, timedelta, timezone

from fake_useragent import UserAgent
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError, Page

from config.config import PLAYWRIGHT_HEADLESS, USE_PROXY
from omni.log.log import olog
from .proxy_server import ProxyServer
from .xhs_online_status_scraper import is_on_login_page
from .utils import convert_mongo_cookies_to_playwright, extract_url_from_text

"""
抓取小红书笔记的标题、内容、编辑时间、图片、评论
"""

# Helper function to parse relative time strings
def _parse_relative_time(time_str: str) -> int | str:
    shanghai_tz = timezone(timedelta(hours=8))
    now = datetime.now(shanghai_tz)  # Use Shanghai timezone
    time_str_original = time_str  # Keep original for logging
    # Simplified cleaning: just strip leading/trailing space and potential "编辑于/发布于"
    time_str_cleaned = re.sub(r'^(编辑于|发布于)\s+', '', time_str.strip()).strip()

    try:
        # 1. 处理特定格式 (新增: 允许后面有任意字符，用于匹配位置后缀)
        if re.match(r"刚刚\s*.*", time_str_cleaned):
            dt = now
        elif re.match(r"(\\d+)\s*分钟前\s*.*", time_str_cleaned):
            match = re.search(r'(\\d+)', time_str_cleaned)
            minutes = int(match.group(1))
            dt = now - timedelta(minutes=minutes)
        elif re.match(r"(\\d+)\s*小时前\s*.*", time_str_cleaned):
            match = re.search(r'(\\d+)', time_str_cleaned)
            hours = int(match.group(1))
            dt = now - timedelta(hours=hours)
        elif re.match(r"(\\d+)\s*天前\s*.*", time_str_cleaned):
            match = re.search(r'(\\d+)', time_str_cleaned)
            days = int(match.group(1))
            dt = now - timedelta(days=days)

        # 2. 处理今天/昨天/前天格式 (保留原有的后缀去除逻辑，确保提取到 HH:MM)
        elif time_str_cleaned.startswith("今天"):  # Format: 今天 HH:MM [Location]
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                dt = datetime.strptime(f"{now.strftime('%Y-%m-%d')} {time_part}", "%Y-%m-%d %H:%M").replace(tzinfo=shanghai_tz)
            else:
                olog.warning(f"无法从 '今天' 格式提取时间部分: Cleaned='{time_str_cleaned}', Processed='{time_str_no_suffix}', Original='{time_str_original}'")
                return time_str_original
        elif time_str_cleaned.startswith("昨天"):  # Format: 昨天 HH:MM [Location]
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                yesterday = now - timedelta(days=1)
                dt = datetime.strptime(f"{yesterday.strftime('%Y-%m-%d')} {time_part}", "%Y-%m-%d %H:%M").replace(tzinfo=shanghai_tz)
            else:
                olog.warning(f"无法从 '昨天' 格式提取时间部分: Cleaned='{time_str_cleaned}', Processed='{time_str_no_suffix}', Original='{time_str_original}'")
                return time_str_original
        elif time_str_cleaned.startswith("前天"):  # Format: 前天 HH:MM [Location]
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                day_before = now - timedelta(days=2)
                dt = datetime.strptime(f"{day_before.strftime('%Y-%m-%d')} {time_part}", "%Y-%m-%d %H:%M").replace(tzinfo=shanghai_tz)
            else:
                olog.warning(f"无法从 '前天' 格式提取时间部分: Cleaned='{time_str_cleaned}', Processed='{time_str_no_suffix}', Original='{time_str_original}'")
                return time_str_original
        else:
            # 3. 处理 MM-DD 和 YYYY-MM-DD 格式 (保留原有的后缀去除和解析逻辑)
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()

            if re.match(r"\\d{1,2}-\\d{1,2}", time_str_no_suffix):  # Format: MM-DD [HH:MM]
                date_part = time_str_no_suffix
                time_part = "00:00"
                if ' ' in time_str_no_suffix:
                    parts = time_str_no_suffix.split(' ', 1)
                    date_part = parts[0]
                    if re.match(r'\d{1,2}:\d{2}', parts[1]):
                        time_part = parts[1]

                parsed_date = datetime.strptime(f"{now.year}-{date_part} {time_part}", "%Y-%m-%d %H:%M")
                if parsed_date.date() > now.date():
                    dt = parsed_date.replace(year=now.year - 1, tzinfo=shanghai_tz)
                else:
                    dt = parsed_date.replace(tzinfo=shanghai_tz)
            elif re.match(r"\\d{4}-\\d{1,2}-\\d{1,2}", time_str_no_suffix):  # Format: YYYY-MM-DD [HH:MM]
                # Assuming YYYY-MM-DD format includes full date and potentially time,
                # need to parse the whole string if it includes time.
                try:
                     dt = datetime.strptime(time_str_no_suffix, "%Y-%m-%d %H:%M").replace(tzinfo=shanghai_tz)
                except ValueError:
                     dt = datetime.strptime(time_str_no_suffix, "%Y-%m-%d").replace(tzinfo=shanghai_tz)
            else:
                olog.warning(f"无法解析时间格式: Cleaned='{time_str_cleaned}', Processed='{time_str_no_suffix}', Original='{time_str_original}'")
                return time_str_original

        # Return as integer Unix timestamp
        timestamp = int(dt.timestamp())
        return timestamp

    except Exception as e:
        # Log the state before potential suffix removal if it failed
        olog.error(f"解析时间字符串 '{time_str_original}' (cleaned: '{time_str_cleaned}') 时出错: {e}")
        return time_str_original


def _extract_comments(page: Page, olog) -> list[dict[str, str]]:
    """
    滚动 note-scroller 区域加载并提取页面上的所有评论，直到遇到结束标志或评论数不再增加。
    :param page: Playwright 的 Page 对象。
    :param olog: 日志记录器。
    :return: 包含评论作者和文本的字典列表。
    """
    comments = []
    comment_selector = '.comment-item'
    scroll_container_selector = '.note-scroller'
    end_container_selector = '.end-container'
    no_comments_selector = '.no-comments-text'  # 新增的选择器
    last_comment_count = 0
    previous_scroll_height = -1  # Initialize with a value that cannot be a scroll height

    # 新增：检查是否存在 "暂无评论" 的标志
    no_comments_element = page.query_selector(no_comments_selector)
    if no_comments_element:
        olog.info("检测到 '暂无评论' 标志，无需滚动。")
        return comments

    olog.info("开始滚动加载评论直到找到结束标志...")

    scroll_container_element = page.query_selector(scroll_container_selector)
    if not scroll_container_element:
        olog.error(f"无法找到滚动容器: {scroll_container_selector}")
        return comments

    # Loop indefinitely until break conditions are met
    while True:
        # 检查是否出现结束标志
        end_element = page.query_selector(end_container_selector)
        if end_element:
            olog.info("检测到评论区底部标志，停止滚动。")
            break

        current_comment_elements = page.query_selector_all(comment_selector)
        current_comment_count = len(current_comment_elements)
        olog.info(f"当前评论数: {current_comment_count}, 上次评论数: {last_comment_count}")

        # Check if comment count stopped increasing *and* scroll height didn't change (more robust check)
        current_scroll_height = page.evaluate('(element) => element.scrollHeight', scroll_container_element)

        if current_comment_count == last_comment_count and current_scroll_height == previous_scroll_height and last_comment_count > 0:
            olog.info("滚动后评论数量和滚动高度均未增加，且未到底部，可能已加载完毕或出现问题，停止滚动。")
            break

        last_comment_count = current_comment_count
        previous_scroll_height = current_scroll_height

        try:
            # 滚动指定的容器
            page.evaluate('(element) => { element.scrollTop = element.scrollHeight; }', scroll_container_element)
            olog.info("已滚动滚动容器到底部，等待 3 秒加载...")
            page.wait_for_timeout(3000)
        except Exception as scroll_err:
            olog.warning(f"滚动页面或等待时出错: {scroll_err}")
            break

    final_comment_elements = page.query_selector_all(comment_selector)
    olog.info(f"滚动结束，最终找到 {len(final_comment_elements)} 条评论元素进行提取。")

    for comment_element in final_comment_elements:
        comment_text = "未找到评论内容"
        comment_time_str = "未找到时间"
        comment_author = "未找到作者"

        text_element = comment_element.query_selector('.content .note-text')
        if text_element:
            comment_text = text_element.inner_text().strip()
            comment_text = re.sub(r"^回复 @[^:]+ : ", "", comment_text)

        time_element = comment_element.query_selector('.info .date span:first-child')
        if time_element:
            comment_time_str = time_element.inner_text().strip()

        author_element = comment_element.query_selector('.author .name')
        if author_element:
            comment_author = author_element.inner_text().strip()

        if comment_text != "未找到评论内容":
            # 尝试解析评论时间
            parsed_comment_time = _parse_relative_time(comment_time_str)

            is_duplicate = any(
                c['author'] == comment_author and
                c['text'] == comment_text and
                c['time_str'] == comment_time_str  # Use original time string for duplicate check
                for c in comments
            )

            if not is_duplicate:
                comment_data = {
                    "author": comment_author,
                    "text": comment_text,
                    "time_str": comment_time_str,  # Keep original string
                    "timestamp": parsed_comment_time  # Add parsed timestamp/original string
                }
                comments.append(comment_data)
            else:
                olog.debug(f"检测到重复评论，跳过: 作者={comment_author}, 内容={comment_text[:20]}..., 时间={comment_time_str}")

    olog.info(f"评论提取完成，共提取 {len(comments)} 条有效评论。")
    return comments


def xhs_note_scraper(xhs_url: str, mongo_cookies: list[dict], scrape_comments: bool = False):
    """
    处理单个小红书笔记爬取任务。
    :param xhs_url: 包含小红书笔记 URL 的字符串。
    :param mongo_cookies: 从 MongoDB 获取的原始 Cookie 列表。
    :param scrape_comments: 是否爬取评论，默认为 False。
    """
    # 调用 utils.py 中的 extract_url_from_text 函数来提取 URL
    url = extract_url_from_text(xhs_url, "小红书")
    if not url:
        olog.error(f"无法从输入中提取有效的小红书 URL: {xhs_url}")
        return

    olog.info(f"开始爬取小红书笔记: {url}")
    scraped_data = None

    try:
        with sync_playwright() as p:
            browser_launch_options = {'headless': PLAYWRIGHT_HEADLESS}
            ua = UserAgent(browsers=['Chrome', 'Firefox'])
            context_options = {
                'user_agent': ua.random,
                'viewport': {'width': 1920, 'height': 1080}
            }

            if USE_PROXY:
                proxy_manager = ProxyServer()
                proxy_info = proxy_manager.get_proxy_server()
                if proxy_info:
                    # Add proxy to context options
                    context_options["proxy"] = {
                        "server": proxy_info["server"],
                        "username": proxy_info["username"],
                        "password": proxy_info["password"]
                    }
                    olog.info(f"正在使用代理服务器: {proxy_info['server']}")
                else:
                    olog.warning("配置了使用代理但无法获取代理信息，将不使用代理。")
            else:
                olog.info("未配置使用代理。")

            # 从 browser_launch_options 移除代理配置
            browser = p.chromium.launch(**browser_launch_options)
            # 在 new_context 时应用 context_options (已包含代理信息)
            context = browser.new_context(**context_options)

            playwright_cookies = [] # Initialize as empty list
            if mongo_cookies:
                try:
                    playwright_cookies = convert_mongo_cookies_to_playwright(mongo_cookies)
                    context.add_cookies(playwright_cookies)
                    olog.info(f"尝试添加 {len(playwright_cookies)} 个转换后的 Cookie。")
                except Exception as cookie_error:
                    olog.error(f"添加或转换 Cookie 时出错: {cookie_error}")
            else:
                olog.warning("未提供 Cookie 列表，将以未登录状态爬取。")

            page = context.new_page()

            try:
                page.goto(url, wait_until="domcontentloaded", timeout=60000)

                # 添加登录检查
                if is_on_login_page(page):
                    olog.error(f"检测到登录页面，无法抓取笔记: {url}。请检查 Cookie 或登录状态。")
                    browser.close()
                    return None

                page.wait_for_selector('#noteContainer', timeout=30000)

                content_selector = '#noteContainer .content, .note-content'
                content_element = page.query_selector(content_selector)
                raw_content = content_element.inner_text().strip() if content_element else "未找到内容"

                title = "未找到标题"
                content = ""
                edit_time_str = "未找到编辑时间"  # 初始化编辑时间

                if raw_content != "未找到内容" and raw_content:
                    lines = raw_content.split('\n')
                    if lines:
                        # 第一行作为标题
                        title = lines[0].strip()

                        if len(lines) > 1:
                            # 最后一行作为编辑时间
                            potential_edit_time = lines[-1].strip()
                            # 可以添加一个简单的检查，比如是否包含时间相关的字符，但这里我们假设最后一行就是
                            edit_time_str = potential_edit_time
                            # 尝试解析相对时间
                            parsed_edit_time = _parse_relative_time(edit_time_str)

                            # 中间的行（如果存在）作为 content
                            if len(lines) > 2:
                                content_lines = [line.strip() for line in lines[1:-1] if line.strip()]
                                content = '\n'.join(content_lines)
                            else:
                                # 如果只有两行（标题和编辑时间），则内容为空
                                content = ""
                        else:
                            # 如果只有一行，则编辑时间未找到，内容也为空
                            edit_time_str = "未找到编辑时间"
                            content = ""
                            parsed_edit_time = edit_time_str  # 如果没找到，保持"未找到"

                images = []
                # Refined selector based on the HTML snippet provided earlier
                img_selector = '#noteContainer .swiper-slide .note-slider-img'

                try:
                    # Wait a bit for images potentially loaded by JS
                    page.wait_for_selector(img_selector, timeout=10000)
                except PlaywrightTimeoutError:
                    olog.warning(f"图片选择器 '{img_selector}' 未在 10 秒内找到，可能没有图片或页面结构已更改。")
                    # Try the old selector as a fallback
                    img_selector = '#noteContainer .slide-container img, #noteContainer .player-container img, #noteContainer .note-image img'
                    try:
                        page.wait_for_selector(img_selector, timeout=5000)
                    except PlaywrightTimeoutError:
                        olog.warning(f"备用图片选择器 '{img_selector}' 也未找到。")

                img_elements = page.query_selector_all(img_selector)
                olog.info(f"找到 {len(img_elements)} 个潜在的图片元素。")

                for img in img_elements:
                    src = img.get_attribute('src')
                    data_src = img.get_attribute('data-src')
                    image_url = None

                    # Prioritize src attribute if it's a valid http(s) URL
                    if src and src.startswith('http'):
                        image_url = src
                    # Fallback to data-src if src is invalid or not http(s)
                    elif data_src and data_src.startswith('http'):
                        image_url = data_src
                        olog.debug(f"使用 data-src 替代 src: {image_url}")
                    elif src:
                        olog.warning(f"找到 src 但不是有效的 http URL: {src}")
                    elif data_src:
                        olog.warning(f"找到 data-src 但不是有效的 http URL: {data_src}")
                    # Add more fallbacks if needed, e.g., parsing srcset

                    if image_url and image_url not in images:  # Avoid adding duplicates if selector matches multiple times for same image
                        # Optional: Clean potential URL parameters if they cause issues
                        # image_url = image_url.split('?')[0]
                        images.append(image_url)
                    elif not image_url:
                        olog.warning("某个 img 元素未能提取到有效的 URL。")

                if not images:
                    olog.warning("未能提取到任何图片 URL。请检查选择器和页面结构。")
                else:
                    # 将第一张图片移到列表末尾
                    first_image = images.pop(0)
                    images.append(first_image)
                    olog.info("已将第一张图片移到列表末尾。")

                comments = []
                if scrape_comments:
                    olog.info("开始提取评论...")
                    comments = _extract_comments(page, olog)
                else:
                    olog.info("根据设置跳过评论提取。")

                scraped_data = {
                    "url": url,
                    "title": title,
                    "content": content,
                    "edit_time": parsed_edit_time,  # 使用解析后的时间
                    "images": images,
                    "comments": comments
                }
                # 修改日志输出，移除笔记作者信息
                # Log the timestamp if it's an int, otherwise log the original string
                log_edit_time = parsed_edit_time if isinstance(parsed_edit_time, str) else str(parsed_edit_time)
                olog.info(f"成功爬取: {url}, 标题: {scraped_data.get('title')}, 编辑时间戳/原文: {log_edit_time}, 图片数: {len(images)}, 评论数: {len(comments)}")


            except PlaywrightTimeoutError:
                olog.error(f"页面加载超时或选择器未找到: {url}")
            except Exception as page_error:
                olog.error(f"处理页面时出错 {url}: {page_error}")
            finally:
                page.close()
                context.close()
                browser.close()

    except Exception as e:
        olog.error(f"Playwright 启动或执行时出错 {url}: {e}")

    if scraped_data:
        olog.info(f"爬取到的数据: {json.dumps(scraped_data, indent=2, ensure_ascii=False)}")
        pass
    else:
        olog.warning(f"未能成功爬取数据: {url}")

    return scraped_data
