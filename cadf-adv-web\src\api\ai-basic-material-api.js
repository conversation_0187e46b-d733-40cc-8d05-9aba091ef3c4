import api from "@/core/api/api";

export const aiBasicMaterialApi = {
    create: async (share_url) => {
        return await api({
            resource: "ai_basic_material",
            method_name: "create",
            share_url,
        });
    },
    delete: async (id_) => {
        return await api({
            resource: "ai_basic_material",
            method_name: "delete",
            id_,
        });
    },
    queryAll: async (search, page, page_size) => {
        return await api({
            resource: "ai_basic_material",
            method_name: "query_all",
            search,
            page,
            page_size,
        });
    },
    getById: async (id_) => {
        return await api({
            resource: "ai_basic_material",
            method_name: "get_by_id",
            id_,
        });
    },
}; 