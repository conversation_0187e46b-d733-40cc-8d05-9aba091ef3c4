import importlib

from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.asr.tencent_asr import TencentASR
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod

config_module = importlib.import_module('config.config')


# 语音识别
@register_handler('asr')
class AsrApi:

    @auth_required(['admin', 'user'])
    def long_text_recognition(self, data):
        file_key = data['file_key']
        url = OSSClient.getInstance().signed_url(SignedMethod.GET, file_key)
        sentence = TencentASR.getInstance().long_text_recognition(url)
        return sentence
