import base64
import io
import random
from typing import List

from PIL import Image
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

from omni.llm.agent.structured_output_agent import structured_output_agent
from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog

"""
从图片中识别文字内容，并结合额外文本生成配文的智能体
"""


class OcrOutput(BaseModel):
    """OCR 识别的结构化输出"""
    has_text: bool = Field(..., description="是否识别到文字")
    content: str = Field(..., description="识别的文字内容")


def ocr_image_text(image_bytes: bytes) -> OcrOutput:
    """
    从图片中识别文字内容。
    """
    olog.info("开始执行图片文字识别...")
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    img = Image.open(io.BytesIO(image_bytes))
    original_format = img.format or "PNG"  # 默认格式为PNG
    olog.info(f"图片格式: {original_format}")

    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=original_format)
    base64_str = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")

    llm_image_input = {
        "type": "image_url",
        "image_url": {"url": f"data:image/{original_format.lower()};base64,{base64_str}"},
    }

    prompt = """
# 角色
你是一个专业的图像文字识别引擎。

# 任务
请识别并提取图片中的艺术字。
不要识别印刷在产品上的logo、品牌名字、使用说明等文字。
如果图片中没有艺术字，请明确告知。

# 返回值
请严格按照如下 JSON 结构返回：
{
  "has_text": 是否识别到文字（布尔值，true/false），
  "content": "识别的文字内容（字符串，若无则为空字符串）"
}
"""

    human_message_content = [
        {"type": "text", "text": prompt},
        llm_image_input,
    ]

    raw_result = llm.invoke([HumanMessage(content=human_message_content)]).content
    olog.info(f"图片文字识别完成: {raw_result}")
    structured_result = structured_output_agent(model_class=OcrOutput, raw_data=raw_result)
    if len(structured_result.content.strip()) <= 10:
        structured_result.has_text = False
    return structured_result


class CaptionItem(BaseModel):
    """单条配文条目"""
    title: str = Field('', description="配文的标题（可为空）")
    contents: List[str] = Field(default_factory=list, description="配文的内容数组（可为空）")


class CaptionOutput(BaseModel):
    """生成配文的结构化输出，包含多个条目"""
    items: List[CaptionItem] = Field(default_factory=list, description="配文条目数组")


def generate_caption_with_ocr_text(ocr_text: str, additional_text: str) -> CaptionOutput:
    """
    结合图片中识别的文字和额外提供的文本，生成新的配文。
    """
    group_num = random.choices([1, 2, 3], weights=[0.4, 0.4, 0.2])[0]
    olog.info("开始执行 _generate_caption_with_ocr_text ...")
    olog.info(f"输入OCR文字: {ocr_text}")
    olog.info(f"输入额外文本: {additional_text}")

    prompt = f"""
# 角色
你是一名创意文案专家，擅长为图片生成吸引人的艺术字配文。

# 背景信息
- 图片中的艺术字内容: "{ocr_text}"
- 用户提供的额外信息: "{additional_text}"

# 任务
根据上述背景信息，生成一组或多组全新的、有创意、吸引人的图片艺术字配文。
配文的字数与段数与'图片中的艺术字内容'相似
每组配文包含一个标题（可以为空，根据实际情况判断是否生成标题）和若干配文内容。
合理的使用带emoji的小红书配文风格
小红书的emoji一般出现在开头
emoji与文字之间要有一个空格
不要每一个配文内容都有emoji表情
emoji规避黑色等负面颜色
配文标题不超过10个字
配文内容不超过10个字
每组配文内容的个数不超过2个
配文组数限制在{group_num}组

# 返回值
- items: 配文条目数组
  - title: 配文标题（可为空）
  - contents: 配文内容数组
"""
    llm = ChatModelFactory.get_llm("QWEN_MAX")
    raw_result = llm.invoke(
        [HumanMessage(content=[{"type": "text", "text": prompt}])]).content
    olog.info(f"新配文生成成功: {raw_result}")
    structured_result = structured_output_agent(model_class=CaptionOutput, raw_data=raw_result)
    return structured_result


def generate_caption_from_image_text(image_bytes: bytes, additional_text: str) -> dict:
    """
    外部方法：先从图片中识别文字，然后结合识别出的文字和额外文本生成新配文。
    """
    olog.info("开始执行 generate_caption_from_image_text ...")

    ocr_result = ocr_image_text(image_bytes)
    if not ocr_result.has_text:
        ocr_result.content = ""
    final_caption = generate_caption_with_ocr_text(ocr_result.content, additional_text)
    return final_caption.model_dump()
