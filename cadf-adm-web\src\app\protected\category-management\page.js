"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Box, 
  Typography, 
  Paper, 
  Button, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Chip,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { dataDictionaryApi } from "@/api/data-dictionary-api";

export default function DataDictionaryManagement() {
  const theme = useTheme();
  const router = useRouter();
  const dispatch = useDispatch();
  
  const [dictionaries, setDictionaries] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loading, setLoading] = useState(false);
  
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [dictionaryToDelete, setDictionaryToDelete] = useState(null);

  // 加载数据字典列表
  const loadDictionaries = async (category = '') => {
    try {
      setLoading(true);
      const response = await dataDictionaryApi.queryAll(category);
      setDictionaries(response.dictionaries || []);
    } catch (error) {
      dispatch(addAlert({ type: AlertType.ERROR, message: '加载数据失败' }));
    } finally {
      setLoading(false);
    }
  };

  // 加载类别列表
  const loadCategories = async () => {
    try {
      const response = await dataDictionaryApi.getCategories();
      setCategories(response.categories || []);
    } catch (error) {
      console.error('加载类别失败:', error);
    }
  };

  useEffect(() => {
    loadDictionaries();
    loadCategories();
  }, []);

  // 监听页面焦点，当从其他页面返回时重新加载数据
  useEffect(() => {
    const handleFocus = () => {
      loadDictionaries(selectedCategory);
      loadCategories();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [selectedCategory]);

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    loadDictionaries(category);
  };

  const handleCreate = () => {
    router.push('/protected/category-management/create');
  };

  const handleEdit = (dictionary) => {
    router.push(`/protected/category-management/edit/${dictionary.id_}`);
  };

  const handleDeleteConfirm = (dictionary) => {
    setDictionaryToDelete(dictionary);
    setDeleteConfirmOpen(true);
  };

  const handleDelete = async () => {
    try {
      await dataDictionaryApi.delete(dictionaryToDelete.id_);
      setDeleteConfirmOpen(false);
      setDictionaryToDelete(null);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.DELETE }));
      loadDictionaries(selectedCategory);
      loadCategories(); // 重新加载类别列表
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '删除失败' 
      }));
    }
  };



  const renderValue = (value) => {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1" fontWeight="bold">
          数据字典管理
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<Plus size={18} />}
          onClick={handleCreate}
        >
          新增字典项
        </Button>
      </Box>

      {/* 类别筛选 */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography variant="body2" color="text.secondary">
          类别筛选:
        </Typography>
        <Chip
          label="全部"
          variant={selectedCategory === '' ? 'filled' : 'outlined'}
          onClick={() => handleCategoryFilter('')}
          sx={{ cursor: 'pointer' }}
        />
        {categories.map((category) => (
          <Chip
            key={category}
            label={category}
            variant={selectedCategory === category ? 'filled' : 'outlined'}
            onClick={() => handleCategoryFilter(category)}
            sx={{ cursor: 'pointer' }}
          />
        ))}
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          overflow: 'hidden'
        }}
      >
        <TableContainer>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                <TableCell>类别</TableCell>
                <TableCell>键</TableCell>
                <TableCell>值</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {dictionaries.map((dictionary) => (
                <TableRow 
                  key={dictionary.id_}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    {dictionary.category}
                  </TableCell>
                  <TableCell>{dictionary.key}</TableCell>
                  <TableCell sx={{ maxWidth: 300, wordBreak: 'break-word' }}>
                    {renderValue(dictionary.value)}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton 
                      size="small" 
                      onClick={() => handleEdit(dictionary)}
                      sx={{ mr: 1 }}
                    >
                      <Edit size={16} />
                    </IconButton>
                    <IconButton 
                      size="small"
                      onClick={() => handleDeleteConfirm(dictionary)}
                      color="error"
                    >
                      <Trash2 size={16} />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {dictionaries.length === 0 && !loading && (
                <TableRow>
                  <TableCell colSpan={4} align="center" sx={{ py: 4 }}>
                    <Typography color="text.secondary">
                      暂无数据
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>



      {/* 删除确认对话框 */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除字典项 "{dictionaryToDelete?.category}.{dictionaryToDelete?.key}" 吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setDeleteConfirmOpen(false)} color="inherit">取消</Button>
          <Button onClick={handleDelete} color="error" variant="contained">删除</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
} 