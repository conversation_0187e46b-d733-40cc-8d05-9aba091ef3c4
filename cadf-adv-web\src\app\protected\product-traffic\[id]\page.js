"use client";

import React, {useState} from 'react';
import {Box, Button, Card, CardContent, CardHeader, Divider, Grid, IconButton, Paper, Tab, Tabs, Typography,} from '@mui/material';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart} from '@mui/x-charts';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ShareIcon from '@mui/icons-material/Share';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {AlertTriangle, MessageCircle, Search, ThumbsDown, ThumbsUp} from 'lucide-react';
import {useRouter} from 'next/navigation';

export default function ProductDetailRoute({params}) {
    const [timeRange, setTimeRange] = useState('week');
    const [tabValue, setTabValue] = useState(0);
    const router = useRouter();

    // 使用 React.use() 解包 params 对象
    const unwrappedParams = React.use(params);
    // 从URL参数中获取产品ID
    const productId = unwrappedParams.id;

    // 模拟产品列表数据 - 在实际应用中，这应该从API获取或通过props传入
    const products = [
        {id: 1, name: '时尚服饰系列'},
        {id: 2, name: '创新科技产品'},
        {id: 3, name: '新品预告'},
        {id: 4, name: '促销活动'},
        {id: 5, name: '用户评价集锦'}
    ];

    // 产品数据 - 在实际应用中，这应该从API获取
    const dailyData = {
        dates: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        products: {
            1: {
                views: [500, 600, 450, 550, 700, 800, 750],
                likes: [150, 130, 120, 160, 180, 200, 190],
                shares: [70, 60, 80, 90, 100, 120, 110],
                favorites: [30, 40, 35, 50, 60, 70, 65]
            },
            2: {
                views: [300, 350, 280, 320, 380, 450, 400],
                likes: [80, 70, 75, 85, 95, 110, 100],
                shares: [40, 30, 45, 50, 55, 65, 60],
                favorites: [20, 25, 18, 30, 35, 40, 38]
            },
            3: {
                views: [200, 230, 190, 210, 240, 300, 270],
                likes: [50, 45, 55, 60, 70, 80, 75],
                shares: [25, 20, 30, 35, 40, 45, 42],
                favorites: [12, 15, 10, 18, 20, 24, 22]
            },
            4: {
                views: [120, 140, 110, 130, 150, 200, 180],
                likes: [30, 25, 35, 40, 45, 55, 50],
                shares: [15, 12, 18, 20, 22, 28, 25],
                favorites: [8, 10, 7, 12, 15, 18, 16]
            },
            5: {
                views: [80, 90, 70, 85, 95, 120, 110],
                likes: [20, 15, 25, 30, 35, 40, 37],
                shares: [10, 8, 12, 15, 18, 20, 19],
                favorites: [5, 6, 4, 8, 10, 12, 11]
            }
        }
    };

    const weeklyData = {
        dates: ['第1周', '第2周', '第3周', '第4周'],
        products: {
            1: {
                views: [3500, 4000, 4400, 4200],
                likes: [900, 1050, 1200, 1100],
                shares: [550, 600, 700, 650],
                favorites: [300, 350, 400, 380]
            },
            2: {
                views: [2000, 2300, 2600, 2400],
                likes: [550, 650, 750, 700],
                shares: [320, 380, 430, 400],
                favorites: [180, 220, 250, 230]
            },
            3: {
                views: [1300, 1500, 1700, 1600],
                likes: [360, 420, 480, 450],
                shares: [200, 240, 280, 260],
                favorites: [120, 140, 160, 150]
            },
            4: {
                views: [800, 900, 1100, 1000],
                likes: [220, 260, 300, 280],
                shares: [120, 150, 170, 160],
                favorites: [70, 85, 95, 90]
            },
            5: {
                views: [400, 500, 700, 600],
                likes: [110, 140, 170, 150],
                shares: [60, 80, 100, 90],
                favorites: [35, 45, 55, 50]
            }
        }
    };

    const monthlyData = {
        dates: ['1月', '2月', '3月'],
        products: {
            1: {
                views: [14000, 15000, 16500],
                likes: [3700, 4000, 4400],
                shares: [2200, 2400, 2600],
                favorites: [1300, 1450, 1600]
            },
            2: {
                views: [8000, 8800, 9600],
                likes: [2200, 2400, 2700],
                shares: [1300, 1450, 1600],
                favorites: [700, 800, 900]
            },
            3: {
                views: [5000, 5500, 6000],
                likes: [1350, 1500, 1650],
                shares: [750, 850, 950],
                favorites: [450, 520, 580]
            },
            4: {
                views: [3200, 3600, 4000],
                likes: [850, 950, 1050],
                shares: [480, 550, 600],
                favorites: [280, 320, 360]
            },
            5: {
                views: [1800, 2100, 2400],
                likes: [480, 550, 650],
                shares: [270, 320, 370],
                favorites: [160, 190, 220]
            }
        }
    };

    // 获取当前应显示的数据
    const getCurrentData = () => {
        switch (timeRange) {
            case 'day':
                return dailyData;
            case 'week':
                return weeklyData;
            case 'month':
                return monthlyData;
            default:
                return weeklyData;
        }
    };

    const currentData = getCurrentData();
    const product = products.find(p => p.id === parseInt(productId));
    const productData = currentData.products[productId];

    // 饼图数据 - 不同指标占比
    const pieChartData = [
        {id: 0, value: productData?.views.reduce((a, b) => a + b, 0), label: '阅读量', color: '#3f51b5'},
        {id: 1, value: productData?.likes.reduce((a, b) => a + b, 0), label: '点赞量', color: '#4caf50'},
        {id: 2, value: productData?.shares.reduce((a, b) => a + b, 0), label: '转发量', color: '#2196f3'},
        {id: 3, value: productData?.favorites.reduce((a, b) => a + b, 0), label: '收藏量', color: '#ff9800'}
    ];

    // 评论区词云数据 - 在实际应用中，这应该从API获取
    const commentCloudData = {
        1: [
            {text: '好看', value: 100},
            {text: '时尚', value: 80},
            {text: '质量好', value: 75},
            {text: '价格合理', value: 60},
            {text: '舒适', value: 55},
            {text: '设计感', value: 50},
            {text: '百搭', value: 48},
            {text: '推荐', value: 45},
            {text: '喜欢', value: 40},
            {text: '实用', value: 35},
        ],
        2: [
            {text: '创新', value: 95},
            {text: '实用', value: 85},
            {text: '科技感', value: 80},
            {text: '便捷', value: 70},
            {text: '性价比高', value: 65},
            {text: '体验好', value: 60},
            {text: '功能强大', value: 55},
            {text: '好用', value: 50},
            {text: '设计精美', value: 45},
            {text: '智能', value: 40},
        ],
        3: [
            {text: '期待', value: 90},
            {text: '新颖', value: 85},
            {text: '创意', value: 80},
            {text: '设计感', value: 70},
            {text: '有趣', value: 65},
            {text: '品质', value: 60},
            {text: '惊喜', value: 55},
            {text: '关注', value: 50},
            {text: '好奇', value: 45},
            {text: '潮流', value: 40},
        ],
        4: [
            {text: '优惠', value: 100},
            {text: '划算', value: 90},
            {text: '折扣', value: 85},
            {text: '实惠', value: 75},
            {text: '超值', value: 70},
            {text: '性价比', value: 65},
            {text: '限时', value: 60},
            {text: '福利', value: 55},
            {text: '划算', value: 50},
            {text: '抢购', value: 45},
        ],
        5: [
            {text: '真实', value: 95},
            {text: '客观', value: 90},
            {text: '有用', value: 85},
            {text: '参考', value: 75},
            {text: '详细', value: 70},
            {text: '准确', value: 65},
            {text: 'helpful', value: 60},
            {text: '良心', value: 55},
            {text: '信任', value: 50},
            {text: '推荐', value: 45},
        ]
    };

    // 舆论导向分析数据
    const sentimentData = {
        1: {positive: 75, neutral: 15, negative: 10},
        2: {positive: 70, neutral: 20, negative: 10},
        3: {positive: 80, neutral: 15, negative: 5},
        4: {positive: 65, neutral: 25, negative: 10},
        5: {positive: 60, neutral: 30, negative: 10},
    };

    // 用户意图分析数据
    const userIntentData = {
        1: [
            {id: 0, value: 45, label: '购买咨询'},
            {id: 1, value: 25, label: '分享体验'},
            {id: 2, value: 15, label: '询问优惠'},
            {id: 3, value: 10, label: '售后问题'},
            {id: 4, value: 5, label: '其他问题'}
        ],
        2: [
            {id: 0, value: 40, label: '功能咨询'},
            {id: 1, value: 30, label: '购买咨询'},
            {id: 2, value: 15, label: '分享体验'},
            {id: 3, value: 10, label: '售后问题'},
            {id: 4, value: 5, label: '其他问题'}
        ],
        3: [
            {id: 0, value: 50, label: '获取最新信息'},
            {id: 1, value: 25, label: '预约试用'},
            {id: 2, value: 15, label: '价格咨询'},
            {id: 3, value: 5, label: '分享期待'},
            {id: 4, value: 5, label: '其他问题'}
        ],
        4: [
            {id: 0, value: 60, label: '获取优惠信息'},
            {id: 1, value: 20, label: '询问活动细则'},
            {id: 2, value: 10, label: '购买咨询'},
            {id: 3, value: 5, label: '分享体验'},
            {id: 4, value: 5, label: '其他问题'}
        ],
        5: [
            {id: 0, value: 40, label: '分享体验'},
            {id: 1, value: 30, label: '产品比较'},
            {id: 2, value: 15, label: '购买咨询'},
            {id: 3, value: 10, label: '推荐给他人'},
            {id: 4, value: 5, label: '其他问题'}
        ]
    };

    // 处理标签切换
    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    // 返回产品列表
    const handleBackToList = () => {
        router.push('/protected/operation-stats');
    };

    if (!product || !productData) {
        return (
            <Box sx={{textAlign: 'center', py: 5}}>
                <Typography variant="h5" color="error" gutterBottom>
                    未找到产品数据
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<ArrowBackIcon/>}
                    onClick={handleBackToList}
                    sx={{mt: 2}}
                >
                    返回产品列表
                </Button>
            </Box>
        );
    }

    return (
        <Box sx={{pt: 2}}>
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                <Typography variant="h4" gutterBottom sx={{fontWeight: 'medium', mb: 0}}>
                    {product.name} - 运营数据
                </Typography>

                <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon/>}
                    onClick={handleBackToList}
                    size="small"
                >
                    返回产品列表
                </Button>
            </Box>

            {/* 总览卡片 */}
            <Grid container spacing={3} sx={{mb: 4}}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{bgcolor: 'primary.50', boxShadow: 2, borderRadius: 2}}>
                        <CardContent sx={{p: 2.5, pb: '16px !important'}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <VisibilityIcon sx={{color: 'primary.main', mr: 1.5, fontSize: 24}}/>
                                <Typography variant="subtitle1" color="text.secondary">
                                    总阅读量
                                </Typography>
                            </Box>
                            <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                {productData.views.reduce((a, b) => a + b, 0).toLocaleString()}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{bgcolor: 'success.50', boxShadow: 2, borderRadius: 2}}>
                        <CardContent sx={{p: 2.5, pb: '16px !important'}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <ThumbUpIcon sx={{color: 'success.main', mr: 1.5, fontSize: 24}}/>
                                <Typography variant="subtitle1" color="text.secondary">
                                    总点赞量
                                </Typography>
                            </Box>
                            <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                {productData.likes.reduce((a, b) => a + b, 0).toLocaleString()}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{bgcolor: 'info.50', boxShadow: 2, borderRadius: 2}}>
                        <CardContent sx={{p: 2.5, pb: '16px !important'}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <ShareIcon sx={{color: 'info.main', mr: 1.5, fontSize: 24}}/>
                                <Typography variant="subtitle1" color="text.secondary">
                                    总转发量
                                </Typography>
                            </Box>
                            <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                {productData.shares.reduce((a, b) => a + b, 0).toLocaleString()}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{bgcolor: 'warning.50', boxShadow: 2, borderRadius: 2}}>
                        <CardContent sx={{p: 2.5, pb: '16px !important'}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <BookmarkIcon sx={{color: 'warning.main', mr: 1.5, fontSize: 24}}/>
                                <Typography variant="subtitle1" color="text.secondary">
                                    总收藏量
                                </Typography>
                            </Box>
                            <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                {productData.favorites.reduce((a, b) => a + b, 0).toLocaleString()}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* 图表切换选项卡 */}
            <Paper sx={{mb: 3}}>
                <Tabs
                    value={tabValue}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    sx={{borderBottom: 1, borderColor: 'divider'}}
                >
                    <Tab label="数据趋势"/>
                    <Tab label="评论词云"/>
                    <Tab label="舆论导向"/>
                    <Tab label="用户意图"/>
                </Tabs>
            </Paper>

            {/* 图表内容 */}
            {tabValue === 0 && (
                <Card sx={{boxShadow: 3, borderRadius: 2}}>
                    <CardHeader
                        title={`${product.name} - 数据趋势`}
                        action={
                            <IconButton aria-label="设置">
                                <MoreVertIcon/>
                            </IconButton>
                        }
                    />
                    <Divider/>
                    <CardContent>
                        <Box sx={{height: 400, width: '100%', position: 'relative'}}>
                            <LineChart
                                series={[
                                    {
                                        data: productData.views,
                                        label: '阅读量',
                                        color: '#3f51b5',
                                        curve: "natural",
                                        showMark: true,
                                        area: true,
                                        stack: 'total'
                                    },
                                    {
                                        data: productData.likes,
                                        label: '点赞量',
                                        color: '#4caf50',
                                        curve: "natural",
                                        showMark: true,
                                        area: true,
                                        stack: 'total'
                                    },
                                    {
                                        data: productData.shares,
                                        label: '转发量',
                                        color: '#2196f3',
                                        curve: "natural",
                                        showMark: true,
                                        area: true,
                                        stack: 'total'
                                    },
                                    {
                                        data: productData.favorites,
                                        label: '收藏量',
                                        color: '#ff9800',
                                        curve: "natural",
                                        showMark: true,
                                        area: true,
                                        stack: 'total'
                                    }
                                ]}
                                xAxis={[{
                                    data: currentData.dates,
                                    scaleType: 'band'
                                }]}
                                sx={{width: '100%', height: 400}}
                                slotProps={{
                                    legend: {
                                        position: {
                                            vertical: 'top',
                                            horizontal: 'right'
                                        }
                                    }
                                }}
                            />
                        </Box>
                    </CardContent>
                </Card>
            )}

            {/* 评论区词云 */}
            {tabValue === 1 && (
                <Card sx={{boxShadow: 3, borderRadius: 2}}>
                    <CardHeader
                        title={`${product.name} - 评论区词云`}
                        action={
                            <IconButton aria-label="设置">
                                <MoreVertIcon/>
                            </IconButton>
                        }
                    />
                    <Divider/>
                    <CardContent>
                        <Box sx={{width: '100%', height: 400, bgcolor: '#f5f7fa', borderRadius: 2, p: 2}}>
                            <Box sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%'
                            }}>
                                {commentCloudData[productId].map((word, index) => (
                                    <Typography
                                        key={index}
                                        variant="h5"
                                        component="span"
                                        sx={{
                                            m: 1,
                                            p: 1,
                                            fontSize: 14 + (word.value / 8),
                                            fontWeight: word.value > 70 ? 'bold' : 'normal',
                                            color: word.value > 80 ? '#3f51b5' :
                                                word.value > 60 ? '#4caf50' :
                                                    word.value > 50 ? '#2196f3' :
                                                        word.value > 40 ? '#ff9800' : '#9e9e9e',
                                            textShadow: word.value > 70 ? '1px 1px 2px rgba(0,0,0,0.1)' : 'none',
                                            transition: 'all 0.3s ease',
                                            '&:hover': {
                                                transform: 'scale(1.1)',
                                            }
                                        }}
                                    >
                                        {word.text}
                                    </Typography>
                                ))}
                            </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{mt: 2, textAlign: 'center'}}>
                            注: 词语大小代表在评论中出现的频率，颜色深浅代表情感倾向
                        </Typography>
                    </CardContent>
                </Card>
            )}

            {/* 舆论导向分析 */}
            {tabValue === 2 && (
                <Card>
                    <CardHeader
                        title={`${product.name} - 舆论导向分析`}
                        action={
                            <IconButton aria-label="设置">
                                <MoreVertIcon/>
                            </IconButton>
                        }
                    />
                    <Divider/>
                    <CardContent>
                        <Box sx={{height: 400, width: '100%'}}>
                            <Grid container spacing={3}>
                                <Grid item xs={12} md={6}>
                                    <Typography variant="h6" sx={{mb: 2, textAlign: 'center'}}>
                                        情感分布
                                    </Typography>
                                    <Box sx={{height: 350, display: 'flex', justifyContent: 'center'}}>
                                        <PieChart
                                            series={[
                                                {
                                                    data: [
                                                        {id: 0, value: sentimentData[productId].positive, label: '正面', color: '#4caf50'},
                                                        {id: 1, value: sentimentData[productId].neutral, label: '中性', color: '#2196f3'},
                                                        {id: 2, value: sentimentData[productId].negative, label: '负面', color: '#f44336'}
                                                    ],
                                                    innerRadius: 60,
                                                    outerRadius: 140,
                                                    paddingAngle: 2,
                                                    cornerRadius: 8,
                                                    highlightScope: {faded: 'global', highlighted: 'item'},
                                                    faded: {innerRadius: 55, outerRadius: 135, opacity: 0.3},
                                                }
                                            ]}
                                            width={350}
                                            height={350}
                                            slotProps={{
                                                legend: {
                                                    position: {
                                                        vertical: 'middle',
                                                        horizontal: 'right'
                                                    }
                                                }
                                            }}
                                        />
                                    </Box>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <Typography variant="h6" sx={{mb: 2, textAlign: 'center'}}>
                                        舆情监测指标
                                    </Typography>
                                    <Box sx={{display: 'flex', flexDirection: 'column', height: 350, justifyContent: 'space-around'}}>
                                        <Card sx={{bgcolor: 'success.50', mb: 2}}>
                                            <CardContent sx={{p: 2, pb: '16px !important'}}>
                                                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                                    <ThumbsUp size={20} color="#2e7d32" style={{marginRight: 8}}/>
                                                    <Typography variant="subtitle1" color="text.secondary">
                                                        正面评价比例
                                                    </Typography>
                                                </Box>
                                                <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                                    {sentimentData[productId].positive}%
                                                </Typography>
                                            </CardContent>
                                        </Card>

                                        <Card sx={{bgcolor: 'info.50', mb: 2}}>
                                            <CardContent sx={{p: 2, pb: '16px !important'}}>
                                                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                                    <MessageCircle size={20} color="#0288d1" style={{marginRight: 8}}/>
                                                    <Typography variant="subtitle1" color="text.secondary">
                                                        中性评价比例
                                                    </Typography>
                                                </Box>
                                                <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                                    {sentimentData[productId].neutral}%
                                                </Typography>
                                            </CardContent>
                                        </Card>

                                        <Card sx={{bgcolor: 'error.50'}}>
                                            <CardContent sx={{p: 2, pb: '16px !important'}}>
                                                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                                    <ThumbsDown size={20} color="#d32f2f" style={{marginRight: 8}}/>
                                                    <Typography variant="subtitle1" color="text.secondary">
                                                        负面评价比例
                                                    </Typography>
                                                </Box>
                                                <Typography variant="h4" sx={{mb: 0, fontWeight: 'medium'}}>
                                                    {sentimentData[productId].negative}%
                                                </Typography>
                                            </CardContent>
                                        </Card>
                                    </Box>
                                </Grid>
                            </Grid>
                        </Box>
                    </CardContent>
                </Card>
            )}

            {/* 用户意图分析 */}
            {tabValue === 3 && (
                <Card>
                    <CardHeader
                        title={`${product.name} - 用户意图分析`}
                        action={
                            <IconButton aria-label="设置">
                                <MoreVertIcon/>
                            </IconButton>
                        }
                    />
                    <Divider/>
                    <CardContent>
                        <Box sx={{height: 400, width: '100%'}}>
                            <Grid container spacing={3}>
                                <Grid item xs={12} md={7}>
                                    <Typography variant="h6" sx={{mb: 2, textAlign: 'center'}}>
                                        用户评论意图分布
                                    </Typography>
                                    <Box sx={{height: 350}}>
                                        <BarChart
                                            series={[{
                                                data: userIntentData[productId].map(item => item.value),
                                                label: '百分比',
                                                color: '#3f51b5',
                                                highlightScope: {faded: 'global', highlighted: 'item'},
                                                barRadius: 4,
                                            }]}
                                            xAxis={[{
                                                data: userIntentData[productId].map(item => item.label),
                                                scaleType: 'band',
                                            }]}
                                            yAxis={[{
                                                min: 0,
                                                max: 100,
                                            }]}
                                            width={550}
                                            height={350}
                                            colors={['#3f51b5', '#4caf50', '#2196f3', '#ff9800', '#f44336']}
                                        />
                                    </Box>
                                </Grid>
                                <Grid item xs={12} md={5}>
                                    <Typography variant="h6" sx={{mb: 2, textAlign: 'center'}}>
                                        关键用户意图分析
                                    </Typography>
                                    <Box sx={{height: 350, overflow: 'auto'}}>
                                        <Box sx={{p: 2, bgcolor: 'background.paper', borderRadius: 1, mb: 2, boxShadow: 1}}>
                                            <Typography variant="subtitle1" sx={{fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center'}}>
                                                <Search size={18} style={{marginRight: 8}}/>
                                                主要用户关注点
                                            </Typography>
                                            <Typography variant="body2" sx={{mb: 1}}>
                                                基于评论分析，用户最关注的是{userIntentData[productId][0].label}，占比{userIntentData[productId][0].value}%
                                            </Typography>
                                        </Box>

                                        <Box sx={{p: 2, bgcolor: 'background.paper', borderRadius: 1, mb: 2, boxShadow: 1}}>
                                            <Typography variant="subtitle1" sx={{fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center'}}>
                                                <AlertTriangle size={18} style={{marginRight: 8}}/>
                                                潜在问题点
                                            </Typography>
                                            <Typography variant="body2" sx={{mb: 1}}>
                                                {userIntentData[productId].find(item => item.label === '售后问题' || item.label === '其他问题')
                                                    ? `${userIntentData[productId].find(item => item.label === '售后问题' || item.label === '其他问题').label}占比${userIntentData[productId].find(item => item.label === '售后问题' || item.label === '其他问题').value}%，需关注原因`
                                                    : '暂无明显潜在问题点'}
                                            </Typography>
                                        </Box>

                                        <Box sx={{p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1}}>
                                            <Typography variant="subtitle1" sx={{fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center'}}>
                                                <MessageCircle size={18} style={{marginRight: 8}}/>
                                                转化机会
                                            </Typography>
                                            <Typography variant="body2" sx={{mb: 1}}>
                                                {userIntentData[productId].find(item => item.label === '购买咨询' || item.label === '价格咨询')
                                                    ? `${userIntentData[productId].find(item => item.label === '购买咨询' || item.label === '价格咨询').label}占比${userIntentData[productId].find(item => item.label === '购买咨询' || item.label === '价格咨询').value}%，存在转化机会`
                                                    : '暂无明显转化机会点'}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Grid>
                            </Grid>
                        </Box>
                    </CardContent>
                </Card>
            )}
        </Box>
    );
} 