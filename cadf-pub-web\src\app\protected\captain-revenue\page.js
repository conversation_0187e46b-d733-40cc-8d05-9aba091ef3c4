"use client";

import {
    <PERSON><PERSON>,
    Box,
    Card,
    CardContent,
    CircularProgress,
    Grid,
    Pagination,
    Paper,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    useMediaQuery,
    useTheme,
} from '@mui/material';
import {AlertCircle, DollarSign} from 'lucide-react';
import {useEffect, useState} from 'react';
import {useDispatch} from "react-redux";
import {captainDailyRevenueApi} from "@/api/captain-daily-revenue-api";
import {addAlert} from "@/core/components/redux/alert-slice";
import {AlertType} from "@/core/components/alert";

// 日期格式化函数 (将Unix时间戳转换为 YYYY-MM-DD)
const formatDate = (timestamp) => {
    if (!timestamp) return '-';
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
};

export default function CaptainRevenue() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const dispatch = useDispatch();
    const [page, setPage] = useState(1);
    const rowsPerPage = 10; // 增加每页显示数量
    const [crewRevenueData, setCrewRevenueData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);
            try {
                const response = await captainDailyRevenueApi.queryAll();
                if (response && Array.isArray(response)) {
                    setCrewRevenueData(response);
                } else {
                    console.error("Unexpected response format:", response);
                    setCrewRevenueData([]); // 设置为空数组以避免后续计算错误
                    setError("获取数据格式不正确");
                    dispatch(addAlert({type: AlertType.ERROR, message: "获取舰员收益数据格式不正确"}));
                }
            } catch (err) {
                console.error("获取舰员收益失败:", err);
                setError(err.message || "获取舰员收益失败");
                dispatch(addAlert({type: AlertType.ERROR, message: err.message || "获取舰员收益失败"}));
                setCrewRevenueData([]); // 发生错误时清空数据
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [dispatch]);

    // 处理页码变化
    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    // 获取当前页显示的数据
    const currentPageData = crewRevenueData.slice(
        (page - 1) * rowsPerPage,
        page * rowsPerPage
    );

    // 计算总收益、可提现收益和当前收益
    const totalCaptainRevenue = crewRevenueData.reduce((sum, item) => sum + (item.daily_revenue || 0), 0);
    const withdrawableRevenue = crewRevenueData
        .filter(item => item.status === "已结算")
        .reduce((sum, item) => sum + (item.daily_revenue || 0), 0);
    const pendingRevenue = crewRevenueData
        .filter(item => item.status === "未结算" || item.status === "已结算") // '当前收益'包含未结算和已结算
        .reduce((sum, item) => sum + (item.daily_revenue || 0), 0);
    // const totalCrewMembers = new Set(crewRevenueData.map(item => item.crew_user_id)).size;

    return (
        <Box sx={{pt: 2, px: {xs: 1, sm: 2}}}>
            <Typography variant="h4" gutterBottom sx={{
                fontWeight: 'medium',
                mb: 3,
                fontSize: {xs: '1.5rem', sm: '2rem', md: '2.25rem'}
            }}>
                舰长收益
            </Typography>

            <Typography variant="body2" sx={{mb: 3, color: theme.palette.text.secondary}}>
                舰长收益包含您的舰队成员产生收益的30%提成，展示您的舰队成员贡献情况
            </Typography>

            {/* 收益概览卡片 */}
            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                收益概览
            </Typography>

            <Grid container spacing={2} sx={{mb: 4}}>
                {/* 历史总收益 */}
                <Grid item xs={12} sm={6} md={4}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.primary.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    历史总收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.primary.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{(totalCaptainRevenue / 100).toFixed(2)} {/* 分转换为元 */}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                历史所有舰长提成总收益
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                {/* 当前收益 */}
                <Grid item xs={12} sm={6} md={4}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.warning.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    当前收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.warning.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{(pendingRevenue / 100).toFixed(2)} {/* 分转换为元 */}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                未结算和已结算的收益总和
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                {/* 可提现收益 */}
                <Grid item xs={12} sm={6} md={4}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.success.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    可提现收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.success.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{(withdrawableRevenue / 100).toFixed(2)} {/* 分转换为元 */}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                本周期内已经结算且可以提现的收益
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* 收益规则说明 */}
            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                舰长收益规则
            </Typography>

            <Alert
                severity="info"
                icon={<AlertCircle size={isMobile ? 20 : 24}/>}
                sx={{
                    mb: 4,
                    '& .MuiAlert-message': {width: '100%'},
                    px: {xs: 1, sm: 2}
                }}
            >
                <Typography variant="body2" sx={{color: 'primary.main', fontWeight: 'medium'}}>
                    • 作为舰长，您将获得舰队成员收益的30%作为提成
                </Typography>
                <Typography variant="body2">
                    • 提成收益将随着舰员收益一起结算
                </Typography>
                <Typography variant="body2">
                    • 收益结算日期为每周四
                </Typography>
                <Typography variant="body2">
                    • 舰长可以在"舰员管理"页面添加和管理舰队成员
                </Typography>
            </Alert>

            {/* 舰员收益明细 */}
            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                舰员收益明细
            </Typography>

            {loading ? (
                <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200}}>
                    <CircularProgress/>
                </Box>
            ) : error ? (
                <Alert severity="error" sx={{mb: 4}}>错误: {error}</Alert>
            ) : crewRevenueData.length === 0 ? (
                <Typography sx={{textAlign: 'center', color: theme.palette.text.secondary, my: 4}}>
                    暂无舰员收益数据
                </Typography>
            ) : isMobile ? (
                // 移动端卡片布局
                <Box>
                    {currentPageData.map((row) => (
                        <Card key={row.id_} sx={{mb: 2}}> {/* 使用 id_ 作为 key */}
                            <CardContent sx={{p: 2}}>
                                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                                    <Typography variant="subtitle1" sx={{fontWeight: 'medium'}}>
                                        舰员: {row.crew_user_id} {/* 显示舰员ID */}
                                    </Typography>
                                    <Box
                                        component="span"
                                        sx={{
                                            px: 1.5,
                                            py: 0.5,
                                            borderRadius: 1,
                                            fontSize: '0.75rem',
                                            backgroundColor: row.status === "已结算"
                                                ? theme.palette.success.light // 使用浅色背景
                                                : row.status === "已提现"
                                                    ? theme.palette.info.light
                                                    : theme.palette.warning.light,
                                            color: row.status === "已结算"
                                                ? theme.palette.success.dark // 使用深色文字
                                                : row.status === "已提现"
                                                    ? theme.palette.info.dark
                                                    : theme.palette.warning.dark,
                                        }}
                                    >
                                        {row.status}
                                    </Box>
                                </Box>

                                <Grid container spacing={1} sx={{mb: 1}}>
                                    <Grid item xs={6}>
                                        <Typography variant="body2" color="textSecondary">舰长提成:</Typography>
                                        <Typography variant="body1" sx={{color: theme.palette.primary.main, fontWeight: 'medium'}}>
                                            {(row.daily_revenue / 100).toFixed(2)} 元 {/* 分转换为元 */}
                                        </Typography>
                                    </Grid>
                                     <Grid item xs={6}>
                                        <Typography variant="body2" color="textSecondary">出账日期:</Typography>
                                        <Typography variant="body1">{formatDate(row.date)}</Typography> {/* 格式化日期 */}
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>
                    ))}

                    {/* 移动端分页控件 */}
                    {crewRevenueData.length > rowsPerPage && (
                        <Stack spacing={2} sx={{mt: 3, mb: 4, alignItems: 'center'}}>
                            <Pagination
                                count={Math.ceil(crewRevenueData.length / rowsPerPage)}
                                page={page}
                                onChange={handlePageChange}
                                color="primary"
                                size={isMobile ? "small" : "medium"}
                            />
                        </Stack>
                    )}
                </Box>
            ) : (
                // 桌面端表格布局
                <Box>
                    <TableContainer component={Paper} sx={{mb: 2}}>
                        <Table>
                            <TableHead>
                                <TableRow sx={{backgroundColor: theme.palette.action.hover}}>
                                    <TableCell sx={{ px: 2 }}>舰员账号</TableCell>
                                    {/*<TableCell sx={{ px: 2 }}>舰员收益 (元)</TableCell> */}{/* 暂时移除舰员收益 */}
                                    <TableCell sx={{ px: 2 }}>舰长提成 (元)</TableCell>
                                    <TableCell sx={{ px: 2 }}>出账日期</TableCell>
                                    <TableCell sx={{ px: 2 }}>状态</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {currentPageData.map((row) => (
                                    <TableRow key={row.id_}> {/* 使用 id_ 作为 key */}
                                        <TableCell sx={{ px: 2 }}>{row.crew_user_id}</TableCell> {/* 显示舰员ID */}
                                        {/*<TableCell sx={{ px: 2 }}>{(row.originalRevenue / 100).toFixed(2)}</TableCell>*/}
                                        <TableCell sx={{color: theme.palette.primary.main, fontWeight: 'medium', px: 2}}>
                                            {(row.daily_revenue / 100).toFixed(2)} {/* 分转换为元 */}
                                        </TableCell>
                                        <TableCell sx={{ px: 2 }}>{formatDate(row.date)}</TableCell> {/* 格式化日期 */}
                                        <TableCell sx={{ px: 2 }}>
                                            <Box
                                                component="span"
                                                sx={{
                                                    px: 1.5,
                                                    py: 0.5,
                                                    borderRadius: 1,
                                                    fontSize: '0.8rem',
                                                    fontWeight: 'medium',
                                                    backgroundColor: row.status === "已结算"
                                                        ? theme.palette.success.light // 使用浅色背景
                                                        : row.status === "已提现"
                                                            ? theme.palette.info.light
                                                            : theme.palette.warning.light,
                                                    color: row.status === "已结算"
                                                        ? theme.palette.success.dark // 使用深色文字
                                                        : row.status === "已提现"
                                                            ? theme.palette.info.dark
                                                            : theme.palette.warning.dark,
                                                }}
                                            >
                                                {row.status}
                                            </Box>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>

                    {/* 桌面端分页控件 */}
                    {crewRevenueData.length > rowsPerPage && (
                         <Stack spacing={2} sx={{mt: 3, mb: 4, alignItems: 'center'}}>
                            <Pagination
                                count={Math.ceil(crewRevenueData.length / rowsPerPage)}
                                page={page}
                                onChange={handlePageChange}
                                color="primary"
                            />
                        </Stack>
                    )}
                </Box>
            )}
        </Box>
    );
} 