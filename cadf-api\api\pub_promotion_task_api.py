import time
from datetime import datetime, timedelta

from mongoengine import Q, DoesNotExist
from mongoengine.errors import ValidationError

from config.config import XHS_TASK_URL_VERIFY_SET
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.log.log import olog
from omni.mongo.mongo_client import doc_to_dict
from omni.msg_queue.redis_set_publisher import publish_to_redis_set
from repository.models import (
    AiGeneratedMaterial,
    PromotionTaskDetail,
    Product,
    PromotionTask,
    Account
)
from utils.platform_judgment_tool import extract_url_from_text

DAILY_TASK_LIMIT = 2
MAX_UNCOMPLETED_TASKS = 2


@register_handler('pub_promotion_task')
class PubPromotionTaskApi:

    @auth_required(['creator', 'admin'])
    def query_accounts_with_task_status(self, data: dict):
        """
        查询指定用户下的账户列表，并附加每个账户的任务接取状态（今日已接、未完成）和限制信息，方便前端展示账户的当前负荷及上限。
        """
        user_id = data.get('user_id')

        if not user_id:
            raise MException("无法获取用户信息")

        try:
            query_filters = Q(user_id=str(user_id))
            accounts = Account.objects(query_filters).order_by('-create_at')
            total_count = accounts.count()

            if not accounts:
                return {'accounts': [], 'total_count': 0}

            accounts_with_status = []
            for account in accounts:
                account_dict = doc_to_dict(account)
                account_id_str = account_dict['id_']

                # Inlined _get_account_task_counts
                # Calculate start and end of the current local day (system timezone) as UTC timestamps
                now_local_for_counts = datetime.now()
                start_of_day_local_for_counts = datetime.combine(now_local_for_counts.date(), datetime.min.time())
                end_of_day_local_for_counts = start_of_day_local_for_counts + timedelta(days=1)
                start_ts_for_counts = int(start_of_day_local_for_counts.timestamp())
                end_ts_for_counts = int(end_of_day_local_for_counts.timestamp())

                accepted_today = PromotionTaskDetail.objects(
                    account_id=account_id_str,
                    accepted_at__gte=start_ts_for_counts,
                    accepted_at__lt=end_ts_for_counts
                ).count()

                uncompleted_count = PromotionTaskDetail.objects(
                    account_id=account_id_str,
                    validation_status__ne='成功'
                ).count()
                # End Inlined _get_account_task_counts

                account_dict['current_usage_level'] = accepted_today
                account_dict['limit'] = DAILY_TASK_LIMIT
                account_dict['uncompleted_tasks_count'] = uncompleted_count
                account_dict['max_uncompleted_limit'] = MAX_UNCOMPLETED_TASKS

                accounts_with_status.append(account_dict)

            return {
                'accounts': accounts_with_status,
                'total_count': total_count
            }

        except Exception as e:
            olog.error(f"查询账户及任务状态时发生错误: {e}", exc_info=True)
            raise MException(f"查询账户列表及状态失败: {e}")

    @auth_required(['creator', 'admin'])
    def get_account_info(self, data: dict):
        """
        获取单个账户的详细信息 (名称、领域、平台、状态等)。
        """
        account_id = data.get('account_id')
        user_id = data.get('user_id')  # Ensure user owns the account

        if not account_id:
            raise MException("缺少必要的参数 'account_id'")
        if not user_id:
            # This should ideally not happen due to auth_required, but as a safeguard:
            raise MException("无法验证用户身份以获取账户信息")

        try:
            # Fetch account ensuring it belongs to the requesting user
            account = Account.objects.get(id=account_id, user_id=str(user_id))
        except DoesNotExist:
            olog.warning(f"用户 {user_id} 尝试获取不存在或不属于他的账户信息: ID {account_id}")
            raise MException("账户不存在或您无权访问")
        except ValidationError:
            raise MException("提供的账户ID格式无效")

        # Include task counts for consistency with query_accounts_with_task_status if needed by frontend
        # For now, just returning basic account details + status.
        account_dict = doc_to_dict(account)

        # Optionally, add task counts like in query_accounts_with_task_status if the consuming page needs them:
        # task_counts = _get_account_task_counts(str(account.id))
        # account_dict['current_usage_level'] = task_counts.get('accepted_today', 0)
        # account_dict['limit'] = DAILY_TASK_LIMIT
        # account_dict['uncompleted_tasks_count'] = task_counts.get('uncompleted', 0)
        # account_dict['max_uncompleted_limit'] = MAX_UNCOMPLETED_TASKS

        return {'account_info': account_dict}

    @auth_required(['creator', 'admin'])
    def query_market_tasks(self, data: dict):
        """
        # 目的
        为指定账户查询任务市场中符合条件的推广任务列表。任务列表会根据账户的偏好平台和领域进行筛选和排序，并返回分页结果。

        # 算法描述
        1.  **参数校验与初始化**:
            *   校验 `user_id` (用户ID) 和 `account_id` (账户ID) 是否提供。
            *   获取 `page` (页码, 0-indexed) 和 `limit` (每页数量) 参数。

        2.  **账户有效性验证**:
            *   根据 `account_id` 和 `user_id` 获取账户信息。
            *   检查账户状态是否为"在线"，若账户不存在或状态非"在线"则抛出异常。
            *   获取账户的 `preferred_domain` (偏好领域) 和 `preferred_platform` (偏好平台)。若偏好平台未设置，则抛出异常。

        3.  **MongoDB聚合管道查询 (获取所有候选任务)**:
            *   **初步筛选**: 选择 `user_id` 为 `None` (即未被分配) 的 `PromotionTaskDetail` (推广任务详情) 文档。
            *   **关联查询**:
                *   通过 `ai_generated_material_id` 关联查询 `AiGeneratedMaterial` (AI生成素材) 集合，排除已标记为删除的素材。
                *   通过 `promotion_task_id` 关联查询 `PromotionTask` (推广任务) 集合，排除已标记为删除的推广任务，并强制匹配账户的 `preferred_platform`。
                *   通过 `PromotionTask` 中的 `product_id` 关联查询 `Product` (产品) 集合 (左外连接)。
            *   **计算字段添加**:
                *   添加表示素材、推广任务、产品是否已删除的布尔标记字段 (主要用于内部逻辑判断，不直接返回给前端)。
                *   提取任务的实际平台 (`current_task_platform`)。
                *   提取任务的实际领域 (`current_task_domain`)：优先从关联的、未删除的产品信息中获取；若无，则为"未知领域"。
            *   **动态标题与内容生成**:
                *   `final_title` (最终标题): 直接使用AI生成素材的标题 (`material_docs.title`)。
                *   `final_content` (最终内容): 直接使用AI生成素材的内容 (`material_docs.content`)。
            *   **过滤与排序标记**:
                *   `matchesDomainFilter`: 标记任务领域是否与账户的 `preferred_domain` (偏好领域) 匹配。
                *   `isPrioritized`: 基于 `matchesDomainFilter` 设置，用于后续排序。
            *   **任务去重与代表选择**:
                *   按 `promotion_task_id` 分组，确保每个推广任务只返回一个代表性的任务详情记录。
            *   **结果排序**:
                *   按 `isPrioritized` 降序 (领域匹配的优先)，然后按推广任务创建时间 `promotion_task_docs.create_at` 降序 (新任务优先)。
            *   **注意**: 此阶段不进行分页 (不使用 $skip, $limit)。

        4.  **处理聚合结果 (格式化所有候选任务)**:
            *   遍历聚合管道返回的每个任务数据。
            *   **图片URL签名**: 为素材中的图片生成临时的可访问OSS签名URL。
            *   **时间戳格式化**: 将任务截止日期的时间戳转换为 "YYYY-MM-DD HH:MM:SS" 格式的字符串。
            *   **构建格式化任务字典 (裁剪字段)**: 组装仅包含前端 `MarketTaskCard` 所需字段的字典: `id_`, `title`, `content`, `imageUrls`, `domain`, `platform`, `deadline`, `isPrioritized`, `matchesDomainFilter`。

        5.  **任务筛选与选择逻辑 (Python端)**:
            *   若无聚合结果，则返回空列表和总数0。
            *   根据账户的 `preferred_domain`，从所有格式化后的平台匹配任务中筛选出领域也匹配的任务 (`domain_matched_tasks`)。
            *   确定最终用于展示的任务池 (`tasks_to_display_pool`):
                *   如果账户设置了偏好领域且存在领域匹配的任务，则任务池为 `domain_matched_tasks`。
                *   否则 (无偏好领域，或有偏好但无匹配项)，任务池为所有格式化后的平台匹配任务 (已按优先级排序)。
            *   计算 `total_count = len(tasks_to_display_pool)`。

        6.  **分页处理 (Python端)**:
            *   根据 `page` 和 `limit` 参数，从 `tasks_to_display_pool` 中切片出当前页的任务。

        7.  **返回结果**:
            *   返回包含 `market_tasks` (当前页的任务列表) 和 `total_count` (应用领域偏好后的任务总数) 的字典。

        8.  **异常处理**:
            *   捕获数据校验、自定义业务异常及其他通用异常，记录日志并向上抛出包含用户友好信息的 `MException`。
        """
        user_id = data.get('user_id')
        account_id = data.get('account_id')

        try:
            page = int(data.get('page', 0))
            limit = int(data.get('limit', 5))  # 前端默认 itemsPerPage 是 5
            if page < 0:
                page = 0
            if limit <= 0:
                limit = 5  # 保证 limit 是正数
        except (ValueError, TypeError):
            olog.warning(f"无效的分页参数: page={data.get('page')}, limit={data.get('limit')}. 使用默认值 page=0, limit=5.")
            page = 0
            limit = 5

        if not user_id:
            raise MException("无法获取用户信息，请重新登录")
        if not account_id:
            raise MException("必需提供 'account_id' 参数以查询任务市场")

        try:
            # Inlined _get_valid_account
            try:
                account = Account.objects.get(id=account_id, user_id=str(user_id))
                if account.status != '在线':
                    raise MException(f"账户 '{account.name}' 当前状态为 {account.status}，无法接取任务")
                # account is valid here
            except DoesNotExist:
                raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
            # End Inlined _get_valid_account

            preferred_domain = account.domain if account.domain else None
            preferred_platform = account.platform

            if not preferred_platform:
                olog.error(f"Account {account_id} does not have a preferred platform set.")
                raise MException("账户未设置推广平台，无法查询适用任务。")

            pipeline = [
                {'$match': {'user_id': None}},
                {
                    '$addFields': {
                        'ai_generated_material_oid': {'$toObjectId': '$ai_generated_material_id'},
                        'promotion_task_oid': {'$toObjectId': '$promotion_task_id'}
                    }
                },
                {
                    '$lookup': {
                        'from': AiGeneratedMaterial._get_collection_name(),
                        'localField': 'ai_generated_material_oid',
                        'foreignField': '_id',
                        'as': 'material_docs'
                    }
                },
                {'$unwind': {'path': '$material_docs', 'preserveNullAndEmptyArrays': False}},
                {'$match': {'material_docs.is_deleted': False}},
                {
                    '$lookup': {
                        'from': PromotionTask._get_collection_name(),
                        'localField': 'promotion_task_oid',
                        'foreignField': '_id',
                        'as': 'promotion_task_docs'
                    }
                },
                {'$unwind': {'path': '$promotion_task_docs', 'preserveNullAndEmptyArrays': False}},
                {'$match': {'promotion_task_docs.is_deleted': False}},
                {'$match': {'promotion_task_docs.platform': preferred_platform}},
                {
                    '$addFields': {
                        'product_oid': {
                            '$cond': {
                                'if': {'$and': [
                                    {'$ne': ['$promotion_task_docs.product_id', None]},
                                    {'$ne': ['$promotion_task_docs.product_id', ""]}
                                ]},
                                'then': {'$toObjectId': '$promotion_task_docs.product_id'},
                                'else': None
                            }
                        }
                    }
                },
                {
                    '$lookup': {
                        'from': Product._get_collection_name(),
                        'localField': 'product_oid',
                        'foreignField': '_id',
                        'as': 'product_docs'
                    }
                },
                {'$unwind': {'path': '$product_docs', 'preserveNullAndEmptyArrays': True}},
                {
                    '$addFields': {
                        'current_task_platform': '$promotion_task_docs.platform',
                        'current_task_domain': {
                            '$cond': {
                                'if': {
                                    '$and': [
                                        '$product_docs',
                                        {'$eq': ['$product_docs.is_deleted', False]},
                                        {'$isArray': '$product_docs.domain'},
                                        {'$gt': [{'$size': '$product_docs.domain'}, 0]}
                                    ]
                                },
                                'then': {'$arrayElemAt': ['$product_docs.domain', 0]},
                                'else': '未知领域'
                            }
                        }
                    }
                },
                {
                    '$addFields': {
                        'final_content': '$material_docs.content',
                        'final_title': '$material_docs.title'
                    }
                },
                {
                    '$addFields': {
                        'matchesDomainFilter': {
                            '$cond': {
                                'if': {'$and': [preferred_domain is not None, {'$ne': [preferred_domain, ""]}, {'$eq': ['$current_task_domain', preferred_domain]}]},
                                'then': True,
                                'else': False
                            }
                        }
                    }
                },
                {
                    '$addFields': {
                        'isPrioritized': '$matchesDomainFilter'
                    }
                },
                {
                    '$sort': {'promotion_task_oid': 1, '_id': 1}
                },
                {
                    '$group': {
                        '_id': '$promotion_task_oid',
                        'representative_detail_doc': {'$first': '$$ROOT'}
                    }
                },
                {'$replaceRoot': {'newRoot': '$representative_detail_doc'}},
                {'$sort': {'isPrioritized': -1, 'promotion_task_docs.create_at': -1}}
            ]

            pipeline_results_all_candidates = list(PromotionTaskDetail.objects.aggregate(*pipeline))

            all_formatted_tasks_candidates = []
            oss_client = OSSClient.getInstance()

            for task_data in pipeline_results_all_candidates:
                signed_image_urls = []
                material_images_list = task_data.get('material_docs', {}).get('images', [])

                if material_images_list:  # material_docs.is_deleted is False due to pipeline
                    sorted_material_images = sorted(
                        material_images_list,
                        key=lambda img_info_lambda: img_info_lambda.get('order') if isinstance(img_info_lambda, dict) and 'order' in img_info_lambda else float('inf')
                    )
                    for img_info in sorted_material_images:
                        oss_key = img_info.get('oss_key') if isinstance(img_info, dict) else None
                        if oss_key:
                            try:
                                signed_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                                signed_image_urls.append(signed_url)
                            except Exception as sign_e:
                                olog.error(f"为 key {oss_key} 签名 URL 时出错 (market task): {sign_e}")
                                signed_image_urls.append(None)
                        else:
                            signed_image_urls.append(None)

                deadline_timestamp = task_data.get('promotion_task_docs', {}).get('end_date')
                deadline_str = 'N/A'
                if deadline_timestamp is not None:
                    try:
                        dt_object = datetime.fromtimestamp(int(deadline_timestamp))
                        deadline_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")
                    except (TypeError, ValueError):
                        olog.error(f"转换时间戳时出错: {deadline_timestamp}")

                formatted_task = {
                    'id_': str(task_data['_id']),
                    'title': task_data['final_title'],
                    'content': task_data['final_content'],
                    'imageUrls': signed_image_urls,
                    'domain': task_data.get('current_task_domain', '未知领域'),
                    'platform': task_data.get('current_task_platform', '未知平台'),
                    'deadline': deadline_str,
                    'isPrioritized': task_data.get('isPrioritized', False),
                    'matchesDomainFilter': task_data.get('matchesDomainFilter', False)
                }
                all_formatted_tasks_candidates.append(formatted_task)

            if not all_formatted_tasks_candidates:
                return {'market_tasks': [], 'total_count': 0}

            domain_matched_tasks = [task for task in all_formatted_tasks_candidates if task['matchesDomainFilter']]

            tasks_to_display_pool = []
            if preferred_domain and domain_matched_tasks:
                tasks_to_display_pool = domain_matched_tasks
                olog.info(f"Account {account_id}: Found {len(domain_matched_tasks)} tasks matching both platform '{preferred_platform}' and domain '{preferred_domain}'.")
            else:
                tasks_to_display_pool = all_formatted_tasks_candidates
                if preferred_domain:
                    olog.info(f"Account {account_id}: No tasks matched domain '{preferred_domain}'. Using {len(all_formatted_tasks_candidates)} platform-matched tasks (platform: '{preferred_platform}').")
                else:
                    olog.info(f"Account {account_id}: No domain preference. Using {len(all_formatted_tasks_candidates)} platform-matched tasks (platform: '{preferred_platform}'). Sorted by any product domain match.")

            total_candidate_count_in_pool = len(tasks_to_display_pool)

            # Python-side pagination
            start_index = page * limit
            end_index = start_index + limit
            final_tasks_to_return = tasks_to_display_pool[start_index:end_index]

            olog.info(f"For account {account_id} (Platform: {preferred_platform}, Domain Pref: {preferred_domain}), page {page}, limit {limit}: selected {len(final_tasks_to_return)} tasks out of {total_candidate_count_in_pool} total candidates for display.")

            return {
                'market_tasks': final_tasks_to_return,
                'total_count': total_candidate_count_in_pool
            }

        except ValidationError as ve:
            olog.error(f"查询市场任务时验证错误 (account_id: {account_id}): {ve}")
            raise MException(f"查询参数错误: {ve}")
        except MException as me:  # Catch MExceptions from _get_valid_account or others
            raise me
        except Exception as e:
            olog.error(f"查询市场推广任务时出错 (account_id: {account_id}): {e}", exc_info=True)
            raise MException(f"查询市场推广任务时出错: {e}")

    @auth_required(['creator', 'admin'])
    def accept_task(self, data: dict):
        """
        # 目的
        允许用户接取一个可用的推广任务详情，将其分配给用户指定的账户，并进行任务限制检查。

        # 算法描述
        1.  **参数校验**:
            *   校验 `user_id` (用户ID), `promotion_task_detail_id` (推广任务详情ID), 和 `account_id` (账户ID) 是否已提供。如果任一参数缺失，抛出 `MException`。

        2.  **账户有效性验证**:
            *   根据 `account_id` 和 `user_id` 从数据库获取账户 (`Account`) 信息。
            *   如果账户不存在或不属于该用户，抛出 `MException`。
            *   检查账户的 `status` 是否为 "在线"。如果不是，抛出 `MException`，提示账户状态异常无法接取任务。

        3.  **任务接取限制检查**:
            *   **未完成任务数检查**:
                *   统计指定 `account_id` 下，`PromotionTaskDetail` 记录中 `validation_status` 不为 "成功" 的任务数量。
                *   如果该数量达到或超过 `MAX_UNCOMPLETED_TASKS` (最大未完成任务数限制)，抛出 `MException`，提示未完成任务过多。
            *   **每日已接任务数检查**:
                *   统计指定 `account_id` 下，在当前自然日内 (根据服务器本地时间计算的当日零点到次日零点的时间戳范围) `accepted_at` 字段记录的任务数量。
                *   如果该数量达到或超过 `DAILY_TASK_LIMIT` (每日任务接取上限)，抛出 `MException`，提示今日已达接取上限。

        4.  **原子化更新任务详情**:
            *   使用 `PromotionTaskDetail.objects(id=promotion_task_detail_id, user_id=None).modify(...)` 方法尝试原子性地更新任务详情:
                *   设置 `user_id` 为当前用户ID。
                *   设置 `account_id` 为指定的账户ID。
                *   设置 `accepted_at` 为当前时间戳。
                *   设置 `validation_status` 为 "待验证"。
                *   清空 `validation_details`。
                *   `new=True` 确保如果更新成功，返回更新后的文档。
            *   **并发处理与错误检查**:
                *   如果 `modify` 操作返回 `None` (表示没有文档被更新，通常是因为 `user_id` 在查询时已经不是 `None`，即任务已被他人接取或状态改变):
                    *   尝试重新获取该 `promotion_task_detail_id` 的任务。
                    *   如果任务的 `user_id` 确实不再是 `None`，抛出 `MException` 提示任务已被接取。
                    *   如果任务的 `user_id` 仍然是 `None` (理论上罕见，表示 `modify` 失败但条件依然满足)，记录错误日志并抛出通用错误 `MException`。
                    *   如果重新获取任务时抛出 `DoesNotExist`，则抛出 `MException` 提示任务不存在。
        
        5.  **异常处理**:
            *   捕获 `MException` (自定义业务异常) 并直接抛出。
            *   捕获其他所有 `Exception`，记录错误日志，并抛出一个包含通用错误信息的 `MException`。
        """
        user_id = data.get('user_id')
        promotion_task_detail_id = data.get('promotion_task_detail_id')
        account_id = data.get('account_id')

        if not promotion_task_detail_id:
            raise MException("缺少必要的参数 'promotion_task_detail_id'")
        if not account_id:
            raise MException("缺少必要的参数 'account_id'")
        if not user_id:
            raise MException("无法获取用户信息，请重新登录")

        try:
            # Inlined _get_valid_account
            try:
                account_for_accept = Account.objects.get(id=account_id, user_id=str(user_id))
                if account_for_accept.status != '在线':
                    raise MException(f"账户 '{account_for_accept.name}' 当前状态为 {account_for_accept.status}，无法接取任务")
                # account_for_accept is valid here
            except DoesNotExist:
                raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
            # End Inlined _get_valid_account

            # Inlined _check_task_limits_for_account
            account_id_str_for_limits = str(account_for_accept.id)
            # Inlined _get_account_task_counts
            # Calculate start and end of the current local day (system timezone) as UTC timestamps for limits
            now_local_for_limits = datetime.now()
            start_of_day_local_for_limits = datetime.combine(now_local_for_limits.date(), datetime.min.time())
            end_of_day_local_for_limits = start_of_day_local_for_limits + timedelta(days=1)
            start_ts_for_limits = int(start_of_day_local_for_limits.timestamp())
            end_ts_for_limits = int(end_of_day_local_for_limits.timestamp())

            accepted_today_for_limits = PromotionTaskDetail.objects(
                account_id=account_id_str_for_limits,
                accepted_at__gte=start_ts_for_limits,
                accepted_at__lt=end_ts_for_limits
            ).count()

            uncompleted_for_limits = PromotionTaskDetail.objects(
                account_id=account_id_str_for_limits,
                validation_status__ne='成功'
            ).count()
            # End Inlined _get_account_task_counts

            if uncompleted_for_limits >= MAX_UNCOMPLETED_TASKS:
                raise MException(f"账户 '{account_for_accept.name}' 当前有 {uncompleted_for_limits} 个未完成的任务 (验证状态非'成功')，达到上限 ({MAX_UNCOMPLETED_TASKS}个)，请等待任务验证成功后再接取新任务")

            if accepted_today_for_limits >= DAILY_TASK_LIMIT:
                raise MException(f"账户 '{account_for_accept.name}' 今日已接取 {accepted_today_for_limits} 个任务，达到每日上限 ({DAILY_TASK_LIMIT}个)")
            # End Inlined _check_task_limits_for_account

            updated_task_detail_doc = PromotionTaskDetail.objects(
                id=promotion_task_detail_id,
                user_id=None
            ).modify(
                set__user_id=str(user_id),
                set__account_id=str(account_id),
                set__accepted_at=int(time.time()),
                set__validation_status='待验证',
                set__validation_details=None,
                new=True
            )

            if updated_task_detail_doc is None:
                try:
                    existing_task = PromotionTaskDetail.objects.get(id=promotion_task_detail_id)
                    if existing_task.user_id is not None:
                        raise MException("任务已被其他用户接取或状态已变更，请刷新后重试")
                    else:
                        # This case (modify failed but user_id is still None) should be rare
                        olog.error(f"Atomic modify failed for task {promotion_task_detail_id} but user_id is still None.")
                        raise MException("接取任务失败，请稍后重试 (并发冲突或未知错误)")
                except DoesNotExist:
                    raise MException("任务不存在，请刷新后重试")

            # The entire _format_single_task_detail block and subsequent return
            # are removed as per the requirement for the method to not return any value.
            # olog.info(f"任务 {updated_task_detail_doc.id} 已被用户 {user_id} 的账户 {account_id} 成功接取。") # Optional: Add a log entry

        except MException as me:
            raise me
        except Exception as e:
            olog.error(f"接取任务时发生意外错误 for detail_id {promotion_task_detail_id}: {e}", exc_info=True)
            raise MException(f"接取任务时发生意外错误: {e}")

    @auth_required(['creator', 'admin'])
    def get_promotion_task_detail(self, data: dict):
        """
        # 目的
        获取指定推广任务详情的完整信息，包括关联的素材内容、图片、状态信息和平台，并处理图片URL签名以供前端展示。

        # 算法描述
        1.  **参数校验与初始化**:
            *   检查必要参数 `promotion_task_detail_id` 是否提供，若缺失则抛出异常。
            *   尝试将任务ID转换为MongoDB的ObjectId格式以提前捕获无效ID。

        2.  **聚合管道构建与查询**:
            *   使用MongoDB聚合管道从`PromotionTaskDetail`集合查询指定ID的任务详情。
            *   关联查询`AiGeneratedMaterial`（AI生成素材）集合获取任务相关素材内容与图片。
            *   关联查询`PromotionTask`（推广任务）集合获取平台信息。
            *   通过条件表达式处理数据空值情况，确定任务的最终标题、内容和平台。
            *   投影选择前端需要的最终字段集合，并处理格式转换（如ID转字符串）。

        3.  **处理聚合结果**:
            *   检查聚合结果是否为空，如为空则抛出"任务不存在"异常。
            *   从聚合结果中提取第一个（也是唯一一个）任务数据。

        4.  **图片处理与URL签名**:
            *   从任务数据中提取素材图片列表。
            *   按图片的order字段对图片列表进行排序。
            *   为每个图片的OSS键生成临时的签名URL以供前端访问。
            *   处理图片签名过程中可能出现的异常。

        5.  **结果格式化与返回**:
            *   构建包含所有必要字段的结果字典，确保字段格式符合前端要求。
            *   返回包含完整任务详情的响应。

        6.  **异常处理**:
            *   捕获并处理验证错误、数据不存在错误和其他类型的异常。
            *   记录错误日志并返回用户友好的错误消息。
        """
        promotion_task_detail_id = data.get('promotion_task_detail_id')

        if not promotion_task_detail_id:
            raise MException("缺少必要的参数 'promotion_task_detail_id'")

        try:
            # Attempt to convert to ObjectId to catch validation errors early
            from bson import ObjectId
            try:
                detail_obj_id = ObjectId(promotion_task_detail_id)
            except Exception:
                raise ValidationError("提供的任务详情ID格式无效")

            pipeline = [
                {'$match': {'_id': detail_obj_id}},
                # Convert string IDs to ObjectIds for lookups
                {
                    '$addFields': {
                        'ai_generated_material_oid': {'$toObjectId': '$ai_generated_material_id'},
                        'promotion_task_oid': {'$toObjectId': '$promotion_task_id'}
                    }
                },
                # Lookup AiGeneratedMaterial
                {
                    '$lookup': {
                        'from': AiGeneratedMaterial._get_collection_name(),
                        'localField': 'ai_generated_material_oid',
                        'foreignField': '_id',
                        'as': 'material_docs'
                    }
                },
                {'$unwind': {'path': '$material_docs', 'preserveNullAndEmptyArrays': True}},
                
                # Lookup PromotionTask to get platform info
                {
                    '$lookup': {
                        'from': PromotionTask._get_collection_name(),
                        'localField': 'promotion_task_oid',
                        'foreignField': '_id',
                        'as': 'promotion_task_docs'
                    }
                },
                {'$unwind': {'path': '$promotion_task_docs', 'preserveNullAndEmptyArrays': True}},
                
                # Determine final title and content directly from material
                {
                    '$addFields': {
                        'final_title': {
                            '$cond': {
                                'if': {'$and': ['$material_docs', {'$ne': ['$material_docs.title', None]}]}, # Simpler condition, just check if title exists
                                'then': '$material_docs.title',
                                'else': '无标题' # Default if material_docs is null or title is null
                            }
                        },
                        'final_content': {
                            '$cond': {
                                'if': {'$and': ['$material_docs', {'$ne': ['$material_docs.content', None]}]}, # Simpler condition
                                'then': '$material_docs.content',
                                'else': '' # Default if material_docs is null or content is null
                            }
                        },
                        'platform': {
                            '$cond': {
                                'if': {'$and': ['$promotion_task_docs', {'$ne': ['$promotion_task_docs.platform', None]}]},
                                'then': '$promotion_task_docs.platform',
                                'else': '未知平台'
                            }
                        }
                    }
                },
                # Project the fields needed for the final output
                {
                    '$project': {
                        '_id': 0, 
                        'id_': {'$toString': '$_id'},
                        'user_id': '$user_id',
                        'account_id': '$account_id',
                        'accepted_at': '$accepted_at',
                        'title': '$final_title',
                        'content': '$final_content',
                        'images_from_material': {'$ifNull': ['$material_docs.images', []]},
                        'publish_url': {'$ifNull': ['$publish_url', '']},
                        'publish_at': '$publish_at',
                        'validation_status': '$validation_status',
                        'validation_details': '$validation_details',
                        'platform': '$platform'
                    }
                }
            ]

            aggregated_results = list(PromotionTaskDetail.objects.aggregate(*pipeline))

            if not aggregated_results:
                olog.warning(f"尝试获取不存在的任务详情 (聚合): ID {promotion_task_detail_id}")
                raise MException("任务详情不存在或已被删除")

            task_data = aggregated_results[0]

            # Sign image URLs
            signed_image_urls = []
            oss_client = OSSClient.getInstance()
            images_list = task_data.get('images_from_material', []) # Will be empty list if material_docs was null

            # We attempt to get images even if material_docs might have been null or "deleted"
            # The images_list will be empty if material_docs was null or had no images field.
            if images_list: 
                sorted_images = sorted(
                    images_list,
                    key=lambda img_info_lambda: img_info_lambda.get('order') if isinstance(img_info_lambda, dict) and 'order' in img_info_lambda else float('inf')
                )
                for img_info in sorted_images:
                    oss_key = img_info.get('oss_key') if isinstance(img_info, dict) else None
                    if oss_key:
                        try:
                            signed_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                            signed_image_urls.append(signed_url)
                        except Exception as sign_e:
                            olog.error(f"为 key {oss_key} 签名 URL 时出错 (聚合详情): {sign_e}")
                            signed_image_urls.append(None)
                    else:
                        signed_image_urls.append(None)

            formatted_detail = {
                'id_': task_data['id_'],
                'user_id': task_data.get('user_id'),
                'account_id': task_data.get('account_id'),
                'accepted_at': task_data.get('accepted_at'),
                'title': task_data['title'],
                'content': task_data['content'],
                'imageUrls': signed_image_urls,
                'publish_url': task_data.get('publish_url', ''),
                'publish_at': task_data.get('publish_at'),
                'validation_status': task_data.get('validation_status'),
                'validation_details': task_data.get('validation_details'),
                'platform': task_data.get('platform', '未知平台')
            }

            return {'task_detail': formatted_detail}

        except ValidationError as ve: # Handles ObjectId conversion error too
            olog.warning(f"获取任务详情时ID格式无效: {promotion_task_detail_id} - {ve}")
            raise MException("提供的任务详情ID格式无效")
        except DoesNotExist: # Should be caught by empty aggregation result now
            olog.warning(f"尝试获取不存在的任务详情: ID {promotion_task_detail_id}")
            raise MException("任务详情不存在或已被删除")
        except MException as me:
            raise me
        except Exception as e:
            olog.error(f"获取任务详情时发生意外错误 (ID: {promotion_task_detail_id}): {e}", exc_info=True)
            raise MException(f"获取任务详情时发生意外错误: {e}")

    @auth_required(['creator', 'admin'])
    def query_accepted_tasks(self, data: dict):
        """
        # 目的
        查询指定用户已接取的推广任务列表。此方法支持按账户ID进行可选的筛选，并提供分页功能。
        为了提升查询性能并直接在数据库层面处理数据关联和初步筛选，本方法采用了 MongoDB 的聚合管道。

        # 算法描述
        1.  **参数处理与初始化**: 提取并校验 `user_id`, `account_id` (必填), `page`, `limit` 参数。计算分页所需的 `skip` 值。
        2.  **总数统计**: 根据 `user_id` 和 `account_id` (校验归属) 查询符合条件的任务总数。若为0，则提前返回空结果。
        3.  **聚合查询**: 
            *   **筛选与排序**: 基于用户和账户筛选任务，并按接取时间降序排序。
            *   **分页**: 应用 `$skip` 和 `$limit`。
            *   **数据关联**: 使用 `$lookup` 关联 `PromotionTask` 和 `AiGeneratedMaterial` 集合。
            *   **字段投影**: 转换ID格式，选择并格式化最终输出所需的字段 (如 `id_`, `accepted_at`, `title`, `imageUrls`, `deadline`, 状态等)。
        4.  **数据后处理**: 
            *   为素材图片生成临时的OSS签名URL。
            *   格式化任务截止日期时间戳为字符串。
            *   根据素材标题和产品标题确定最终显示标题。
        5.  **结果返回**: 返回包含当前页任务列表 (`accepted_tasks`) 和总任务数 (`total_count`) 的字典。
        6.  **异常处理**: 捕获并处理校验、数据库及其他潜在异常，返回用户友好的错误信息。
        """
        user_id = data.get('user_id')
        account_id = data.get('account_id')
        page = data.get('page', 0)
        limit = data.get('limit', 5)

        if not user_id:
            raise MException("无法获取用户信息，请重新登录")
        if not account_id:
            raise MException("必需提供 'account_id' 参数以查询已接任务")

        try:
            page = int(page)
            limit = int(limit)
            if page < 0 or limit <= 0:
                raise ValueError("Page and limit must be non-negative integers.")
        except (ValueError, TypeError):
            raise MException("分页参数 'page' 和 'limit' 必须是有效的非负整数。")

        skip = page * limit

        try:
            # --- 1. 获取总数 --- 
            match_conditions_for_count = {'user_id': str(user_id)}
            if account_id:
                try:
                    # 验证账户ID是否属于该用户，防止越权查询计数
                    Account.objects.get(id=account_id, user_id=str(user_id))
                    match_conditions_for_count['account_id'] = str(account_id)
                except DoesNotExist:
                    raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
            
            total_count = PromotionTaskDetail.objects(**match_conditions_for_count).count()

            if total_count == 0:
                return {'accepted_tasks': [], 'total_count': 0}

            # --- 2. 构建和执行聚合查询 --- 
            pipeline = [
                {'$match': match_conditions_for_count},
                {'$sort': {'accepted_at': -1, '_id': -1}},
                {'$skip': skip},
                {'$limit': limit},
                {'$addFields': {
                    'promotion_task_oid': {'$toObjectId': '$promotion_task_id'},
                    'ai_generated_material_oid': {'$toObjectId': '$ai_generated_material_id'}
                }},
                # Lookup PromotionTask
                {'$lookup': {
                    'from': PromotionTask._get_collection_name(),
                    'localField': 'promotion_task_oid',
                    'foreignField': '_id',
                    'as': 'promotion_task_docs'
                }},
                {'$unwind': '$promotion_task_docs'}, 
                # Lookup AiGeneratedMaterial
                {'$lookup': {
                    'from': AiGeneratedMaterial._get_collection_name(),
                    'localField': 'ai_generated_material_oid',
                    'foreignField': '_id',
                    'as': 'material_docs'
                }},
                {'$unwind': '$material_docs'},
                # Add product_oid for conditional lookup (handles null/empty product_id)
                {'$addFields': {
                    'product_oid_for_lookup': {
                        '$cond': {
                            'if': {'$and': [
                                {'$ne': ['$promotion_task_docs.product_id', None]},
                                {'$ne': ['$promotion_task_docs.product_id', ""]}
                            ]},
                            'then': {'$toObjectId': '$promotion_task_docs.product_id'},
                            'else': None 
                        }
                    }
                }},
                # Lookup Product
                {'$lookup': {
                    'from': Product._get_collection_name(),
                    'localField': 'product_oid_for_lookup',
                    'foreignField': '_id',
                    'as': 'product_docs_array'
                }},
                {'$unwind': {'path': '$product_docs_array', 'preserveNullAndEmptyArrays': True}},
                # Project final fields
                {'$project': {
                    '_id': 0, 
                    'id_': {'$toString': '$_id'},
                    'accepted_at': '$accepted_at',
                    'publish_url': {'$ifNull': ['$publish_url', '']},
                    'publish_at': '$publish_at',
                    'validation_status': '$validation_status',
                    'validation_details': '$validation_details',
                    'material_title': '$material_docs.title',
                    'material_images': {'$ifNull': ['$material_docs.images', []]}, # Ensure images is an array
                    'promotion_task_end_date': '$promotion_task_docs.end_date',
                    'product_title': {
                        '$cond': {
                            'if': {'$and': [
                                {'$ne': ['$product_docs_array', None]},
                                {'$eq': ['$product_docs_array.is_deleted', False]}
                            ]},
                            'then': '$product_docs_array.title',
                            'else': None
                        }
                    }
                }}
            ]

            aggregation_results = list(PromotionTaskDetail.objects.aggregate(*pipeline))

            # --- 3. 数据后处理 --- 
            result_list = []
            oss_client = OSSClient.getInstance()

            for task_data in aggregation_results:
                # a. Sign image URLs
                signed_image_urls = []
                material_images_list = task_data.get('material_images', [])
                if material_images_list: 
                    sorted_material_images = sorted(
                        material_images_list,
                        key=lambda img_info_lambda: img_info_lambda.get('order') if isinstance(img_info_lambda, dict) and 'order' in img_info_lambda else float('inf')
                    )
                    for img_info in sorted_material_images:
                        oss_key = img_info.get('oss_key') if isinstance(img_info, dict) else None
                        if oss_key:
                            try:
                                signed_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                                signed_image_urls.append(signed_url)
                            except Exception as sign_e:
                                olog.error(f"为 key {oss_key} 签名 URL 时出错 (accepted task list aggregation): {sign_e}")
                                signed_image_urls.append(None)
                        else:
                            signed_image_urls.append(None)
                
                # b. Format deadline
                deadline_timestamp = task_data.get('promotion_task_end_date')
                deadline_str = 'N/A'
                if deadline_timestamp is not None:
                    try:
                        dt_object = datetime.fromtimestamp(int(deadline_timestamp))
                        deadline_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")
                    except (TypeError, ValueError):
                        olog.error(f"转换截止时间戳时出错: {deadline_timestamp}")
                
                # c. Determine title
                title = task_data.get('material_title') or task_data.get('product_title') or '无标题'

                result_list.append({
                    'id_': task_data['id_'],
                    'accepted_at': task_data.get('accepted_at'),
                    'title': title,
                    'imageUrls': signed_image_urls,
                    'deadline': deadline_str,
                    'publish_url': task_data.get('publish_url', ''),
                    'publish_at': task_data.get('publish_at'),
                    'validation_status': task_data.get('validation_status'),
                    'validation_details': task_data.get('validation_details'),
                })

            return {
                'accepted_tasks': result_list,
                'total_count': total_count
            }

        except ValidationError as ve:
            olog.error(f"查询已接取任务时验证错误: {ve}")
            raise MException(f"查询参数错误: {ve}")
        except MException as me:
            raise me
        except Exception as e:
            olog.error(f"查询已接取任务时出错: {e}", exc_info=True)
            raise MException(f"查询已接取任务时出错: {e}")

    @auth_required(['creator', 'admin'])
    def give_up_task(self, data: dict):
        """
        # 目的
        允许用户放弃一个已接取但尚未验证成功的推广任务。

        # 算法描述
        1.  **参数校验**: 校验 `user_id` (用户ID) 和 `promotion_task_detail_id` (推广任务详情ID) 是否已提供。若任一参数缺失，抛出 `MException`。
        2.  **任务获取与状态检查**:
            *   根据 `promotion_task_detail_id` 和 `user_id` 从数据库获取 `PromotionTaskDetail` 对象，仅查询必要的字段以优化。
            *   如果任务不存在或不属于该用户，记录警告日志并抛出 `MException`。
            *   检查任务的 `validation_status` (验证状态)。如果为 "成功"，记录警告日志并抛出 `MException`。
        3.  **执行任务放弃**:
            *   使用 `modify` 原子性地更新 `PromotionTaskDetail` 对象：
                *   将 `user_id`, `account_id`, `accepted_at`, `publish_url`, `publish_at`, `validation_status`, `validation_details` 设置为 `None` 或初始状态。
                *   将互动计数器 (`view_count` 等) 重置为 0。
        4.  **成功响应与日志**:
            *   记录用户成功放弃任务的 `info` 级别日志。
            *   返回成功消息。
        5.  **异常处理**:
            *   捕获 `DoesNotExist` (在任务获取阶段处理)。
            *   捕获 `MException` (自定义业务异常) 并直接抛出。
            *   捕获其他所有 `Exception`，记录 `error` 级别日志，并抛出一个包含通用错误信息的 `MException`。
        """
        user_id = data.get('user_id')
        promotion_task_detail_id = data.get('promotion_task_detail_id')

        if not user_id:
            raise MException("无法获取用户信息，请重新登录")
        if not promotion_task_detail_id:
            raise MException("缺少必要的参数 'promotion_task_detail_id'")

        try:
            task_detail = PromotionTaskDetail.objects.only('id', 'user_id', 'validation_status').get(
                id=promotion_task_detail_id,
                user_id=str(user_id)
            )

            if task_detail.validation_status == '成功':
                olog.warning(f"用户 {user_id} 尝试放弃已验证成功的任务 {promotion_task_detail_id}。")
                raise MException("任务已验证成功，无法放弃")

            PromotionTaskDetail.objects(id=promotion_task_detail_id).modify(
                set__user_id=None,
                set__account_id=None,
                set__accepted_at=None,
                set__publish_url=None,
                set__publish_at=None,
                set__validation_status=None,
                set__validation_details=None,
                set__view_count=0,
                set__like_count=0,
                set__comment_count=0,
                set__favorite_count=0,
                set__share_count=0
            )
            olog.info(f"用户 {user_id} 成功放弃任务 {promotion_task_detail_id}。")
            return {'message': '任务放弃成功'}

        except DoesNotExist:
            olog.warning(f"用户 {user_id} 尝试放弃任务 {promotion_task_detail_id} 失败：任务不存在或用户无权操作。")
            raise MException("任务不存在或您无权操作")
        except MException as me:
            raise me
        except Exception as e:
            olog.error(f"放弃任务 {promotion_task_detail_id} 时发生意外错误: {e}", exc_info=True)
            raise MException(f"放弃任务时发生意外错误: {e}")

    @auth_required(['creator', 'admin'])
    def update_publish_link(self, data: dict):
        """
        # 目的
        更新指定推广任务详情的发布链接，并在更新后触发后续的验证流程。

        # 算法描述
        1.  **参数校验**:
            *   校验 `user_id` (用户ID), `promotion_task_detail_id` (推广任务详情ID), 和 `publish_url_input` (用户输入的发布链接内容) 是否已提供。若任一参数缺失，抛出 `MException`。

        2.  **任务详情获取与状态检查**:
            *   根据 `promotion_task_detail_id` 和 `user_id` 从数据库获取 `PromotionTaskDetail` (推广任务详情) 对象。
            *   如果任务不存在或不属于该用户，抛出 `MException`。
            *   检查任务的 `validation_status` (验证状态)。如果状态为 "成功"，则抛出 `MException`，提示任务已验证成功，无法直接修改。

        3.  **关联推广任务与平台信息获取**:
            *   通过任务详情中的 `promotion_task_id` 获取关联的 `PromotionTask` (推广任务) 对象。
            *   检查推广任务是否已被标记为删除 (`is_deleted`)，若是则抛出 `MException`。
            *   获取推广任务的 `platform` (平台) 信息。如果平台信息缺失，抛出 `MException`。

        4.  **发布链接提取与校验**:
            *   调用 `extract_url_from_text` 工具函数，根据获取到的 `platform` 从用户输入的 `publish_url_input` 中提取规范化的发布链接。
            *   如果未能提取到有效链接，抛出 `MException`。
            *   查询数据库，检查提取出的 `extracted_url` 是否已被其他 `PromotionTaskDetail` (除当前任务外) 使用。如果已被使用，抛出 `MException`，提示链接重复。

        5.  **更新任务详情**:
            *   获取当前时间戳。
            *   原子性地更新当前 `PromotionTaskDetail` 对象的以下字段：
                *   `publish_url`: 设置为提取出的 `extracted_url`。
                *   `publish_at`: 设置为当前时间戳。
                *   `validation_status`: 设置为 "待验证"。
                *   `validation_details`: 清空 (设置为 `None`)。

        6.  **触发异步验证 (基于平台)**:
            *   判断推广任务的 `platform`：
                *   如果平台是 "小红书"，将 `promotion_task_detail_id` 发布到预定义的 Redis Set (`XHS_TASK_URL_VERIFY_SET`) 中，以供后端 Worker 进行异步验证。
                *   记录发布到 Redis Set 的结果 (成功、失败或已存在)。
            *   如果平台未配置对应的 Redis 验证集，则记录日志说明链接已更新但未自动提交验证。

        7.  **返回结果**:
            *   返回一个包含成功提示信息的字典。

        8.  **异常处理**:
            *   捕获 `DoesNotExist` (任务不存在)、`MException` (自定义业务异常) 及其他通用 `Exception`。
            *   记录错误日志，并向上抛出包含用户友好信息的 `MException`。
        """
        user_id = data.get('user_id')
        promotion_task_detail_id = data.get('promotion_task_detail_id')
        publish_url_input = data.get('publish_url')

        if not user_id:
            raise MException("无法获取用户信息，请重新登录")
        if not promotion_task_detail_id:
            raise MException("缺少必要的参数 'promotion_task_detail_id'")
        if not publish_url_input:
            raise MException("发布链接不能为空")

        try:
            task_detail = PromotionTaskDetail.objects.get(
                id=promotion_task_detail_id,
                user_id=str(user_id)
            )

            if task_detail.validation_status == '成功':
                raise MException("任务已验证成功，如需修改，请先联系管理员处理当前验证状态或放弃任务后重新接取。")

            try:
                promotion_task = PromotionTask.objects.only('platform', 'is_deleted').get(id=task_detail.promotion_task_id)
                if promotion_task.is_deleted:
                    raise MException("关联的推广计划已被删除，无法更新链接。")
                platform = promotion_task.platform
                if not platform:
                    raise MException("无法确定任务所属平台，无法提取链接")
            except DoesNotExist:
                raise MException("关联的推广任务不存在，无法处理链接")

            extracted_url = extract_url_from_text(publish_url_input, platform)
            if not extracted_url:
                raise MException(f"未能从输入内容中提取有效的 {platform} 链接，请检查输入")

            existing_link_task = PromotionTaskDetail.objects(
                publish_url=extracted_url,
                id__ne=promotion_task_detail_id
            ).first()

            if existing_link_task:
                raise MException(f"该链接已被其他任务使用 (任务ID: {existing_link_task.id})，请检查链接是否正确")

            current_time = int(time.time())
            task_detail.modify(
                set__publish_url=extracted_url,
                set__publish_at=current_time,
                set__validation_status='待验证',
                set__validation_details=None
            )

            redis_key_to_publish = None
            if platform == "小红书":
                redis_key_to_publish = XHS_TASK_URL_VERIFY_SET

            if redis_key_to_publish:
                publish_result = publish_to_redis_set(redis_key_to_publish, str(promotion_task_detail_id))
                if publish_result is None or publish_result == 0:
                    olog.warning(f"警告: 将任务 ID {promotion_task_detail_id} 发布到 Redis Set '{redis_key_to_publish}' 失败或已存在。")
                else:
                    olog.info(f"任务 ID {promotion_task_detail_id} 已成功发布到 Redis Set '{redis_key_to_publish}' 进行验证。")
            else:
                olog.info(f"未配置平台 {platform} 的Redis验证集，任务 {promotion_task_detail_id} 链接已更新但未自动提交验证。")

            return {'message': '发布链接更新成功，已提交验证'}

        except DoesNotExist:
            raise MException("任务不存在或您无权操作")
        except MException as me:
            raise me
        except Exception as e:
            olog.error(f"更新发布链接时发生意外错误: {e}", exc_info=True)
            raise MException(f"更新发布链接时发生意外错误: {e}")
