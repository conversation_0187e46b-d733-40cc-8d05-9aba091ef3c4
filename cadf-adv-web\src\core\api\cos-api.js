/**
 * COS文件管理器
 * 提供文件上传和URL签名等功能的统一接口
 *
 * @class FileManager
 *
 * @example
 * // 导入实例
 * import fileManager from '@/core/api/cos-api';
 *
 * // 上传普通文件
 * const file = event.target.files[0];
 * const fileKey = await fileManager.uploadFile(file, 'uploads');
 *
 * // 上传Base64格式文件
 * const base64Data = 'data:image/png;base64,...';
 * const fileKey = await fileManager.uploadFileFromBase64(base64Data, 'png', 'images');
 *
 * // 获取文件访问链接
 * const url = await fileManager.getSignedUrl('GET', fileKey);
 *
 * // 批量获取文件链接
 * const urls = await fileManager.fetchFileUrls(['key1', 'key2']);
 *
 * @property {string} bucket - 存储桶名称，从配置文件中读取
 * @property {number} MAX_FILE_SIZE - 文件大小上限，默认20MB
 *
 * @throws {Error} 当文件大小超过限制时抛出错误
 * @throws {Error} 当base64字符串格式错误时抛出错误
 * @throws {Error} 当上传失败时抛出错误
 */

import api from "@/core/api/api";
import axios from "axios";

class FileManager {
    constructor() {
        this.MAX_FILE_SIZE = 20 * 1024 * 1024;
    }

    // 随机生成文件名
    genFileName(extension, prefix = "", originalName = "") {
        if (typeof extension !== "string" || extension.length === 0) {
            throw new Error("扩展名是必需的,且必须是非空字符串");
        }

        const randomNum = Math.random().toString(36).substring(2, 8); // 生成6位随机字符串

        // 如果提供了原始文件名，则使用原始文件名（去掉扩展名）+ 随机字符串
        if (originalName) {
            const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
            // 去掉了 timestamp
            return `${nameWithoutExt}_${randomNum}.${extension}`;
        }

        // 如果没有原始文件名，则使用默认的生成方式（去掉 timestamp）
        return `${prefix ? `${prefix}_` : ""}${randomNum}.${extension}`;
    }

    // 生成签名URL
    async getSignedUrl(method, key) {
        return await api({
            resource: "oss",
            method_name: "gen_signed_url",
            signed_method: method,
            signed_key: key,
        });
    }

    // 获取多个文件的URL
    async fetchFileUrls(keys) {
        const urlPromises = keys.map((key) => this.getSignedUrl("GET", key));
        return await Promise.all(urlPromises);
    }

    // 上传文件
    async uploadFile(file, parentKey, onProgress) {
        // 检查文件大小
        if (file.size > this.MAX_FILE_SIZE) {
            throw new Error(
                `文件大小不能超过${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
            );
        }

        const fileName = file.name;
        const fileExtension = fileName.split(".").pop() || "";
        const fileKey = `${parentKey}/${this.genFileName(fileExtension, "", fileName)}`;

        try {
            const uploadUrl = await this.getSignedUrl("PUT", fileKey);

            await axios.put(uploadUrl, file, {
                headers: {
                    "Content-Type": "application/octet-stream",
                },
                onUploadProgress: (progressEvent) => {
                    if (onProgress && progressEvent.total) {
                        const percentCompleted = Math.round(
                            (progressEvent.loaded * 100) / progressEvent.total
                        );
                        onProgress(percentCompleted);
                    }
                },
            });

            return fileKey;
        } catch (error) {
            console.error("上传错误:", error);
            throw error;
        }
    }

    // 通过base64上传文件
    async uploadFileFromBase64(
        base64String,
        fileExtension,
        parentKey,
        onProgress
    ) {
        // 验证输入
        if (!base64String || typeof base64String !== "string") {
            throw new Error("无效的 base64 字符串");
        }

        let byteArray;
        try {
            // 检查是否包含 data URI 前缀，如果有，去掉它
            const base64Data = base64String.split(",")[1] || base64String;

            // 解码base64字符串
            const byteCharacters = atob(base64Data);
            byteArray = new Uint8Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteArray[i] = byteCharacters.charCodeAt(i);
            }
        } catch (error) {
            console.error("Base64 解码错误:", error);
            throw new Error("解码 base64 字符串失败");
        }

        const blob = new Blob([byteArray], {type: "application/octet-stream"});

        // 检查文件大小
        if (blob.size > this.MAX_FILE_SIZE) {
            throw new Error(
                `文件大小不能超过 ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
            );
        }

        const fileKey = `${parentKey}/${this.genFileName(fileExtension)}`;

        try {
            const uploadUrl = await this.getSignedUrl("PUT", fileKey);

            await axios.put(uploadUrl, blob, {
                headers: {
                    "Content-Type": "application/octet-stream",
                },
                onUploadProgress: (progressEvent) => {
                    if (onProgress && progressEvent.total) {
                        const percentCompleted = Math.round(
                            (progressEvent.loaded * 100) / progressEvent.total
                        );
                        onProgress(percentCompleted);
                    }
                },
            });

            return fileKey;
        } catch (error) {
            console.error("上传错误:", error);
            throw error;
        }
    }
}

export default new FileManager();
