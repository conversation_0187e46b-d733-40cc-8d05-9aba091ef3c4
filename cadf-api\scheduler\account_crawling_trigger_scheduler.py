from config.config import XHS_ACCOUNTS_CRAWL_SET  # 导入 Redis Key
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_many_to_redis_set  # 导入发布函数
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import Account  # 导入 Account 模型


@register_scheduler(trigger='cron', hour='*/2', minute='0')  # 每偶数小时整点执行一次
# @register_scheduler(trigger='interval', seconds=10)  # 每10秒执行一次
class AccountCrawlingTriggerScheduler(BaseScheduler):
    def run_task(self):
        """
        **目的**: 定期获取所有状态为"在线"的账号ID，并将这些ID发布到 Redis Set 中，以触发后续针对这些账号的笔记互动数据（如点赞、阅读、评论等）的爬虫任务。

        **算法描述**:
            - **查询在线账号**: 从 `Account` 数据库集合中查找所有 `status` 字段值为 '在线' 的文档。
            - **提取账号ID**: 将查询到的每个在线账号文档的 `id` 字段转换为字符串格式，并收集到一个列表中。
            - **检查账号列表**: 判断提取到的账号ID列表是否为空。
                - **若列表为空**: 记录一条信息日志，说明没有找到在线账号，然后结束任务执行。
                - **若列表非空**: 继续执行下一步。
            - **发布账号ID**: 调用 `publish_many_to_redis_set` 函数，尝试将账号ID列表添加到名为 `XHS_ACCOUNTS_CRAWL_SET` 的 Redis Set 中。
            - **记录发布结果**: 检查 `publish_many_to_redis_set` 函数的返回值。
                - **若返回非空值 (添加成功的数量)**: 记录一条信息日志，说明成功向 Redis Set 添加了多少个账号ID，并显示总共找到的在线账号数量。
                - **若返回 None (表示添加失败)**: 记录一条错误日志，说明向 Redis Set 添加账号ID时发生错误。
            - **异常处理**: 使用 `try...except` 块包裹整个逻辑，捕获执行过程中可能出现的任何异常，并记录详细的错误日志。
        """
        try:
            online_accounts = Account.objects(status='在线')
            account_ids = [str(account.id) for account in online_accounts]

            if not account_ids:
                olog.info("没有找到在线账号，不执行爬取。")
                return

            num_added = publish_many_to_redis_set(XHS_ACCOUNTS_CRAWL_SET, account_ids)

            if num_added is not None:
                olog.info(f"成功向 Redis Set '{XHS_ACCOUNTS_CRAWL_SET}' 添加 {num_added} 个在线账号ID (共找到 {len(account_ids)} 个)。")
            else:
                olog.error(f"向 Redis Set '{XHS_ACCOUNTS_CRAWL_SET}' 添加在线账号ID时发生错误。")

        except Exception as e:
            olog.error(f"执行 AccountCrawlingTriggerScheduler 任务时出错: {e}", exc_info=True)
