import base64
import io
from typing import Literal

from PIL import Image
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

from omni.llm.agent.structured_output_agent import structured_output_agent
from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog

"""
判断图片类别智能体
"""

class ImageCategoryResult(BaseModel):
    category: Literal["美妆产品", "人像", "其他"] = Field(..., description="图片类别：美妆产品/人像/其他")
    description: str = Field(..., description="图片主要内容描述")

def classify_image_category(image_bytes: bytes) -> str:
    olog.info("开始执行 classify_image_category (Bytes模式)...")
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    img = Image.open(io.BytesIO(image_bytes))
    original_format = img.format
    olog.info(f"处理图片，原始格式为: {original_format}")

    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=original_format)
    base64_str = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")
    mime_type = original_format.lower() if original_format else "png"
    llm_image_input = {
        "type": "image_url",
        "image_url": {"url": f"data:image/{mime_type};base64,{base64_str}"},
    }

    prompt = """
# 角色
你是一位专注于电商图片分析的AI视觉专家。

# 任务
请判断输入图片的主要内容类别，只能从以下三类中选择：
1. 美妆产品: 图片主要展示为化妆品、护肤品等美妆相关产品实物，且必须有明显的瓶子、包装或其他容器。如果仅展示液体（如洗发膏的液体）或无包装，则判断为其他。
2. 人像: 图片主要为人物面部、半身或全身，且不是以产品为主。
3. 其他: 不属于上述两类的图片。

# 返回值
- category: 只能为"美妆产品"、"人像"或"其他"
- description: 用一句话描述图片主要内容
"""

    messages = [
        HumanMessage(content=[{"type": "text", "text": prompt}, llm_image_input])
    ]
    raw_result = llm.invoke(messages).content
    olog.info(f"原始LLM输出: {raw_result}")
    agent_structured_output = structured_output_agent(
        model_class=ImageCategoryResult, raw_data=raw_result
    )
    return agent_structured_output.category
