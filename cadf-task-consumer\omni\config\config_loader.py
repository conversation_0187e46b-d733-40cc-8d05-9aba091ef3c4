import os

import yaml
from dotenv import load_dotenv


class ConfigLoader:
    def __init__(self):
        env_file = '.env'
        load_dotenv(dotenv_path=env_file)
        self.config = {}
        self.load_settings()
        self.process_env = os.getenv('process_env')
        self.config.update(dict(process_env=self.process_env))
        self.load_env_config()

    def load_settings(self):
        config_file = f'config/settings.yaml'
        if os.path.exists(config_file):
            with open(config_file, 'r') as file:
                env_data = yaml.safe_load(file)
                self.config.update(env_data)

    def load_env_config(self):
        config_file = f'config/{self.process_env}.yaml'
        if os.path.exists(config_file):
            with open(config_file, 'r') as file:
                env_data = yaml.safe_load(file)
                self.config.update(env_data)

    def get_config(self):
        return self.config


config_dict = ConfigLoader().get_config()
