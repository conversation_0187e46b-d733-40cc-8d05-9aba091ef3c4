import time
import json

from omni.api.auth import auth_required, generate_token
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import User, CrewManagement
from omni.redis.redis_client import rc
from omni.log.log import olog


@register_handler("user")
class UserApi:

    def login(self, data):
        username = data.get("username")
        password = data.get("password")
        
        # 登录验证
        user = User.objects(username=username).first()
        if not user:
            raise MException("登录失败,错误的用户名或密码")
        if user["password"] != password:
            raise MException("登录失败,错误的用户名或密码")
        user = doc_to_dict(user)
        access_token = generate_token(user["id_"])
        roles = user["roles"]
        return {"access_token": access_token, "roles": roles}
        
    def creator_register(self, data):
        username = data.get("username")
        password = data.get("password")
        crew_invite_code = data.get("crew_invite_code")
        roles = ["creator"]
        
        # 检查用户名是否已存在
        existing_user = User.objects(username=username).first()
        if existing_user:
            raise MException("用户名已存在，请更换用户名")
            
        # 创建新用户
        new_user = User(
            username=username,
            password=password,
            roles=roles,
            create_at=int(time.time())
        )
        new_user.save()
        
        # 处理舰队加入逻辑
        if crew_invite_code:
            captain_user = User.objects(crew_invite_code=crew_invite_code, roles="captain").first()
            if captain_user:
                # 创建 CrewManagement 记录
                crew_management = CrewManagement(
                    captain_user_id=str(captain_user.id),
                    crew_user_id=str(new_user.id),
                    create_at=int(time.time())
                )
                crew_management.save()
                olog.info(f"用户 {new_user.id} 成功加入舰队 {captain_user.id}")
            else:
                olog.warning(f"用户 {new_user.id} 使用的邀请码 {crew_invite_code} 无效")

        # 返回用户信息和token
        user_dict = doc_to_dict(new_user)
        access_token = generate_token(user_dict["id_"])
        
        return {
            "message": "注册成功",
            "access_token": access_token,
            "roles": roles,
            "user_id": user_dict["id_"]
        }
        
    @auth_required(["admin"])
    def create(self, data):
        username = data.get("username")
        password = data.get("password")
        roles = data.get("roles", ["user"])
        
        # 检查用户名是否已存在
        existing_user = User.objects(username=username).first()
        if existing_user:
            raise MException("用户名已存在，请更换用户名")
            
        # 创建新用户
        new_user = User(
            username=username,
            password=password,
            roles=roles,
            create_at=int(time.time())
        )
        new_user.save()
        
        # 返回用户信息
        return doc_to_dict(new_user)
        
    @auth_required(["admin"])
    def update(self, data):
        user_id = data.get("id_")
        user = User.objects(id=user_id).first()
        
        if not user:
            raise MException("用户不存在")
            
        # 更新用户信息
        if "username" in data:
            # 检查用户名是否已被其他用户使用
            existing_user = User.objects(username=data["username"]).first()
            if existing_user and str(existing_user.id) != user_id:
                raise MException("用户名已存在，请更换用户名")
            user.username = data["username"]
            
        if "password" in data:
            user.password = data["password"]
            
        if "roles" in data:
            user.roles = data["roles"]
            
        user.save()
        
        # 如果更新了角色，清除Redis缓存
        if "roles" in data:
            redis_key = f"session:roles:{user_id}"
            rc.delete(redis_key)
            
        return doc_to_dict(user)

    @auth_required(["admin"])
    def delete(self, data):
        user_id = data.get("id_")
        user = User.objects(id=user_id).first()
        if not user:
            raise MException("用户不存在")
        user.delete()
        return {"message": "用户已成功删除"}

    @auth_required(["user", "admin"])
    def query_one(self, data):
        target_user_id = data.get("target_user_id")
        user = User.objects(id=target_user_id).first()
        return doc_to_dict(user)

    @auth_required(["admin"])
    def query_all(self, data):
        user = User.objects()
        return docs_to_dict(user)

    @auth_required(["user", "advertiser", "creator", "captain", "admin"])
    def get_roles(self, data):
        """获取用户角色，使用 Redis 缓存"""
        # 系统会自动获取当前用户的user_id，不需要从data中获取
        user_id = data.get("user_id")  # 这里的user_id是系统自动注入的当前用户ID

        # Redis 缓存键
        redis_key = f"session:roles:{user_id}"

        # 尝试从缓存获取角色
        cached_roles = rc.get(redis_key)
        if cached_roles:
            olog.info(f"从 Redis 获取用户 {user_id} 的角色缓存")
            return {"roles": json.loads(cached_roles)}

        # 缓存不存在，从数据库查询
        user = User.objects(id=user_id).first()
        roles = doc_to_dict(user).get("roles", [])

        # 将角色存入 Redis 缓存，设置过期时间 (例如：5 分钟)
        rc.setex(redis_key, 300, json.dumps(roles))
        olog.info(f"从数据库获取用户 {user_id} 的角色并存入 Redis 缓存")

        return {"roles": roles}


