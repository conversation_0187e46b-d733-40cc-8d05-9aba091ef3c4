"use client";

import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Box, Button, Chip, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Divider, Grid, IconButton, Pagination, Paper, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {ArrowLeft, XCircle} from 'lucide-react';
import {useParams, useRouter} from 'next/navigation';
import TokenIcon from '@mui/icons-material/Token';
import ProductCard from '@/components/ProductCard';
import {aiGeneratedMaterialApi} from '@/api/ai-generated-material-api';
import {format} from 'date-fns';

export default function GenerationListPage() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams();
    const [page, setPage] = useState(1);
    const itemsPerPage = 5;
    const [results, setResults] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [deleteCandidateId, setDeleteCandidateId] = useState(null);

    useEffect(() => {
        const taskId = params?.id;
        if (!taskId) {
            setError("缺少任务 ID");
            setLoading(false);
            return;
        }
        setLoading(true);
        setError(null);
        const fetchData = async () => {
            try {
                const response = await aiGeneratedMaterialApi.queryByTaskId(taskId, page, itemsPerPage);
                setTotalCount(response.total_count || 0);

                const validResults = (response.results || []).filter(item => item && item.id_);

                const processedResults = validResults.map(item => {
                    const imageUrls = (item.images || [])
                        .map(img => {
                            if (img.signed_url) {
                                return img.signed_url;
                            } else {
                                console.warn("Image missing signed_url or key:", img);
                                return 'https://placehold.co/300x200/cccccc/333333?text=URL+Error';
                            }
                        });

                    return {
                        ...item,
                        id: item.id_,
                        date: item.create_at ? format(new Date(item.create_at * 1000), 'yyyy-MM-dd') : 'N/A',
                        images: imageUrls,
                        viralTitle: item.title || item.content?.substring(0, 50) || '无标题',
                        tokens: item.token_consumption || 0,
                        product: item.product_id || 'N/A',
                        material: item.material_id || 'N/A',
                    };
                });
                setResults(processedResults);
            } catch (err) {
                console.error("Failed to fetch generated materials:", err);
                setError(err.message || '获取生成内容失败');
                setTotalCount(0);
                setResults([]);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [params?.id, page, itemsPerPage]);

    const displayTaskId = params?.id || '未知';

    const totalPages = Math.ceil(totalCount / itemsPerPage);

    const handlePageChange = (event, value) => {
        setPage(value);
    };

    const handleGoBack = () => {
        router.push('/protected/ai-image-text-generation');
    };

    const handleContentSelect = (contentId) => {
        router.push(`/protected/ai-image-text-generation/${params.id}/detail/${contentId}`);
    };

    const handleReject = async (e, contentId) => {
        e.stopPropagation();
        setDeleteCandidateId(contentId);
        setOpenConfirmDialog(true);
    };

    const handleCloseConfirmDialog = () => {
        setOpenConfirmDialog(false);
        setDeleteCandidateId(null);
    };

    const handleConfirmDelete = async () => {
        if (!deleteCandidateId) return;

        handleCloseConfirmDialog();

        try {
            await aiGeneratedMaterialApi.delete(deleteCandidateId);
            const deletedItemId = deleteCandidateId;
            setResults(prev => prev.filter(item => item.id !== deletedItemId));
            setTotalCount(prev => prev - 1);

            if (results.length === 1 && page > 1) {
                setPage(prevPage => prevPage - 1);
            } else if (results.length === 1 && page === 1 && totalCount <= 1) {
                setResults([]);
                setTotalCount(0);
            }

        } catch (err) {
            console.error("Failed to delete material:", err);
            alert(`删除失败: ${err.message || '未知错误'}`);
        } finally {
            setDeleteCandidateId(null);
        }
    };

    return (
        <Container maxWidth="xl" sx={{py: 3}}>
            <Box>
                <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                    <Button
                        startIcon={<ArrowLeft size={18}/>}
                        onClick={handleGoBack}
                        sx={{mr: 2}}
                    >
                        返回生成列表
                    </Button>
                    <Typography variant="h5" component="h1" sx={{fontWeight: 600, flexGrow: 1}}>
                        生成内容列表
                    </Typography>
                </Box>

                <Paper
                    elevation={0}
                    sx={{
                        mb: 4,
                        p: 0,
                        overflow: 'hidden',
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <Box sx={{
                        p: 2,
                        bgcolor: theme.palette.primary.light,
                        color: theme.palette.primary.contrastText,
                        borderBottom: `1px solid ${theme.palette.divider}`
                    }}>
                        <Typography variant="subtitle1" fontWeight="medium">
                            任务的生成内容
                        </Typography>
                    </Box>

                    <Box sx={{p: 3}}>
                        {loading && (
                            <Box sx={{display: 'flex', justifyContent: 'center', py: 5}}>
                                <CircularProgress/>
                            </Box>
                        )}

                        {error && (
                            <Alert severity="error" sx={{mb: 2}}>{error}</Alert>
                        )}

                        {!loading && !error && (
                            <>
                                <Box sx={{mb: 2}}>
                                    <Typography variant="body2" color="text.secondary">
                                        共找到 {totalCount} 个生成内容
                                    </Typography>
                                </Box>

                                <Grid container spacing={3}>
                                    {results.map((content) => (
                                        <Grid item xs={12} sm={6} md={4} lg={2.4} key={content.id}>
                                            <ProductCard
                                                images={content.images && content.images.length > 0 ? [content.images[0]] : ['https://placehold.co/300x200/eeeeee/333333?text=No+Image']}
                                                onClick={() => handleContentSelect(content.id)}
                                            >
                                                <Box sx={{p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column'}}>
                                                    <Typography gutterBottom variant="subtitle1" component="div" noWrap sx={{mb: 1, fontWeight: 500}}>
                                                        {content.title || '无标题'}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary" sx={{mb: 2, flexGrow: 1, overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical'}}>
                                                        {content.content?.substring(0, 100) || '无内容'}
                                                    </Typography>
                                                    <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto'}}>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {content.date}
                                                        </Typography>
                                                        <Chip
                                                            icon={<TokenIcon sx={{fontSize: '0.875rem', mr: 0.5}}/>}
                                                            label={`${content.tokens}`}
                                                            size="small"
                                                            color="primary"
                                                            variant="outlined"
                                                            sx={{height: 20, '& .MuiChip-label': {px: 0.5}}}
                                                        />
                                                    </Box>
                                                </Box>
                                                <IconButton
                                                    aria-label="delete"
                                                    size="small"
                                                    onClick={(e) => handleReject(e, content.id)}
                                                    sx={{
                                                        position: 'absolute',
                                                        top: 4,
                                                        right: 4,
                                                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                                        '&:hover': {
                                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                                        },
                                                        color: theme.palette.error.main
                                                    }}
                                                >
                                                    <XCircle size={18}/>
                                                </IconButton>
                                            </ProductCard>
                                        </Grid>
                                    ))}
                                </Grid>

                                {totalCount === 0 && !loading && (
                                    <Box sx={{py: 4, textAlign: 'center'}}>
                                        <Typography color="text.secondary">未找到任何内容</Typography>
                                    </Box>
                                )}

                                {totalPages > 1 && (
                                    <Box sx={{mt: 4, display: 'flex', justifyContent: 'center'}}>
                                        <Pagination
                                            count={totalPages}
                                            page={page}
                                            onChange={handlePageChange}
                                            color="primary"
                                            size="medium"
                                        />
                                    </Box>
                                )}
                            </>
                        )}
                    </Box>
                </Paper>
            </Box>

            <Dialog
                open={openConfirmDialog}
                onClose={handleCloseConfirmDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{"确认删除"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        确定要删除这个生成内容吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseConfirmDialog} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleConfirmDelete} color="error" autoFocus>
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
} 