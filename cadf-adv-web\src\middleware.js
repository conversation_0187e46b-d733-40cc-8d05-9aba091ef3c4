import {NextResponse} from 'next/server';
import {LOGIN_PATH} from "@/config";

export function middleware(request) {
    const token = request.cookies.get('access_token')?.value;

    if (request.nextUrl.pathname.startsWith('/protected') && !token) {
        return NextResponse.redirect(new URL(LOGIN_PATH, request.url));
    }

    return NextResponse.next();
}

export const config = {
    matcher: ['/protected/:path*'],
};
