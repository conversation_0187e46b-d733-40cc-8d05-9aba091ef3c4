"use client";

import DeleteIcon from "@mui/icons-material/Delete";
import RefreshIcon from "@mui/icons-material/Refresh";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import InfoIcon from "@mui/icons-material/Info";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import {Box, Button, Chip, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, IconButton, InputAdornment, LinearProgress, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Typography} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {useCallback, useEffect, useState} from "react";
import {aiGenerationTaskApi} from "@/api/ai-generation-task-api";

export default function TaskList() {
    const theme = useTheme();
    const [searchQuery, setSearchQuery] = useState("");
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;
    const [tasks, setTasks] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [taskToDeleteId, setTaskToDeleteId] = useState(null);
    const [statusDialogOpen, setStatusDialogOpen] = useState(false);
    const [statusDialogData, setStatusDialogData] = useState({ type: "", task: null });

    const router = useRouter();

    const fetchTasks = useCallback(async (currentSearchQuery) => {
        setIsLoading(true);
        setError(null);

        const status = "all";

        try {
            const response = await aiGenerationTaskApi.query_all(
                status,
                currentSearchQuery,
                page - 1,
                rowsPerPage
            );

            if (response && Array.isArray(response.items)) {

                const mappedTasks = response.items.map(item => ({
                    id: item.id_,
                    name: item.task_name,
                    product: {id: item.product_id},
                    material_count: Array.isArray(item.material_ids) ? item.material_ids.length : 0,
                    create_time: item.create_at ? new Date(item.create_at * 1000) : null,
                    total_generated_count: item.total_generated_count,
                    image_completed_count: item.image_completed_count,
                    text_completed_count: item.text_completed_count,
                }));
                setTasks(mappedTasks);
                setTotalCount(response.total || 0);
            } else {
                setTasks([]);
                setTotalCount(0);
                setError("获取任务列表失败或数据格式不正确");
                console.error("Failed to fetch tasks or invalid format:", response);
            }
        } catch (err) {
            setTasks([]);
            setTotalCount(0);
            setError("获取任务列表时发生错误");
            console.error("Error fetching tasks:", err);
        } finally {
            setIsLoading(false);
        }
    }, [page, rowsPerPage]);

    useEffect(() => {
        fetchTasks(searchQuery);
    }, [fetchTasks]);

    const handleDelete = useCallback(async (taskId) => {
        setTaskToDeleteId(taskId);
        setIsDeleteDialogOpen(true);
    }, []);

    const confirmDeleteHandler = useCallback(async () => {
        if (!taskToDeleteId) return;

        try {
            const response = await aiGenerationTaskApi.delete(taskToDeleteId);

            fetchTasks(searchQuery);

            handleCloseDeleteDialog();
        } catch (err) {
            setError(`删除任务时发生错误: ${err.message || '未知错误'}`);
            console.error("Error deleting task:", err);

            handleCloseDeleteDialog();
        }
    }, [taskToDeleteId, fetchTasks, searchQuery]);

    const handleCloseDeleteDialog = () => {
        setIsDeleteDialogOpen(false);
        setTaskToDeleteId(null);
        fetchTasks(searchQuery);
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handleSearchSubmit = useCallback(() => {
        setPage(1);
        fetchTasks(searchQuery);
    }, [fetchTasks, searchQuery]);

    const handleRefresh = useCallback(() => {
        fetchTasks(searchQuery);
    }, [fetchTasks, searchQuery]);

    const openStatusDialog = (type, task) => {
        setStatusDialogData({ type, task });
        setStatusDialogOpen(true);
    };

    const closeStatusDialog = () => {
        setStatusDialogOpen(false);
    };

    const getStatusLabel = (task, type) => {
        const completedCount = type === "image" ? task.image_completed_count : task.text_completed_count;
        const totalCount = task.total_generated_count ?? 0;
        
        const isComplete = (completedCount ?? 0) >= totalCount && totalCount > 0;
        return isComplete ? "生成完毕" : "生成中...";
    };

    const getStatusColor = (task, type) => {
        const completedCount = type === "image" ? task.image_completed_count : task.text_completed_count;
        const totalCount = task.total_generated_count ?? 0;
        
        const isComplete = (completedCount ?? 0) >= totalCount && totalCount > 0;
        return isComplete ? "success" : "warning";
    };

    const getEstimatedTime = (task, type) => {
        const completedCount = type === "image" ? task.image_completed_count : task.text_completed_count;
        const totalCount = task.total_generated_count ?? 0;
        const remaining = totalCount - (completedCount ?? 0);
        
        if (remaining <= 0 || totalCount <= 0) return "";
        
        const timePerItem = type === "image" ? 5 : 1.5;
        return (remaining * timePerItem).toFixed(1);
    };

    const isTaskInProgress = (task) => {
        const totalCount = task.total_generated_count ?? 0;
        const imageCompleted = task.image_completed_count ?? 0;
        const textCompleted = task.text_completed_count ?? 0;
        
        return totalCount > 0 && (imageCompleted < totalCount || textCompleted < totalCount);
    };

    const getTaskOverallStatus = (task) => {
        if (!task) return { label: "-", color: "default" };
        
        const totalCount = task.total_generated_count ?? 0;
        const imageCompleted = task.image_completed_count ?? 0;
        const textCompleted = task.text_completed_count ?? 0;
        
        // 如果总数为0，任务还未开始
        if (totalCount === 0) return { label: "-", color: "default" };
        
        // 如果图片和文本都完成了，任务完成
        if (imageCompleted >= totalCount && textCompleted >= totalCount) {
            return { label: "生成完毕", color: "success" };
        }
        
        // 否则任务还在进行中
        return { label: "生成中...", color: "warning" };
    };

    const startIndex = (page - 1) * rowsPerPage + 1;
    const endIndex = Math.min(page * rowsPerPage, totalCount);

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{mb: 4}}
            >
                <Typography variant="h4" fontWeight="bold">
                    任务列表
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    onClick={() =>
                        router.push("/protected/ai-image-text-generation/create")
                    }
                    sx={{
                        px: 4,
                        borderRadius: 2,
                        textTransform: "none",
                        fontWeight: "medium",
                    }}
                >
                    生成AI图文
                </Button>
            </Stack>

            <Paper
                elevation={0}
                sx={{
                    p: 0,
                    mb: 4,
                    overflow: "hidden",
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                }}
            >
                <Box
                    sx={{
                        p: 2,
                        bgcolor: theme.palette.primary.light,
                        color: theme.palette.primary.contrastText,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                    }}
                >
                    <Typography variant="subtitle1" fontWeight="medium">
                        任务列表
                    </Typography>
                    <Stack direction="row" spacing={1} alignItems="center">
                        <TextField
                            placeholder="搜索任务名称"
                            variant="outlined"
                            size="small"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            sx={{
                                width: 240,
                                bgcolor: "background.paper",
                                borderRadius: 1,
                                "& .MuiOutlinedInput-root": {
                                    borderRadius: 1,
                                },
                            }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon fontSize="small"/>
                                    </InputAdornment>
                                ),
                            }}
                            onKeyDown={(event) => {
                                if (event.key === 'Enter') {
                                    handleSearchSubmit();
                                }
                            }}
                        />
                        <IconButton
                            onClick={handleSearchSubmit}
                            size="small"
                            sx={{bgcolor: "background.paper", borderRadius: 1}}
                        >
                            <SearchIcon fontSize="small"/>
                        </IconButton>
                        <IconButton
                            onClick={handleRefresh}
                            size="small"
                            sx={{bgcolor: "background.paper", borderRadius: 1}}
                        >
                            <RefreshIcon fontSize="small"/>
                        </IconButton>
                    </Stack>
                </Box>

                {isLoading ? (
                    <Box sx={{p: 4, textAlign: "center"}}>
                        <Typography color="text.secondary">加载中...</Typography>
                    </Box>
                ) : error ? (
                    <Box sx={{p: 4, textAlign: "center"}}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : totalCount === 0 ? (
                    <Box sx={{p: 4, textAlign: "center"}}>
                        <Typography color="text.secondary">
                            暂无任务记录
                        </Typography>
                    </Box>
                ) : (
                    <Box sx={{p: 3}}>
                        <TableContainer>
                            <Table sx={{minWidth: 650}}>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{maxWidth: 200}}>任务名称</TableCell>
                                        {/* <TableCell>素材个数</TableCell> */}
                                        <TableCell>生成状态</TableCell>
                                        <TableCell>创建时间</TableCell>
                                        <TableCell align="right">操作</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {tasks.map((task) => (
                                        <TableRow key={task.id} hover>
                                            <TableCell sx={{maxWidth: 200}}>
                                                <Typography
                                                    variant="body2"
                                                    title={task.name}
                                                    sx={{
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                    }}
                                                >
                                                    {task.name}
                                                </Typography>
                                            </TableCell>
                                            {/* <TableCell>
                                                <Typography variant="body2">
                                                    {task.material_count || 0}
                                                </Typography>
                                            </TableCell> */}
                                            <TableCell sx={{minWidth: 120}}>
                                                {(() => {
                                                    const status = getTaskOverallStatus(task);
                                                    return (
                                                        <Box 
                                                            onClick={() => openStatusDialog("combined", task)} 
                                                            sx={{
                                                                display: 'inline-flex',
                                                                alignItems: 'center',
                                                                borderRadius: '16px',
                                                                bgcolor: status.color === 'success' 
                                                                    ? 'rgba(84, 214, 44, 0.16)' 
                                                                    : status.color === 'warning' 
                                                                        ? 'rgba(255, 193, 7, 0.16)' 
                                                                        : 'rgba(145, 158, 171, 0.16)',
                                                                px: 1,
                                                                py: 0.5,
                                                                cursor: 'pointer',
                                                                '&:hover': {
                                                                    boxShadow: '0 0 0 1px rgba(0,0,0,0.05)',
                                                                    opacity: 0.9
                                                                }
                                                            }}
                                                        >
                                                            <Box
                                                                sx={{
                                                                    width: 8,
                                                                    height: 8,
                                                                    borderRadius: '50%',
                                                                    mr: 0.8,
                                                                    bgcolor: status.color === 'success' 
                                                                        ? '#54d628' 
                                                                        : status.color === 'warning' 
                                                                            ? '#ffab00' 
                                                                            : '#637381',
                                                                }}
                                                            />
                                                            <Typography 
                                                                variant="caption" 
                                                                sx={{ 
                                                                    fontWeight: 'bold',
                                                                    color: status.color === 'success' 
                                                                        ? '#229A16' 
                                                                        : status.color === 'warning' 
                                                                            ? '#B76E00' 
                                                                            : '#212B36',
                                                                }}
                                                            >
                                                                {status.label}
                                                            </Typography>
                                                            <MoreHorizIcon 
                                                                sx={{ 
                                                                    ml: 0.5, 
                                                                    fontSize: '0.875rem',
                                                                    opacity: 0.5,
                                                                    color: status.color === 'success' 
                                                                        ? '#229A16' 
                                                                        : status.color === 'warning' 
                                                                            ? '#B76E00' 
                                                                            : '#212B36',
                                                                }} 
                                                            />
                                                        </Box>
                                                    );
                                                })()}
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2">
                                                    {task.create_time ? task.create_time.toLocaleString() : "-"}
                                                </Typography>
                                            </TableCell>
                                            <TableCell align="right">
                                                <Stack direction="row" spacing={1} justifyContent="flex-end">
                                                    <IconButton
                                                        size="small"
                                                        color="primary"
                                                        onClick={() =>
                                                            router.push(`/protected/ai-image-text-generation/${task.id}`)
                                                        }
                                                    >
                                                        <VisibilityIcon fontSize="small"/>
                                                    </IconButton>
                                                    <IconButton
                                                        size="small"
                                                        color="error"
                                                        onClick={() => handleDelete(task.id)}
                                                    >
                                                        <DeleteIcon fontSize="small"/>
                                                    </IconButton>
                                                </Stack>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>

                        {totalCount > rowsPerPage && (
                            <Box sx={{display: 'flex', justifyContent: 'center', mt: 3}}>
                                <Pagination
                                    count={Math.ceil(totalCount / rowsPerPage)}
                                    page={page}
                                    onChange={handlePageChange}
                                    color="primary"
                                    showFirstButton
                                    showLastButton
                                />
                            </Box>
                        )}
                    </Box>
                )}
            </Paper>

            <Box sx={{display: "flex", justifyContent: "flex-end"}}>
                <Typography variant="body2" color="text.secondary">
                    显示 {totalCount > 0 ? startIndex : 0}-
                    {endIndex}/{totalCount} 个任务
                </Typography>
            </Box>

            <Dialog
                open={isDeleteDialogOpen}
                onClose={handleCloseDeleteDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{"确认删除"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        确定要删除这个任务吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDeleteDialog}>取消</Button>
                    <Button onClick={confirmDeleteHandler} color="error" autoFocus>
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog
                open={statusDialogOpen}
                onClose={closeStatusDialog}
                aria-labelledby="status-dialog-title"
                aria-describedby="status-dialog-description"
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    elevation: 0,
                    sx: {
                        borderRadius: 2,
                        boxShadow: '0px 10px 35px rgba(0, 0, 0, 0.1)',
                        overflow: 'visible',
                        p: 0,
                    }
                }}
            >
                <Box sx={{ 
                    position: 'relative', 
                    bgcolor: theme.palette.primary.light,
                    borderRadius: '8px 8px 0 0',
                    p: 2.5
                }}>
                    <Typography variant="h6" sx={{ color: '#fff', fontWeight: 'bold' }}>
                        生成状态详情
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)', mt: 0.5 }}>
                        {statusDialogData.task?.name}
                    </Typography>
                    <IconButton 
                        onClick={closeStatusDialog}
                        sx={{ 
                            position: 'absolute', 
                            top: 8, 
                            right: 8, 
                            color: 'rgba(255,255,255,0.8)',
                            '&:hover': { color: '#fff', bgcolor: 'rgba(255,255,255,0.15)' } 
                        }}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </IconButton>
                </Box>
                
                <DialogContent sx={{ p: 3, pb: 4 }}>
                    {statusDialogData.task && (
                        <Stack spacing={3.5}>
                            <Box sx={{ 
                                bgcolor: '#F9FAFB', 
                                p: 2, 
                                borderRadius: 2,
                                border: '1px solid rgba(145, 158, 171, 0.12)'
                            }}>
                                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1.5 }}>
                                    <Box
                                        sx={{
                                            width: 10,
                                            height: 10,
                                            borderRadius: '50%',
                                            bgcolor: '#36B37E'
                                        }}
                                    />
                                    <Typography variant="subtitle1" fontWeight="bold">
                                        图片生成状态
                                    </Typography>
                                </Stack>

                                <Box sx={{ mb: 2 }}>
                                    <Stack direction="row" justifyContent="space-between" sx={{ mb: 0.5 }}>
                                        <Typography variant="body2" color="text.secondary">
                                            完成进度
                                        </Typography>
                                        <Typography variant="body2" fontWeight="medium">
                                            {statusDialogData.task.image_completed_count ?? 0}/{statusDialogData.task.total_generated_count ?? 0} 项
                                        </Typography>
                                    </Stack>
                                    <LinearProgress
                                        variant="determinate"
                                        value={
                                            (statusDialogData.task.total_generated_count ?? 0) > 0
                                                ? ((statusDialogData.task.image_completed_count ?? 0) / 
                                                    statusDialogData.task.total_generated_count) * 100
                                                : 0
                                        }
                                        sx={{ 
                                            height: 8, 
                                            borderRadius: 4,
                                            bgcolor: 'rgba(54, 179, 126, 0.16)',
                                            '& .MuiLinearProgress-bar': {
                                                bgcolor: '#36B37E'
                                            }
                                        }}
                                    />
                                </Box>

                                <Stack spacing={1}>
                                    <Stack direction="row" justifyContent="space-between">
                                        <Typography variant="body2" color="text.secondary">
                                            当前状态
                                        </Typography>
                                        <Box sx={{
                                            px: 1.5,
                                            py: 0.25,
                                            borderRadius: '12px',
                                            bgcolor: (statusDialogData.task.image_completed_count ?? 0) >= (statusDialogData.task.total_generated_count ?? 0) && (statusDialogData.task.total_generated_count ?? 0) > 0
                                                ? 'rgba(84, 214, 44, 0.16)'
                                                : 'rgba(255, 193, 7, 0.16)',
                                            display: 'inline-flex',
                                            alignItems: 'center'
                                        }}>
                                            <Typography variant="caption" fontWeight="bold" sx={{
                                                color: (statusDialogData.task.image_completed_count ?? 0) >= (statusDialogData.task.total_generated_count ?? 0) && (statusDialogData.task.total_generated_count ?? 0) > 0
                                                    ? '#229A16'
                                                    : '#B76E00'
                                            }}>
                                                {getStatusLabel(statusDialogData.task, "image")}
                                            </Typography>
                                        </Box>
                                    </Stack>

                                    {getEstimatedTime(statusDialogData.task, "image") && (
                                        <Stack direction="row" justifyContent="space-between">
                                            <Typography variant="body2" color="text.secondary">
                                                预计剩余时间
                                            </Typography>
                                            <Typography variant="body2" fontWeight="medium">
                                                约 {getEstimatedTime(statusDialogData.task, "image")} 分钟
                                            </Typography>
                                        </Stack>
                                    )}
                                </Stack>
                            </Box>

                            <Box sx={{ 
                                bgcolor: '#F9FAFB', 
                                p: 2, 
                                borderRadius: 2,
                                border: '1px solid rgba(145, 158, 171, 0.12)'
                            }}>
                                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1.5 }}>
                                    <Box
                                        sx={{
                                            width: 10,
                                            height: 10,
                                            borderRadius: '50%',
                                            bgcolor: '#0052CC'
                                        }}
                                    />
                                    <Typography variant="subtitle1" fontWeight="bold">
                                        文本生成状态
                                    </Typography>
                                </Stack>

                                <Box sx={{ mb: 2 }}>
                                    <Stack direction="row" justifyContent="space-between" sx={{ mb: 0.5 }}>
                                        <Typography variant="body2" color="text.secondary">
                                            完成进度
                                        </Typography>
                                        <Typography variant="body2" fontWeight="medium">
                                            {statusDialogData.task.text_completed_count ?? 0}/{statusDialogData.task.total_generated_count ?? 0} 项
                                        </Typography>
                                    </Stack>
                                    <LinearProgress
                                        variant="determinate"
                                        value={
                                            (statusDialogData.task.total_generated_count ?? 0) > 0
                                                ? ((statusDialogData.task.text_completed_count ?? 0) / 
                                                    statusDialogData.task.total_generated_count) * 100
                                                : 0
                                        }
                                        sx={{ 
                                            height: 8, 
                                            borderRadius: 4,
                                            bgcolor: 'rgba(0, 82, 204, 0.16)',
                                            '& .MuiLinearProgress-bar': {
                                                bgcolor: '#0052CC'
                                            }
                                        }}
                                    />
                                </Box>

                                <Stack spacing={1}>
                                    <Stack direction="row" justifyContent="space-between">
                                        <Typography variant="body2" color="text.secondary">
                                            当前状态
                                        </Typography>
                                        <Box sx={{
                                            px: 1.5,
                                            py: 0.25,
                                            borderRadius: '12px',
                                            bgcolor: (statusDialogData.task.text_completed_count ?? 0) >= (statusDialogData.task.total_generated_count ?? 0) && (statusDialogData.task.total_generated_count ?? 0) > 0
                                                ? 'rgba(84, 214, 44, 0.16)'
                                                : 'rgba(255, 193, 7, 0.16)',
                                            display: 'inline-flex',
                                            alignItems: 'center'
                                        }}>
                                            <Typography variant="caption" fontWeight="bold" sx={{
                                                color: (statusDialogData.task.text_completed_count ?? 0) >= (statusDialogData.task.total_generated_count ?? 0) && (statusDialogData.task.total_generated_count ?? 0) > 0
                                                    ? '#229A16'
                                                    : '#B76E00'
                                            }}>
                                                {getStatusLabel(statusDialogData.task, "text")}
                                            </Typography>
                                        </Box>
                                    </Stack>

                                    {getEstimatedTime(statusDialogData.task, "text") && (
                                        <Stack direction="row" justifyContent="space-between">
                                            <Typography variant="body2" color="text.secondary">
                                                预计剩余时间
                                            </Typography>
                                            <Typography variant="body2" fontWeight="medium">
                                                约 {getEstimatedTime(statusDialogData.task, "text")} 分钟
                                            </Typography>
                                        </Stack>
                                    )}
                                </Stack>
                            </Box>

                            <Stack direction="row" justifyContent="space-between" sx={{ pt: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                    创建时间
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {statusDialogData.task.create_time ? statusDialogData.task.create_time.toLocaleString() : "-"}
                                </Typography>
                            </Stack>
                        </Stack>
                    )}
                </DialogContent>
            </Dialog>
        </Container>
    );
}
