import api from "@/core/api/api";

export const userApi = {
    login: async (username, password) => {
        return await api({
            resource: "user",
            method_name: "login",
            username,
            password,
        });
    },

    delete: async (id_) => {
        return await api({
            resource: "user",
            method_name: "delete",
            id_,
        });
    },

    query_one: async (user_id) => {
        return await api({
            resource: "user",
            method_name: "query_one",
            user_id,
        });
    },

    query_all: async (filter = {}) => {
        return await api({
            resource: "user",
            method_name: "query_all",
            ...filter,
        });
    },
};