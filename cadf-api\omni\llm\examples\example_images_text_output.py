import base64
import os

import httpx
from langchain_core.messages import HumanMessage

from omni.llm.chat.chat_model_factory import ChatModelFactory

PROMPT = """
# 角色
你是一名专业的图片识别大师。

# 背景
你收到了多张图片。

# 任务
根据输入的多张图片内容，判断图片哪些含有产品,哪些不含产品,并且生成图片本身的描述,且告诉我图片上文字的位置在哪,文字的位置描述是基于图片本身,能让ffmpeg快速定位的位置

"""


def product_description_image_recognition_agent(image_paths: list[str]) -> str:
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")

    image_data = []
    for path in image_paths:
        if not os.path.exists(path):
            print(f"Warning: Image file not found at {path}")
            continue
        try:
            with open(path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                image_data.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{encoded_string}"
                        }
                    }
                )
        except Exception as e:
            print(f"Error processing image file {path}: {e}")
            continue


    human_message_content = [
        {"type": "text", "text": PROMPT},
        *image_data,
    ]

    messages = [HumanMessage(content=human_message_content)]

    chain = llm

    result = chain.invoke(messages)
    print(result.content)
    return result.content

if __name__ == "__main__":
    # 请将这里的路径替换为你的实际图片文件路径
    sample_image_paths = [
        "images/1.jpg",
        "images/2.jpg",
        "images/3.jpg",
        "images/4.jpg",
        "images/5.jpg",
        "images/6.jpg",
        "images/7.jpg",
    ]
    print(f"开始处理图片: {sample_image_paths}")
    description = product_description_image_recognition_agent(sample_image_paths)
    print(description)
