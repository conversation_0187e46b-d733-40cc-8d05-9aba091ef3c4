"use client";

import {useState, useEffect} from 'react';
import {Box, Button, Card, CardContent, Chip, IconButton, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme, CircularProgress, Alert } from '@mui/material';
import {ArrowLeft, Search, UserCheck, DollarSign, Calendar} from 'lucide-react';
import {useRouter} from 'next/navigation';
import { crewManagementApi } from '@/api/crew-management-api';

export default function ActiveCrewMembers() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    
    const [crewMembers, setCrewMembers] = useState([]);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const rowsPerPage = 10;

    useEffect(() => {
        const fetchActiveCrew = async () => {
            setLoading(true);
            setError(null);
            try {
                const response = await crewManagementApi.getActiveCrewList(page, rowsPerPage);
                if (response && response.crew) {
                    setCrewMembers(response.crew);
                    setTotalCount(response.total_count || 0);
                } else {
                    throw new Error('获取活跃舰员数据失败');
                }
            } catch (err) {
                setError(err.message || '加载数据时发生错误');
                setCrewMembers([]);
                setTotalCount(0);
            } finally {
                setLoading(false);
            }
        };

        fetchActiveCrew();
    }, [page]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton 
                    onClick={() => router.back()}
                    sx={{mr: 1}}
                    color="primary"
                >
                    <ArrowLeft />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{
                    fontWeight: 500, 
                    fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}
                }}>
                    活跃舰员 ({totalCount})
                </Typography>
            </Box>

            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        查看所有活跃舰员列表。活跃舰员是指在最近两天内有成功发布任务记录的舰员。
                    </Typography>

                    {loading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                            <CircularProgress />
                        </Box>
                    )}

                    {error && (
                        <Alert severity="error" sx={{ my: 2 }}>{error}</Alert>
                    )}

                    {!loading && !error && (
                        <>
                        {isMobile ? (
                            <Box>
                                {crewMembers.length > 0 ? (
                                    crewMembers.map((member, index) => (
                                        <Card key={member.id_} sx={{
                                            mb: 2.5,
                                            borderRadius: 2,
                                            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                                            transition: 'transform 0.2s, box-shadow 0.2s',
                                            '&:hover': {
                                                transform: 'translateY(-2px)',
                                                boxShadow: '0 6px 16px rgba(0,0,0,0.12)',
                                            }
                                        }}>
                                            <CardContent sx={{p: 0}}>
                                                <Box sx={{
                                                    p: 2,
                                                    background: 'linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%)',
                                                    borderBottom: '1px solid rgba(0,0,0,0.06)',
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center'
                                                }}>
                                                    <Typography variant="h6" sx={{fontWeight: 600}}>
                                                        {member.user_account}
                                                    </Typography>
                                                    <Chip
                                                        label="活跃"
                                                        color="success"
                                                        size="small"
                                                        sx={{fontWeight: 500}}
                                                    />
                                                </Box>
                                                
                                                <Box sx={{p: 2}}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        mb: 2,
                                                        pb: 1.5,
                                                        borderBottom: '1px dashed rgba(0,0,0,0.1)'
                                                    }}>
                                                        <Box sx={{
                                                            width: 40,
                                                            height: 40,
                                                            borderRadius: '50%',
                                                            background: 'linear-gradient(45deg, #3f51b5, #2196f3)',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            mr: 2
                                                        }}>
                                                            <UserCheck size={20} color="#fff" />
                                                        </Box>
                                                        <Box>
                                                            <Typography variant="body2" color="text.secondary" sx={{fontSize: '0.775rem'}}>
                                                                平台账号
                                                            </Typography>
                                                            <Typography variant="body1" sx={{fontWeight: 500}}>
                                                                {member.platform_account || '未设置'}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'flex-start',
                                                        mt: 2
                                                    }}>
                                                    </Box>
                                                </Box>
                                            </CardContent>
                                        </Card>
                                    ))
                                ) : (
                                    <Card variant="outlined" sx={{p: 3, textAlign: 'center', borderRadius: 2, bgcolor: 'background.paper'}}>
                                        <Typography>没有找到活跃舰员</Typography>
                                    </Card>
                                )}
                            </Box>
                        ) : (
                            <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>用户账号</TableCell>
                                            <TableCell>平台账号</TableCell>
                                            <TableCell>状态</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {crewMembers.length > 0 ? (
                                            crewMembers.map((member, index) => (
                                                <TableRow key={member.id_} hover>
                                                    <TableCell>{member.user_account}</TableCell>
                                                    <TableCell>{member.platform_account || '未设置'}</TableCell>
                                                    <TableCell>
                                                        <Chip
                                                            label="活跃"
                                                            color="success"
                                                            size="small"
                                                        />
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={3} align="center">
                                                    没有找到活跃舰员
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        )}
                        </>
                    )}

                    {totalCount > rowsPerPage && (
                        <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                            <Pagination
                                count={Math.ceil(totalCount / rowsPerPage)}
                                page={page}
                                onChange={handlePageChange}
                                color="primary"
                                size={isMobile ? "small" : "medium"}
                            />
                        </Stack>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
} 