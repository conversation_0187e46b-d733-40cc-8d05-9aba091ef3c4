import traceback

from omni.log.log import olog
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import (
    PromotionTaskDetail,
    AccountMetrics,
    AiGeneratedMaterial,
    Account,
)


@register_scheduler(trigger='cron', hour='1-23/2', minute='0')  # 每奇数小时整点执行一次
# @register_scheduler(trigger="interval", seconds=10)  # 每10秒执行一次
class TaskMetricsScheduler(BaseScheduler):
    def run_task(self):
        """
        **目的**: 定期更新推广任务详情 (`PromotionTaskDetail`) 的各项指标（浏览量、点赞数等）。

        **算法描述**:
            - **初始化**: 记录任务开始日志，初始化更新计数器和跳过计数器。
            - **查询待更新任务**: 从 `PromotionTaskDetail` 集合中查找所有 `validation_status` 为 "成功" 且已分配 `account_id` 的记录。
            - **遍历任务详情**: 对查询到的每个 `PromotionTaskDetail` 记录执行以下操作：
                - **获取关联素材**: 使用 `detail.ai_generated_material_id` 查询 `AiGeneratedMaterial` 以获取笔记标题 (`title`)。如果找不到，记录警告，跳过当前任务。
                - **获取关联账号**: 使用 `detail.account_id` 查询 `Account` 以获取平台信息 (`platform`)。如果找不到，记录警告，跳过当前任务。
                - **查询最新指标**: 使用从 `Account` 获取的 `platform`、从 `AiGeneratedMaterial` 获取的 `title` 以及 `detail.account_id`，在 `AccountMetrics` 集合中查找匹配的记录。查询结果按 `crawled_at` 字段降序排序，并获取最新的一条记录。
                - **更新或跳过**:
                    - **如果找到匹配的指标记录**: 将 `AccountMetrics` 中的指标 (`view_count`, `like_count` 等，空值则用 0 替代) 更新到当前的 `PromotionTaskDetail` 记录中。记录更新日志，增加更新计数器。
                    - **如果未找到匹配的指标记录**: 记录警告日志，说明未找到匹配的指标，增加跳过计数器。
                - **单任务异常处理**: 捕获处理单个 `PromotionTaskDetail` 时可能发生的任何异常，记录错误日志，增加跳过计数器。
            - **任务总结**: 记录任务完成日志，包括成功更新的记录数和跳过/失败的记录数。
            - **全局异常处理**: 捕获整个任务执行过程中可能发生的任何异常，记录错误日志。
        """
        olog.info("开始执行推广任务指标更新任务...")
        try:

            details_to_update = PromotionTaskDetail.objects(
                validation_status="成功", account_id__ne=None
            )

            updated_count = 0
            skipped_count = 0

            for detail in details_to_update:
                try:
                    material = AiGeneratedMaterial.objects(
                        id=detail.ai_generated_material_id
                    ).first()
                    if not material:
                        olog.warning(
                            f"找不到 PromotionTaskDetail (ID: {detail.id}) 关联的 AiGeneratedMaterial (ID: {detail.ai_generated_material_id})"
                        )
                        skipped_count += 1
                        continue

                    account = Account.objects(id=detail.account_id).first()
                    if not account:
                        olog.warning(
                            f"找不到 PromotionTaskDetail (ID: {detail.id}) 关联的 Account (ID: {detail.account_id})"
                        )
                        skipped_count += 1
                        continue

                    latest_metrics = (
                        AccountMetrics.objects(
                            account_id=str(detail.account_id),
                            platform=account.platform,
                            title=material.title,
                        )
                        .order_by("-crawled_at")
                        .first()
                    )

                    if latest_metrics:

                        update_fields = {
                            "set__view_count": latest_metrics.view_count or 0,
                            "set__like_count": latest_metrics.like_count or 0,
                            "set__comment_count": latest_metrics.comment_count or 0,
                            "set__favorite_count": latest_metrics.favorite_count or 0,
                            "set__share_count": latest_metrics.share_count or 0,
                        }

                        detail.modify(**update_fields)
                        olog.info(
                            f"已更新 PromotionTaskDetail (ID: {detail.id}) 指标: {update_fields}"
                        )
                        updated_count += 1
                    else:
                        olog.warning(
                            f"找不到与 PromotionTaskDetail (ID: {detail.id}, AccountID: {detail.account_id}, Title: {material.title}) 匹配的 AccountMetrics 记录"
                        )
                        skipped_count += 1

                except Exception as e:
                    olog.error(
                        f"处理 PromotionTaskDetail (ID: {detail.id}) 时出错: {e} {traceback.format_exc()}"
                    )
                    skipped_count += 1

            olog.info(
                f"推广任务指标更新任务完成。更新: {updated_count}, 跳过/失败: {skipped_count}"
            )

        except Exception as e:
            olog.error(f"执行 TaskMetricsScheduler 失败: {e} {traceback.format_exc()} ")
