# 开发规范

## 风格约束

页面风格采用智能极简主义设计

不使用动态样式

请使用 MUI 的 theme.js 中预定义的主题颜色，以确保整个应用的色彩一致性

请使用 light 主题

## 技术约束

当前项目使用了 MUI(v6.1.10)与 Nextjs(v15.0.3)

默认约定使用客户端渲染，需要在文件的开头添加 `"use client"` 指令

路径引入使用@方式,@的根路径是src

请使用 MUI 帮我构建界面

icon 使用 lucide-react

图表使用@mui/x-charts

## Alert 通知使用例子

如何使用 Alert

```js
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType } from "@/core/components/alert";
const dispatch = useDispatch();
dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.CREATE }));
```

Alert 相关定义

```js
export const AlertType = {
  SUCCESS: "success",
  INFO: "info",
  WARNING: "warning",
  ERROR: "error",
};

export const AlertMsg = {
  CREATE: "创建成功",
  MODIFY: "修改成功",
  DELETE: "删除成功",
};
```
