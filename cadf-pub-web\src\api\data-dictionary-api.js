import api from "@/core/api/api";

export const dataDictionaryApi = {
    /**
     * 查询数据字典条目
     * @param {string} category - 要查询的类别 (例如 'domain')
     * @returns {Promise<object>} 查询结果，包含 dictionaries (key, value 列表) 和 total_count
     */
    queryAll: async (category) => {
        const data = {};
        if (category) {
            data.category = category;
        }
        return await api({
            resource: "data_dictionary",
            method_name: "query_all",
            ...data
        });
    },
}; 