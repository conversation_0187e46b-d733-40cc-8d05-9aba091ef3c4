import json
from typing import Any, Dict

from langchain.schema.messages import AIMessageChunk


class GraphMessageHandler:
    """统一消息处理器"""

    def _graph_ai_message_parse(self, message: Any) -> str:
        """
        解析 AI 消息块的内容，提取其中的文本信息。
        
        该函数用于处理来自 LangChain 的 AI 消息块，将其转换为纯文本格式。
        如果输入不是有效的消息格式，则返回空字符串。

        Args:
            message: 包含 AIMessageChunk 和元数据的列表或元组

        Returns:
            str: 解析后的文本内容
        """
        # 检查 message 是否为列表或元组，且长度至少为 2
        if not isinstance(message, (list, tuple)) or len(message) < 2:
            return [], ""

        # 检查第一个元素是否为 AIMessageChunk
        if not isinstance(message[0], AIMessageChunk):
            return [], ""

        # 检查第二个元素是否为字典，并安全地获取 tags
        if not isinstance(message[1], dict):
            return [], ""

        content = message[0].content

        tags = message[1].get('tags', [])

        return tags, content

    def handle_message(self, stream_mode: str, chunk: Any) -> Dict[str, Any]:
        """
        处理消息流中的数据

        Args:
            stream_mode: 消息流模式，如 'messages' 或 'values'
            chunk: 消息数据块

        Returns:
            Dict[str, Any]: 包含处理后的消息信息的字典
        """
        if stream_mode == "messages":
            # 处理普通AI消息
            tags, ai_message = self._graph_ai_message_parse(chunk)
            if ai_message:
                return {
                    "stream_mode": stream_mode,
                    "message": ai_message,
                    "tags": tags
                }

        # 处理values类型消息
        elif stream_mode == "values":
            return {
                "stream_mode": stream_mode,
                "message": json.dumps(chunk, ensure_ascii=False)
            }
