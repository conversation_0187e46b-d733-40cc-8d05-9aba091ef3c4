import base64
import io
import os

import httpx
import pillow_avif
from PIL import Image
from omni.log.log import olog

# 不要删除
pillow_avif.__version__


def url_to_png_base64(image_url: str) -> str:
    """
    下载图片，将其强制转换为PNG格式，并返回Base64编码的字符串。

    :param image_url: 图片的URL
    :return: Base64编码的PNG图片数据
    :raises Exception: 如果下载或处理过程中发生错误
    """
    try:
        response = httpx.get(image_url)
        response.raise_for_status()  # 确保请求成功
        img_data = response.content

        # 从内存中读取图片
        img = Image.open(io.BytesIO(img_data))

        # 转换为PNG格式到内存中
        with io.BytesIO() as output:
            if img.mode == 'RGBA' or 'A' in img.mode:
                img.save(output, format="PNG")
            else:
                img.convert('RGB').save(output, format="PNG")
            png_data = output.getvalue()

        # Base64编码
        base64_encoded_data = base64.b64encode(png_data).decode('utf-8')
        return base64_encoded_data
    except httpx.HTTPStatusError:
        olog.exception(f"请求图片失败 (url_to_png_base64 for {image_url})")
        raise
    except Exception:
        olog.exception(f"处理图片时发生错误 (url_to_png_base64 for {image_url})")
        raise


def base64_to_png_base64(base64_image_data: str) -> str:
    """
    将任意格式的Base64编码的图片数据统一转换为PNG格式，并返回Base64编码的字符串。

    :param base64_image_data: Base64编码的图片数据字符串 (可以是JPEG, WEBP, AVIF等)
    :return: Base64编码的PNG图片数据
    :raises Exception: 如果解码或处理过程中发生错误
    """
    try:
        # Base64解码
        img_data = base64.b64decode(base64_image_data)

        # 从内存中读取图片
        img = Image.open(io.BytesIO(img_data))

        # 转换为PNG格式到内存中
        with io.BytesIO() as output:
            if img.mode == 'RGBA' or 'A' in img.mode:
                img.save(output, format="PNG")
            else:
                img.save(output, format="PNG")
            png_data = output.getvalue()

        # Base64编码
        base64_encoded_data = base64.b64encode(png_data).decode('utf-8')
        return base64_encoded_data
    except Exception:
        olog.exception("处理Base64图片数据时发生错误 (base64_to_png_base64)")
        raise


def bytes_to_png_base64(image_bytes: bytes) -> str:
    """
    将原始图片字节流（可能是任何格式）转换为PNG格式，然后返回Base64编码的字符串。

    :param image_bytes: 图片的原始字节数据
    :return: Base64编码的PNG图片数据
    :raises Exception: 如果处理过程中发生错误
    """
    try:
        img = Image.open(io.BytesIO(image_bytes))
        with io.BytesIO() as output:
            if img.mode == 'RGBA' or 'A' in img.mode:
                img.save(output, format="PNG")
            else:
                img.save(output, format="PNG")
            png_data = output.getvalue()
        return base64.b64encode(png_data).decode('utf-8')
    except Exception:
        olog.exception(f"将字节转换为PNG Base64时发生错误 (bytes_to_png_base64)")
        raise


def local_path_to_png_base64(image_path: str) -> str:
    """
    读取本地图片文件，转换为PNG格式，然后返回Base64编码的字符串。

    :param image_path: 本地图片文件的路径
    :return: Base64编码的PNG图片数据
    :raises Exception: 如果文件读取或处理过程中发生错误
    """
    try:
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件未找到: {image_path}")
        
        img = Image.open(image_path)
        with io.BytesIO() as output:
            if img.mode == 'RGBA' or 'A' in img.mode:
                img.save(output, format="PNG")
            else:
                img.save(output, format="PNG")
            png_data = output.getvalue()
        return base64.b64encode(png_data).decode('utf-8')
    except Exception:
        olog.exception(f"将本地图片转换为PNG Base64时发生错误 (local_path_to_png_base64 for {image_path})")
        raise