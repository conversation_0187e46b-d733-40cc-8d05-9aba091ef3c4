"use client";

import { userApi } from "@/api/user-api";
import {DASHBOARD_PATH, PROJECT_DESCRIPTION, PROJECT_NAME} from "@/config";
import { Lock, User, Eye, EyeOff } from "lucide-react";
import { Alert, Box, Button, Fade, IconButton, InputAdornment, Paper, Stack, TextField, Typography, useMediaQuery, useTheme } from "@mui/material";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function LoginPage() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const [formData, setFormData] = useState({username: "", password: ""});
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState("");

    const handleLogin = async (username, password) => {
        setIsLoading(true);
        try {
            const response = await userApi.login(username, password);
            if (response.access_token) {
                Cookies.set("access_token", response.access_token, {expires: 7});
                router.push(DASHBOARD_PATH);
            } else {
                setError("登录失败，请检查用户名和密码");
            }
        } catch (err) {
            setError("登录失败，请稍后重试");
        } finally {
            setIsLoading(false);
        }
    };

    const defaultLogin = () => {
        handleLogin("admin", "y708sWekpvoRdIpF");
    };


    const fakeLogin = () => {
        const fakeToken = "fake_token_" + Date.now();
        Cookies.set("access_token", fakeToken, {expires: 7});
        router.push(DASHBOARD_PATH);
    };

    
    useEffect(() => {
        // fakeLogin();
        defaultLogin();
    }, []);

    const handleInputChange = (e) => {
        const {name, value} = e.target;
        setFormData(prev => ({...prev, [name]: value}));
        setError("");
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!formData.username || !formData.password) {
            setError("请填写所有必填字段");
            return;
        }
        await handleLogin(formData.username, formData.password);
    };

    return (
        <Box
            component="main"
            sx={{
                display: "flex",
                minHeight: "100vh",
                backgroundColor: theme.palette.background.default,
                backgroundImage: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.grey[100]} 100%)`,
                overflow: "hidden",
                px: { xs: 2, sm: 3, md: 0 }
            }}
        >
            {/* 左侧装饰区域 - 仅在非移动设备上显示 */}
            <Box
                sx={{
                    display: {xs: "none", md: "flex"},
                    width: "50%",
                    backgroundColor: theme.palette.primary.main,
                    backgroundImage: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    justifyContent: "center",
                    alignItems: "center",
                    position: "relative",
                    overflow: "hidden",
                }}
            >
                <Box
                    sx={{
                        position: "relative",
                        zIndex: 2,
                        p: 6,
                        color: theme.palette.primary.contrastText,
                        textAlign: "center",
                        maxWidth: "600px",
                    }}
                >
                    <Typography
                        variant="h2"
                        component="h1"
                        sx={{
                            fontWeight: 700,
                            mb: 3,
                            letterSpacing: "-0.5px",
                            textShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
                            fontSize: {xs: "2.5rem", sm: "3rem", md: "3.5rem"}
                        }}
                    >
                        {PROJECT_NAME}
                    </Typography>
                    <Typography
                        variant="h5"
                        sx={{
                            fontWeight: 500,
                            opacity: 0.9,
                            mb: 6,
                            letterSpacing: "0.5px",
                            position: "relative",
                            "&::after": {
                                content: '""',
                                position: "absolute",
                                bottom: "-20px",
                                left: "50%",
                                transform: "translateX(-50%)",
                                width: "80px",
                                height: "4px",
                                borderRadius: "2px",
                                backgroundColor: theme.palette.primary.light,
                                opacity: 0.7
                            }
                        }}
                    >
                        {PROJECT_DESCRIPTION}
                    </Typography>
                </Box>

                {/* 装饰性图形元素 */}
                <Box
                    sx={{
                        position: "absolute",
                        bottom: "-10%",
                        right: "-5%",
                        width: "300px",
                        height: "300px",
                        borderRadius: "50%",
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                    }}
                />
                <Box
                    sx={{
                        position: "absolute",
                        top: "10%",
                        left: "-5%",
                        width: "200px",
                        height: "200px",
                        borderRadius: "50%",
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                    }}
                />
            </Box>

            {/* 登录区域 */}
            <Box
                sx={{
                    flex: 1,
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    p: {xs: 2, sm: 4},
                }}
            >
                {/* 移动设备上的品牌标识 */}
                <Fade in={true} timeout={800}>
                    <Box
                        sx={{
                            display: {xs: "flex", md: "none"},
                            flexDirection: "column",
                            alignItems: "center",
                            mb: 4,
                            textAlign: "center"
                        }}
                    >
                        <Typography
                            variant="h4"
                            component="h1"
                            sx={{
                                fontWeight: 700,
                                color: theme.palette.primary.light,
                                mb: 1
                            }}
                        >
                            {PROJECT_NAME}
                        </Typography>
                        <Typography
                            variant="subtitle1"
                            sx={{
                                color: theme.palette.text.secondary,
                                fontWeight: 500,
                                position: "relative",
                                pb: 2,
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    bottom: 0,
                                    left: "50%",
                                    transform: "translateX(-50%)",
                                    width: "40px",
                                    height: "3px",
                                    borderRadius: "1.5px",
                                    backgroundColor: theme.palette.primary.light,
                                }
                            }}
                        >
                            {PROJECT_DESCRIPTION}
                        </Typography>
                    </Box>
                </Fade>

                {/* 登录卡片 */}
                <Fade in={true} timeout={1000}>
                    <Paper
                        elevation={3}
                        sx={{
                            width: "100%",
                            maxWidth: "400px",
                            borderRadius: "16px",
                            p: {xs: 3, sm: 4},
                            backgroundColor: theme.palette.background.paper,
                        }}
                    >
                        <Typography
                            variant="h5"
                            component="h2"
                            sx={{
                                mb: 3,
                                fontWeight: 600,
                                color: theme.palette.text.primary
                            }}
                        >
                            登录账号
                        </Typography>

                        <form onSubmit={handleSubmit}>
                            <Stack spacing={3}>
                                {error && (
                                    <Alert severity="error" sx={{borderRadius: "8px"}}>
                                        {error}
                                    </Alert>
                                )}

                                <TextField
                                    fullWidth
                                    required
                                    id="username"
                                    name="username"
                                    label="用户名"
                                    variant="outlined"
                                    value={formData.username}
                                    onChange={handleInputChange}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <User size={20} />
                                            </InputAdornment>
                                        ),
                                    }}
                                    sx={{
                                        borderRadius: "8px",
                                    }}
                                />

                                <TextField
                                    fullWidth
                                    required
                                    id="password"
                                    name="password"
                                    label="密码"
                                    type={showPassword ? "text" : "password"}
                                    variant="outlined"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <Lock size={20} />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    aria-label="切换密码可见性"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    edge="end"
                                                >
                                                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                    }}
                                />

                                <Button
                                    fullWidth
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    size="large"
                                    disabled={isLoading}
                                    sx={{
                                        py: 1.5,
                                        mt: 1,
                                        fontWeight: 500,
                                        fontSize: "1rem",
                                    }}
                                >
                                    {isLoading ? "登录中..." : "登录"}
                                </Button>
                            </Stack>
                        </form>

                        <Box sx={{mt: 3, textAlign: "center"}}>
                            <Typography variant="body2" color="text.secondary">
                                {new Date().getFullYear()} {PROJECT_NAME}. 保留所有权利
                            </Typography>
                        </Box>
                    </Paper>
                </Fade>
            </Box>
        </Box>
    );
}
