"""
Redis Set 发布者模块

提供一个简单的函数用于向 Redis Set 中添加消息。
"""
from omni.redis.redis_client import rc  # 导入 Redis 客户端实例
from omni.log.log import olog

def publish_to_redis_set(redis_key: str, message: str):
    """
    向指定的 Redis Set 中添加一条消息。

    Args:
        redis_key: 目标 Redis Set 的键名。
        message: 要添加到 Set 中的消息字符串。

    Returns:
        int: 成功添加到 Set 的元素数量 (1 表示成功添加，0 表示元素已存在)。
             如果发生错误，则返回 None 并记录错误日志。
    """
    try:
        result = rc.sadd(redis_key, message)
        if result == 1:
            olog.debug(f"成功将消息添加到 Redis Set '{redis_key}'")
        else:
            olog.debug(f"消息已存在于 Redis Set '{redis_key}'，未重复添加。")
        return result
    except Exception as e:
        olog.error(f"向 Redis Set '{redis_key}' 添加消息时出错: {e}", exc_info=True)
        return None

def publish_many_to_redis_set(redis_key: str, messages: list[str]):
    """
    向指定的 Redis Set 中批量添加多条消息。

    Args:
        redis_key: 目标 Redis Set 的键名。
        messages: 要添加到 Set 中的消息字符串列表。

    Returns:
        int: 成功添加到 Set 的新元素数量。
             如果消息列表为空，返回 0。
             如果发生错误，则返回 None 并记录错误日志。
    """
    if not messages:
        olog.debug(f"传递给 publish_many_to_redis_set 的消息列表为空，redis_key: '{redis_key}'")
        return 0
    try:
        # 使用 *messages 将列表解包为 SADD 命令的多个参数
        num_added = rc.sadd(redis_key, *messages)
        olog.debug(f"尝试向 Redis Set '{redis_key}' 添加 {len(messages)} 条消息，成功添加了 {num_added} 条新消息。")
        return num_added
    except Exception as e:
        olog.error(f"向 Redis Set '{redis_key}' 批量添加消息时出错: {e}", exc_info=True)
        return None 