"use client";

import {useCallback, useEffect, useState} from 'react';
import {Box, Button, CardMedia, Chip, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Divider, IconButton, InputAdornment, Pagination, TextField, Tooltip, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {RefreshCw, Search, Trash2, Upload, X} from 'lucide-react';
import {useRouter} from 'next/navigation';
import ProductCard from '../../../components/ProductCard';
import {aiBasicMaterialApi} from '@/api/ai-basic-material-api';
import dayjs from 'dayjs';
import {useDispatch} from 'react-redux';
import {addAlert} from '@/core/components/redux/alert-slice';
import {AlertMsg, AlertType} from '@/core/components/alert';

// 添加状态颜色映射函数
const getFetchStatusColor = (status) => {
    switch (status) {
        case '待爬取':
            return 'warning'; // 黄色
        case '爬取中':
            return 'info';    // 蓝色
        case '已完成':
            return 'success'; // 绿色
        case '失败':
            return 'error';   // 红色
        default:
            return 'default'; // 灰色
    }
};

// 添加刷新图标的旋转动画样式
const refreshIconStyle = `
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .spin {
    animation: spin 1s linear infinite;
  }
`;

export default function MaterialManagement() {
    const theme = useTheme();
    const router = useRouter();
    const dispatch = useDispatch();
    const [search, setSearch] = useState('');
    const [searchInput, setSearchInput] = useState('');
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [imagePreviewDialogOpen, setImagePreviewDialogOpen] = useState(false);
    const [previewImages, setPreviewImages] = useState([]);
    const [isAddMaterialDialogOpen, setIsAddMaterialDialogOpen] = useState(false);
    const [xiaohongshuUrl, setXiaohongshuUrl] = useState('');
    const [isAddingMaterial, setIsAddingMaterial] = useState(false);

    const [materialsData, setMaterialsData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [materialToDelete, setMaterialToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const response = await aiBasicMaterialApi.queryAll(search, page, rowsPerPage);
            if (response && response.data) {
                setMaterialsData(response.data);
                setTotalCount(response.total || 0);
            } else {
                setMaterialsData([]);
                setTotalCount(0);
            }
        } catch (err) {
            console.error("Error fetching materials:", err);
            dispatch(addAlert({type: AlertType.ERROR, message: err.message || '获取素材数据失败'}));
            setMaterialsData([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    }, [search, page, rowsPerPage]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleSearchChange = (event) => {
        const value = event.target.value;
        setSearchInput(value);
    };

    const handleSearchKeyDown = (event) => {
        if (event.key === 'Enter') {
            handleSearchClick();
        }
    };

    const handleSearchClick = () => {
        setSearch(searchInput);
        setPage(0);
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage - 1);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleUploadMaterials = () => {
        setXiaohongshuUrl('');
        setIsAddMaterialDialogOpen(true);
    };

    const handleViewDetail = (id) => {
        const materialId = id;

        if (materialId) {
            router.push(`/protected/ai-base-material-management/${materialId}`);
        } else {
            console.error("Invalid material ID for navigation:", id);
        }
    };

    const handleImagePreview = (event, images) => {
        event.stopPropagation();
        const previewUrls = images.map(img => img.signed_url || 'https://placehold.co/600x400?text=ImageNotFound');
        setPreviewImages(previewUrls);
        setImagePreviewDialogOpen(true);
    };

    const handleCloseImagePreview = () => {
        setImagePreviewDialogOpen(false);
    };

    const truncateContent = (content, limit = 80) => {
        if (!content) return '';
        if (content.length <= limit) return content;
        return content.substring(0, limit) + '.';
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';
        return dayjs.unix(timestamp).format('YYYY-MM-DD');
    };

    const handleDeleteClick = (event, material) => {
        event.stopPropagation();
        setMaterialToDelete(material);
        setDeleteDialogOpen(true);
    };

    const handleCloseDeleteDialog = () => {
        setDeleteDialogOpen(false);
        setMaterialToDelete(null);
    };

    const handleConfirmDelete = async () => {
        if (!materialToDelete) return;

        setIsDeleting(true);
        try {
            console.log('删除的素材对象:', materialToDelete);

            const materialId = materialToDelete.id_;

            if (!materialId) {
                throw new Error('素材ID不存在');
            }

            console.log('准备删除素材，ID:', materialId);

            await aiBasicMaterialApi.delete(materialId);

            const newData = materialsData.filter(item => item.id_ !== materialId);
            setMaterialsData(newData);

            setTotalCount(prevCount => prevCount - 1);

            if (newData.length === 0 && page > 0) {
                setPage(page - 1);
            }

            dispatch(addAlert({type: AlertType.SUCCESS, message: AlertMsg.DELETE}));
        } catch (err) {
            console.error("删除素材失败:", err);
            dispatch(addAlert({type: AlertType.ERROR, message: err.message || '删除素材失败'}));
        } finally {
            setIsDeleting(false);
            handleCloseDeleteDialog();
        }
    };

    const handleCloseAddMaterialDialog = () => {
        setIsAddMaterialDialogOpen(false);
        setXiaohongshuUrl('');
    };

    const handleConfirmAddMaterial = async () => {
        if (!xiaohongshuUrl.trim()) {
            dispatch(addAlert({type: AlertType.WARNING, message: '请输入小红书分享URL'}));
            return;
        }
        setIsAddingMaterial(true);
        try {
            console.log("提交的小红书URL:", xiaohongshuUrl);
            // 调用 API 创建素材
            await aiBasicMaterialApi.create(xiaohongshuUrl);
            // await new Promise(resolve => setTimeout(resolve, 1000)); // 移除模拟延时

            dispatch(addAlert({type: AlertType.SUCCESS, message: '素材添加任务已提交，正在获取内容...'}));
            handleCloseAddMaterialDialog();
            // 成功后刷新数据
            fetchData();
        } catch (err) {
            console.error("添加素材失败:", err);
            dispatch(addAlert({type: AlertType.ERROR, message: err.message || '添加素材失败'}));
        } finally {
            setIsAddingMaterial(false);
        }
    };

    const handleRefresh = () => {
        fetchData();
    };

    return (
        <Box sx={{
            py: 3,
            width: '100%',
            px: {xs: 2, sm: 3}
        }}>
            <style jsx global>{refreshIconStyle}</style>

            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                <Typography variant="h4" component="h1" sx={{fontWeight: 600}}>
                    客户素材库
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<Upload size={18}/>}
                    onClick={handleUploadMaterials}
                >
                    添加小红书素材
                </Button>
            </Box>

            <Box sx={{mb: 4}}>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                    <Box sx={{display: 'flex', gap: 1}}>
                        <TextField
                            placeholder="搜索标题或内容"
                            size="small"
                            value={searchInput}
                            onChange={handleSearchChange}
                            onKeyDown={handleSearchKeyDown}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <Search size={18}/>
                                    </InputAdornment>
                                ),
                                sx: {borderRadius: 2}
                            }}
                            sx={{width: 240}}
                        />
                        <Tooltip title="搜索">
                            <span>
                                <IconButton
                                    onClick={handleSearchClick}
                                    size="small"
                                    sx={{borderRadius: 2, ml: 1}}
                                    disabled={loading}
                                >
                                    <Search size={18}/>
                                </IconButton>
                            </span>
                        </Tooltip>
                        <Tooltip title="刷新数据">
                            <span>
                                <IconButton
                                    onClick={handleRefresh}
                                    size="small"
                                    sx={{borderRadius: 2}}
                                    disabled={loading}
                                >
                                    <RefreshCw size={18} className={loading ? "spin" : ""}/>
                                </IconButton>
                            </span>
                        </Tooltip>
                    </Box>
                </Box>

                <Divider/>
            </Box>

            {loading && (
                <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '300px'}}>
                    <CircularProgress/>
                </Box>
            )}

            {!loading && materialsData.length > 0 && (
                <Box sx={{mb: 3}}>
                    <Box sx={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
                        gap: 3
                    }}>
                        {materialsData.map((material, materialIndex) => (
                            <ProductCard
                                key={material.id_ || `material-${materialIndex}`}
                                images={material.images?.map(img => img.signed_url || 'https://placehold.co/600x400?text=ImageNotFound') || []}
                                onClick={() => handleViewDetail(material.id_)}
                            >
                                <Box sx={{
                                    p: 2,
                                    flexGrow: 1,
                                    display: 'flex',
                                    flexDirection: 'column'
                                }}>
                                    <Box sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'flex-start',
                                        mb: 1
                                    }}>
                                        <Typography
                                            variant="subtitle1"
                                            title={material.title}
                                            sx={{
                                                fontWeight: 600,
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                lineHeight: 1.3,
                                                minHeight: '2.6em',
                                                flex: 1
                                            }}
                                        >
                                            {material.title}
                                        </Typography>
                                        <IconButton
                                            size="small"
                                            onClick={(e) => handleDeleteClick(e, material)}
                                            sx={{
                                                ml: 1,
                                                color: theme.palette.error.main,
                                                '&:hover': {
                                                    backgroundColor: theme.palette.error.light,
                                                }
                                            }}
                                        >
                                            <Trash2 size={16}/>
                                        </IconButton>
                                    </Box>

                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        title={material.content}
                                        sx={{
                                            mb: 1.5,
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            display: '-webkit-box',
                                            WebkitLineClamp: 3,
                                            WebkitBoxOrient: 'vertical',
                                            minHeight: '3.6em'
                                        }}
                                    >
                                        {truncateContent(material.content)}
                                    </Typography>

                                    <Box sx={{
                                        display: 'flex',
                                        flexWrap: 'wrap',
                                        gap: 0.5,
                                        mb: 'auto',
                                        minHeight: '24px'
                                    }}>
                                        {(material.tags || []).slice(0, 3).map((tag, index) => (
                                            <Chip
                                                key={`${material.id_}-tag-${index}`}
                                                label={tag}
                                                size="small"
                                                sx={{
                                                    fontSize: '0.7rem',
                                                    maxWidth: '80px',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap'
                                                }}
                                            />
                                        ))}
                                        {(material.tags?.length || 0) > 3 && (
                                            <Typography
                                                key={`${material.id_}-tag-more`}
                                                variant="caption"
                                                sx={{color: theme.palette.text.secondary, alignSelf: 'center'}}
                                            >
                                                +{(material.tags.length) - 3}
                                            </Typography>
                                        )}
                                    </Box>

                                    <Box sx={{
                                        mt: 2,
                                        pt: 1,
                                        borderTop: `1px solid ${theme.palette.divider}`,
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center'
                                    }}>
                                        <Typography variant="caption" color="text.secondary">
                                            {material.platform || '未知来源'}
                                        </Typography>
                                        <Chip
                                            label={material.fetch_status || '未知'}
                                            size="small"
                                            color={getFetchStatusColor(material.fetch_status)}
                                            sx={{fontSize: '0.7rem', mr: 1}}
                                        />
                                        <Typography variant="caption" color="text.secondary">
                                            {formatTimestamp(material.create_at)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </ProductCard>
                        ))}
                    </Box>

                    <Box sx={{display: 'flex', justifyContent: 'center', mt: 4, mb: 2}}>
                        <Pagination
                            count={Math.ceil(totalCount / rowsPerPage)}
                            page={page + 1}
                            onChange={handleChangePage}
                            color="primary"
                            showFirstButton
                            showLastButton
                        />
                    </Box>
                </Box>
            )}

            {!loading && materialsData.length === 0 && (
                <Box sx={{textAlign: 'center', mt: 5, color: theme.palette.text.secondary}}>
                    <Typography>暂无素材数据</Typography>
                </Box>
            )}

            <Dialog
                open={imagePreviewDialogOpen}
                onClose={handleCloseImagePreview}
                maxWidth="md"
                fullWidth
                onClick={(e) => e.stopPropagation()}
            >
                <DialogTitle>
                    <Typography variant="h6">素材图片</Typography>
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseImagePreview}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: theme.palette.grey[500],
                        }}
                    >
                        <X size={20}/>
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'center'}}>
                        {previewImages.map((image, index) => (
                            <CardMedia
                                key={`preview-image-${index}`}
                                component="img"
                                src={image}
                                alt={`图片 ${index + 1}`}
                                sx={{
                                    maxHeight: {xs: 200, sm: 300, md: 400},
                                    maxWidth: '100%',
                                    width: 'auto',
                                    objectFit: 'contain',
                                    borderRadius: 1,
                                    border: `1px solid ${theme.palette.divider}`
                                }}
                            />
                        ))}
                        {previewImages.length === 0 && <Typography key="no-preview-message">没有可预览的图片</Typography>}
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseImagePreview}>关闭</Button>
                </DialogActions>
            </Dialog>

            <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
                <DialogTitle>确认删除</DialogTitle>
                <DialogContent>
                    <Typography>
                        您确定要删除素材"{materialToDelete?.title || '未命名素材'}"吗？此操作不可恢复。
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDeleteDialog} disabled={isDeleting}>
                        取消
                    </Button>
                    <Button
                        onClick={handleConfirmDelete}
                        color="error"
                        disabled={isDeleting}
                        startIcon={isDeleting ? <CircularProgress size={16}/> : null}
                    >
                        {isDeleting ? '删除中...' : '删除'}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={isAddMaterialDialogOpen} onClose={handleCloseAddMaterialDialog} maxWidth="sm" fullWidth>
                <DialogTitle>添加小红书素材</DialogTitle>
                <DialogContent>
                    <Typography variant="body2" sx={{mb: 2}}>
                        请输入小红书图文的分享链接：
                    </Typography>
                    <TextField
                        autoFocus
                        margin="dense"
                        id="xiaohongshu-url"
                        label="小红书分享URL"
                        type="url"
                        fullWidth
                        variant="outlined"
                        value={xiaohongshuUrl}
                        onChange={(e) => setXiaohongshuUrl(e.target.value)}
                        disabled={isAddingMaterial}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseAddMaterialDialog} disabled={isAddingMaterial}>
                        取消
                    </Button>
                    <Button
                        onClick={handleConfirmAddMaterial}
                        variant="contained"
                        disabled={isAddingMaterial}
                        startIcon={isAddingMaterial ? <CircularProgress size={16}/> : null}
                    >
                        {isAddingMaterial ? '添加中...' : '确认添加'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
} 