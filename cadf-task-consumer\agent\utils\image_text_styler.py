import io
import os
import time
from requests.exceptions import ReadTimeout

from PIL import Image, ImageDraw, ImageFont
from pilmoji import <PERSON><PERSON><PERSON><PERSON>

from omni.log.log import olog


class ImageTextStyler:
    def __init__(self):
        # 定义字体路径
        self.text_font_path = os.path.join(
            os.path.dirname(__file__), "AlimamaShuHeiTi-Bold.ttf"
        )  # 文字字体
        self.body_font_size = 40
        self.title_font_size = self.body_font_size * 1.3
        self.edge_padding_percent = 0.05
        self.border_width = 4

        self.text_font = ImageFont.truetype(self.text_font_path, self.body_font_size)
        self.title_text_font = ImageFont.truetype(
            self.text_font_path, int(self.title_font_size)
        )  # 新增标题字体

        self.title_color = (255, 220, 100, 255)
        self.body_color = (255, 255, 255, 255)
        self.text_padding = 20
        self.line_spacing = 10

    def _draw_text_with_retry(self, renderer, *args, max_retries=3, retry_interval=0.5, **kwargs):
        """带重试机制的 pilmoji text 渲染"""
        for attempt in range(1, max_retries + 1):
            try:
                return renderer.text(*args, **kwargs)
            except ReadTimeout as e:
                olog.warning(f"第{attempt}次渲染emoji超时，准备重试: {e}")
                if attempt == max_retries:
                    olog.error("多次重试后仍然失败，放弃渲染emoji")
                    raise
                time.sleep(retry_interval)
            except Exception as e:
                # 其他异常直接抛出，由外层统一处理
                raise

    def apply_style(
        self,
        image_bytes: bytes,
        title: str,
        bodies: list[str] = None,
        position: str = "左上",
    ) -> bytes | None:
        if bodies is None:
            bodies = []
        try:
            img = Image.open(io.BytesIO(image_bytes)).convert("RGBA")
            img_width, img_height = img.size

            draw_for_border_and_measure = ImageDraw.Draw(img)
            pilmoji_renderer = Pilmoji(img)

            padding_x = int(img_width * self.edge_padding_percent)
            padding_y = int(img_height * self.edge_padding_percent)

            title_color = self.title_color
            body_color = self.body_color

            total_height = 0
            max_width = 0

            if title:
                title_bbox = draw_for_border_and_measure.textbbox(
                    (0, 0), title, font=self.title_text_font
                )
                max_width = max(max_width, title_bbox[2] - title_bbox[0])
                total_height += (title_bbox[3] - title_bbox[1]) + self.line_spacing * 2

            if bodies:
                for line in bodies:
                    line_bbox = draw_for_border_and_measure.textbbox(
                        (0, 0), line, font=self.text_font
                    )
                    max_width = max(max_width, line_bbox[2] - line_bbox[0])
                    total_height += (line_bbox[3] - line_bbox[1]) + self.line_spacing

            max_x = img_width - max_width - padding_x
            max_y = img_height - total_height - padding_y

            if "左上" in position:
                region_x = padding_x
                region_y = padding_y
            elif "左下" in position:
                region_x = padding_x
                region_y = max(max_y, padding_y)
            elif "右上" in position:
                region_x = max(max_x, padding_x)
                region_y = padding_y
            elif "右下" in position:
                region_x = max(max_x, padding_x)
                region_y = max(max_y, padding_y)
            elif "左中" in position:
                region_x = padding_x
                region_y = int((img_height - total_height) / 2)
            elif "右中" in position:
                region_x = max(max_x, padding_x)
                region_y = int((img_height - total_height) / 2)
            else:
                raise ValueError(
                    "无效的位置参数，可选值为 '左上'、'左下'、'右上'、'右下'、'左中'、'右中'"
                )

            current_y = int(region_y)
            border_color = (0, 0, 0, 255)

            if title:
                title_actual_bbox = draw_for_border_and_measure.textbbox(
                    (0, 0), title, font=self.title_text_font
                )
                title_width = title_actual_bbox[2] - title_actual_bbox[0]
                title_height = title_actual_bbox[3] - title_actual_bbox[1]

                title_x = int(region_x + (max_width - title_width) / 2)
                self._draw_text_with_retry(
                    pilmoji_renderer,
                    (title_x, current_y),
                    title,
                    font=self.title_text_font,
                    fill=title_color,
                    stroke_width=self.border_width,
                    stroke_fill=border_color,
                )
                current_y += title_height + self.line_spacing * 2

            if bodies:
                for line in bodies:
                    line_actual_bbox = draw_for_border_and_measure.textbbox(
                        (0, 0), line, font=self.text_font
                    )
                    line_width = line_actual_bbox[2] - line_actual_bbox[0]
                    line_height = line_actual_bbox[3] - line_actual_bbox[1]

                    line_x = int(region_x + (max_width - line_width) / 2)
                    self._draw_text_with_retry(
                        pilmoji_renderer,
                        (line_x, current_y),
                        line,
                        font=self.text_font,
                        fill=body_color,
                        stroke_width=self.border_width,
                        stroke_fill=border_color,
                    )
                    current_y += line_height + self.line_spacing

            output_buffer = io.BytesIO()
            img.save(output_buffer, format="PNG")
            return output_buffer.getvalue()

        except Exception as e:
            olog.exception(f"应用样式时发生错误: {e}")
            return None
