import importlib
import importlib.util
import os

from fastapi import APIRouter

from omni.log.log import olog


def register_routers(app, package_name):
    try:
        # 尝试导入包
        package = importlib.import_module(package_name)

        # 获取包的目录路径
        package_dir = os.path.dirname(package.__file__)

        # 遍历包目录中的所有文件
        for module_name in os.listdir(package_dir):
            # 只处理 router.py 或 blueprint.py 文件，并排除 __init__.py 等特殊文件
            if (module_name.endswith('router.py') or module_name.endswith('blueprint.py')) and not module_name.startswith('__'):
                module_name = module_name[:-3]  # 去掉文件扩展名
                try:
                    module = importlib.import_module(f'{package_name}.{module_name}')  # 动态导入模块

                    # 检查模块中的每个属性
                    for attribute_name in dir(module):
                        attribute = getattr(module, attribute_name)

                        # 如果属性是一个 APIRouter 实例，则注册到 app
                        if isinstance(attribute, APIRouter):
                            app.include_router(attribute)
                            olog.info(f"已注册路由: {attribute.prefix or '/'} 从模块 {package_name}.{module_name}")
                except Exception as e:
                    import traceback
                    olog.error(f"错误堆栈信息:\n{traceback.format_exc()}")
                    olog.error(f"导入模块 {package_name}.{module_name} 时出错: {str(e)}")
    except ModuleNotFoundError:
        olog.warning(f"包 {package_name} 不存在，跳过路由注册")
    except Exception as e:
        olog.error(f"注册路由时出错: {str(e)}")
