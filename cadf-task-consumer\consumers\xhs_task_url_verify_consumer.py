import time
from config.config import XHS_TASK_URL_VERIFY_SET
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from repository.models import PromotionTaskDetail, Account, AiGeneratedMaterial
from scraper.xhs_note_scraper import xhs_note_scraper


@consume_redis_set(redis_key=XHS_TASK_URL_VERIFY_SET, num_threads=1)
def handle_task(task_detail_id):
    olog.info(f"开始处理小红书发布验证任务: {task_detail_id}")
    try:
        # 1. 获取关联数据
        task_detail = PromotionTaskDetail.objects(id=task_detail_id).first()
        if not task_detail:
            olog.error(f"未找到 ID 为 {task_detail_id} 的 PromotionTaskDetail。")
            return

        if not task_detail.publish_url:
            olog.error(f"PromotionTaskDetail {task_detail_id} 没有发布链接 (publish_url)。")
            return

        if not task_detail.account_id:
            olog.error(f"PromotionTaskDetail {task_detail_id} 没有关联账号 ID (account_id)。")
            return

        if not task_detail.ai_generated_material_id:
            olog.error(f"PromotionTaskDetail {task_detail_id} 没有关联 AI 素材 ID (ai_generated_material_id)。")
            return

        account = Account.objects(id=task_detail.account_id).first()
        if not account:
            olog.error(f"未找到 PromotionTaskDetail {task_detail_id} 关联的账号 ID: {task_detail.account_id}。")
            return

        # Add Account Status Check
        if account.status != "在线":
            olog.error(f"账号 {task_detail.account_id} ({account.name}) 状态为 '{account.status}'，不是 '在线'，无法进行验证。")
            task_detail.validation_status = '失败'
            task_detail.validation_details = f"您的账号（{account.name}）当前状态为'{account.status}'，暂时无法进行发布验证。请检查账号状态后再试。"
            task_detail.save()
            return
        # --- End of Add ---

        # --- 根据平台选择验证逻辑 ---
        platform = account.platform
        if platform == "小红书":
            olog.info(f"任务 {task_detail_id}: 检测到平台为小红书，开始执行验证流程。")

            # 检查 Cookie
            if not account.cookie:
                olog.error(f"账号 {task_detail.account_id} ({account.name}) 没有配置 Cookie，无法进行小红书验证。")
                task_detail.validation_status = '失败'
                task_detail.validation_details = f"您的账号（{account.name}）缺少必要的配置信息（Cookie），无法进行发布验证。请联系技术支持或检查账号设置。"
                task_detail.save()
                return

            # 检查关联素材
            material = AiGeneratedMaterial.objects(id=task_detail.ai_generated_material_id).first()
            if not material:
                olog.error(f"未找到 PromotionTaskDetail {task_detail_id} 关联的 AI 素材 ID: {task_detail.ai_generated_material_id}，无法进行验证。")
                task_detail.validation_status = '失败'
                task_detail.validation_details = "系统未能找到与此任务关联的AI生成素材，无法进行内容验证。请检查任务设置或联系技术支持。"
                task_detail.save()
                return

            # 调用爬虫
            olog.info(f"开始为任务 {task_detail_id} (小红书) 爬取链接: {task_detail.publish_url}")
            scraped_data = None
            max_retries = 5
            last_scrape_error = None # 用于存储最后一次尝试的错误

            for attempt_count in range(max_retries): # 尝试次数从 0 到 4 (共5次)
                current_attempt_num = attempt_count + 1
                olog.info(f"任务 {task_detail_id} (小红书): 尝试第 {current_attempt_num}/{max_retries} 次爬取链接 {task_detail.publish_url}")
                try:
                    current_scraped_data = xhs_note_scraper(
                        xhs_url=task_detail.publish_url,
                        mongo_cookies=account.cookie, # Pass raw cookies
                        scrape_comments=False
                    )
                    if current_scraped_data: # 成功获取到数据
                        scraped_data = current_scraped_data
                        olog.info(f"任务 {task_detail_id} (小红书): 第 {current_attempt_num} 次爬取成功。")
                        last_scrape_error = None # 清除之前的错误记录
                        break # 成功，跳出重试循环
                    else: # 爬虫执行成功，但未返回有效数据
                        last_scrape_error = ValueError("爬虫执行成功但未返回有效数据")
                        olog.warning(f"任务 {task_detail_id} (小红书): 第 {current_attempt_num} 次爬取成功，但未返回有效数据。")

                except Exception as e:
                    last_scrape_error = e # 记录当前尝试的错误
                    olog.warning(f"任务 {task_detail_id} (小红书): 第 {current_attempt_num}/{max_retries} 次爬取失败: {e}")

                if current_attempt_num < max_retries: # 如果不是最后一次尝试，则等待后重试
                    delay_seconds = current_attempt_num # 延迟时间：1, 2, 3, 4 秒
                    olog.info(f"任务 {task_detail_id} (小红书): {delay_seconds} 秒后进行下一次尝试...")
                    time.sleep(delay_seconds)
            
            # 所有重试尝试结束后，检查是否成功获取数据
            if not scraped_data:
                error_message_base = f"系统在尝试多次后仍无法获取您在小红书发布的笔记内容（尝试{max_retries}次）。可能原因包括链接错误、网络波动或平台限制。请稍后重试或检查发布链接。"
                if last_scrape_error:
                    error_message_detail = f"{error_message_base} 技术参考：{str(last_scrape_error)[:150]}"
                else:
                    error_message_detail = error_message_base
                
                olog.error(f"任务 {task_detail_id} (小红书): 爬取 {task_detail.publish_url} 失败。{error_message_detail}")
                task_detail.validation_status = '失败'
                task_detail.validation_details = error_message_detail
                task_detail.save()
                return

            # 执行验证
            validation_successful = True
            failures = []

            # 4.1 验证标题
            expected_title = material.title
            part_title = scraped_data.get('title')
            if part_title is None or not expected_title.startswith(part_title):
                validation_successful = False
                failures.append(f"笔记标题与预期不符。预期：'{part_title}'，实际检测到：'{expected_title}'。")
                olog.warning(f"任务 {task_detail_id} (小红书) 验证失败: 标题不匹配 (预期以 '{part_title}' 开头, 实际: '{expected_title}')")

            # 4.2 验证图片数量
            expected_image_count = len(material.images) if material.images else 0
            actual_image_count = len(scraped_data.get('images', []))
            if expected_image_count != actual_image_count:
                validation_successful = False
                failures.append(f"笔记图片数量与预期不符。预期：{expected_image_count}张，实际检测到：{actual_image_count}张。")
                olog.warning(f"任务 {task_detail_id} (小红书) 验证失败: 图片数量不匹配 (预期: {expected_image_count}, 实际: {actual_image_count})")

            # 记录最终结果
            try:
                if validation_successful:
                    olog.info(f"任务 {task_detail_id} (小红书) 验证成功: {task_detail.publish_url}")
                    task_detail.validation_status = '成功'
                    task_detail.validation_details = None
                    task_detail.save()
                else:
                    failure_reasons = "; ".join(failures)
                    olog.error(f"任务 {task_detail_id} (小红书) 验证失败: {task_detail.publish_url}. 原因: {failure_reasons}")
                    task_detail.validation_status = '失败'
                    task_detail.validation_details = failure_reasons
                    task_detail.save()
            except Exception as update_err:
                 olog.error(f"更新任务 {task_detail_id} (小红书) 验证状态时出错: {update_err}", exc_info=True)
                 # 即使更新失败，验证流程也算走完了，外层 except 会捕获并标记失败

        elif platform == "抖音":
            olog.warning(f"任务 {task_detail_id}: 抖音平台的验证逻辑尚未实现。")
            # TODO: 实现抖音验证逻辑
            # task_detail.validation_status = '失败'
            # task_detail.validation_details = '抖音平台验证未实现'
            # task_detail.save()
            return
        elif platform == "微博":
            olog.warning(f"任务 {task_detail_id}: 微博平台的验证逻辑尚未实现。")
            # TODO: 实现微博验证逻辑
            # task_detail.validation_status = '失败'
            # task_detail.validation_details = '微博平台验证未实现'
            # task_detail.save()
            return
        else:
             olog.error(f"任务 {task_detail_id}: 当前不支持平台 '{platform}' 的发布验证。")
             try:
                 task_detail.validation_status = '失败'
                 task_detail.validation_details = f"系统当前不支持对'{platform}'平台的发布内容进行自动验证。"
                 task_detail.save()
             except Exception as update_err:
                 olog.error(f"尝试标记任务 {task_detail_id} 为不支持平台时出错: {update_err}")
             return
        # --- 平台特定逻辑结束 ---

    except Exception as e:
        olog.error(f"处理发布验证任务 {task_detail_id} 时发生未预期错误: {e}", exc_info=True)
        # 尝试将 task_detail 标记为 失败 (内部错误)
        try:
            # 重新获取 task_detail 对象以防之前的引用失效
            task_detail_to_update = PromotionTaskDetail.objects(id=task_detail_id).first()
            if task_detail_to_update:
                task_detail_to_update.validation_status = '失败'
                task_detail_to_update.validation_details = f"验证过程中系统出现内部错误，导致无法完成验证。请稍后重试或联系技术支持。技术参考：{str(e)[:150]}"
                task_detail_to_update.save()
                olog.info(f"已将任务 {task_detail_id} 状态标记为 失败 (内部错误)。")
            else:
                olog.error(f"在尝试标记错误状态时，未能重新找到任务 {task_detail_id}。")
        except Exception as final_update_err:
            olog.error(f"尝试将任务 {task_detail_id} 标记为 失败 (内部错误) 状态时再次失败: {final_update_err}", exc_info=True)
