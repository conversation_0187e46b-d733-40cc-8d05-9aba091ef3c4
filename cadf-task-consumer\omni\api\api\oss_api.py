from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient


# 腾讯对向存储
@register_handler('oss')
class OssApi:

    @auth_required(['user', 'admin'])
    def gen_signed_url(self, data):
        signed_method = data.get('signed_method')
        signed_key = data.get('signed_key')
        signed_url_result = OSSClient.getInstance().signed_url(signed_method, signed_key)
        return signed_url_result
