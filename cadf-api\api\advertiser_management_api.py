import time

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import User
from omni.log.log import olog


@register_handler("advertiser")
class AdvertiserManagementApi:

    @auth_required(["admin"])
    def create(self, data):
        """创建广告主账户"""
        username = data.get("username")
        password = data.get("password")
        
        if not username or not password:
            raise MException("用户名和密码不能为空")
            
        # 检查用户名是否已存在
        existing_user = User.objects(username=username).first()
        if existing_user:
            raise MException("用户名已存在，请更换用户名")
            
        # 创建新的广告主用户
        new_user = User(
            username=username,
            password=password,
            roles=["advertiser"],
            create_at=int(time.time())
        )
        new_user.save()
        
        olog.info(f"管理员创建了新的广告主账户: {username}")
        
        return {
            "message": "广告主账户创建成功",
            "user_id": str(new_user.id),
            "username": username
        }

    @auth_required(["admin"])
    def query_list(self, data):
        """查询广告主列表"""
        page = data.get("page", 1)
        page_size = data.get("page_size", 10)
        search = data.get("search", "")
        
        # 构建查询条件
        query_filter = {"roles__in": ["advertiser"]}
        if search:
            query_filter["username__icontains"] = search
            
        # 分页查询
        skip = (page - 1) * page_size
        advertisers = User.objects(**query_filter).skip(skip).limit(page_size)
        total = User.objects(**query_filter).count()
        
        # 转换为字典格式
        advertiser_list = []
        for advertiser in advertisers:
            advertiser_dict = doc_to_dict(advertiser)
            # 移除密码字段
            advertiser_dict.pop("password", None)
            advertiser_list.append(advertiser_dict)
        
        return {
            "advertisers": advertiser_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }

    @auth_required(["admin"])
    def query_detail(self, data):
        """查询单个广告主详情"""
        target_user_id = data.get("target_user_id")
        
        user = User.objects(id=target_user_id, roles__in=["advertiser"]).first()
        if not user:
            raise MException("广告主不存在")
            
        user_dict = doc_to_dict(user)
        # 移除密码字段
        user_dict.pop("password", None)
        
        return user_dict

    @auth_required(["admin"])
    def update(self, data):
        """更新广告主信息"""
        target_user_id = data.get("target_user_id")
        username = data.get("username")
        password = data.get("password")
        
        user = User.objects(id=target_user_id, roles__in=["advertiser"]).first()
        if not user:
            raise MException("广告主不存在")
            
        # 检查用户名是否被其他用户使用
        if username and username != user.username:
            existing_user = User.objects(username=username).first()
            if existing_user:
                raise MException("用户名已存在，请更换用户名")
            user.username = username
            
        # 更新密码
        if password:
            user.password = password
            
        user.save()
        
        olog.info(f"管理员更新了广告主账户信息: {user.username}")
        
        return {"message": "广告主信息更新成功"}

    @auth_required(["admin"])
    def delete(self, data):
        """删除广告主"""
        target_user_id = data.get("target_user_id")
        
        user = User.objects(id=target_user_id, roles__in=["advertiser"]).first()
        if not user:
            raise MException("广告主不存在")
            
        username = user.username
        user.delete()
        
        olog.info(f"管理员删除了广告主账户: {username}")
        
        return {"message": "广告主删除成功"}