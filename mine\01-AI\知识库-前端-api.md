# 约束

前后端传递数据时,id 字段在 json 里用 id_表示

# API 组件使用例子

resource 与 method_name 是必须的

resource 的值对应的是@register_handler('xxx')中的 xxx

method_name 的值对应的是 Python 中的方法名

`user_id` 参数由后端通过认证信息自动获取，前端无需传递。

response 是 api 中 return 返回的值

一般处理情况

```js
import api from "@/core/api/api";

export const userApi = {
  login: async (username, password) => {
    return await api({
      resource: "user",
      method_name: "login",
      username,
      password,
    });
  },
};
```
