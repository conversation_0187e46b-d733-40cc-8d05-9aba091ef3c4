"use client";

import SidebarLink from "@/app/protected/components/SidebarLink";
import {LOGIN_PATH, PROJECT_DESCRIPTION, PROJECT_NAME} from "@/config";
import {Box, Divider, Drawer, IconButton, List, ListItemButton, ListItemIcon, ListItemText, useMediaQuery} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Cookies from 'js-cookie';
import {Brain, ChevronDown, ChevronRight, DollarSign, LayoutDashboard, ListChecks, LogOut, Menu, Ship, Users} from "lucide-react";
import {useRouter} from 'next/navigation';
import {useEffect, useState} from 'react';
import {userApi} from "@/api/user-api";

const drawerWidth = 240;

export default function ProtectedLayout({children}) {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("md"));
    const [mobileOpen, setMobileOpen] = useState(false);
    const router = useRouter();
    const [expandedMenus, setExpandedMenus] = useState({});
    const [userRoles, setUserRoles] = useState([]);
    const [loadingRoles, setLoadingRoles] = useState(true);

    useEffect(() => {
        const fetchUserRoles = async () => {
            try {
                const response = await userApi.getRoles();
                setUserRoles(response.roles);
            } catch (error) {
                console.error("获取用户角色失败:", error);
            } finally {
                setLoadingRoles(false);
            }
        };

        fetchUserRoles();
    }, []);

    const handleDrawerToggle = () => {
        setMobileOpen(!mobileOpen);
    };

    const toggleSubMenu = (menuText) => {
        setExpandedMenus((prev) => ({
            ...prev,
            [menuText]: !prev[menuText],
        }));
    };

    const handleLogout = () => {
        Cookies.remove("access_token");
        router.push(LOGIN_PATH);
    };

    const menuItems = [
        {text: "控制台", icon: <LayoutDashboard size={20}/>, path: "/protected/dashboard"},
        {text: "我的收益", icon: <DollarSign size={20}/>, path: "/protected/revenue"},
        {text: "任务中心", icon: <ListChecks size={20}/>, path: "/protected/tasks/account"},
        {text: "账号管理", icon: <Users size={20}/>, path: "/protected/accounts"},
    ];

    if (!loadingRoles && (userRoles.includes("captain") || userRoles.includes("admin"))) {
        menuItems.push({
            text: "轻创业舰长",
            icon: <Ship size={20}/>,
            children: [
                {text: "舰长收益", icon: <DollarSign size={20}/>, path: "/protected/captain-revenue"},
                {text: "舰员管理", icon: <Users size={20}/>, path: "/protected/crew-management"},
            ],
        });
    }

    const drawer = (
        <Box
            sx={{
                overflow: "auto",
                height: "100%",
                display: "flex",
                flexDirection: "column",
            }}
        >
            <Box
                sx={{
                    py: 2.5,
                    px: 2,
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    color: theme.palette.text.primary,
                }}
            >
                <Brain
                    size={28}
                    color={theme.palette.primary.main}
                    style={{marginRight: "12px"}}
                />
                <Box>
                    <Typography
                        variant="subtitle1"
                        component="div"
                        sx={{
                            fontWeight: 600,
                            lineHeight: 1.2,
                            fontSize: "0.95rem",
                        }}
                    >
                        {PROJECT_NAME}
                    </Typography>
                    <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                            fontSize: "0.7rem",
                            display: "block",
                            mt: 0.3,
                        }}
                    >
                        {PROJECT_DESCRIPTION}
                    </Typography>
                </Box>
            </Box>
            <Divider/>
            <List sx={{flexGrow: 1}}>
                {menuItems.map((item) =>
                    item.children ? (
                        <Box key={item.text}>
                            <ListItemButton
                                sx={{
                                    py: 1.2,
                                    borderRadius: 1,
                                    mx: 1.5,
                                    px: 0,
                                    "&:hover": {
                                        backgroundColor: `rgba(${theme.palette.primary.main}, 0.08)`,
                                        color: theme.palette.primary.main,
                                        "& .MuiListItemIcon-root": {
                                            color: theme.palette.primary.main,
                                        },
                                    },
                                }}
                                onClick={() => toggleSubMenu(item.text)}
                            >
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        width: "100%",
                                        pl: 2,
                                        pr: 2,
                                    }}
                                >
                                    <ListItemIcon
                                        sx={{
                                            minWidth: 36,
                                            color: theme.palette.text.secondary,
                                        }}
                                    >
                                        {item.icon}
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={item.text}
                                        primaryTypographyProps={{
                                            fontSize: "0.95rem",
                                        }}
                                        sx={{flex: 1}}
                                    />
                                    {expandedMenus[item.text] ? (
                                        <ChevronDown size={16}/>
                                    ) : (
                                        <ChevronRight size={16}/>
                                    )}
                                </Box>
                            </ListItemButton>
                            {expandedMenus[item.text] && (
                                <List disablePadding>
                                    {item.children.map((child) => (
                                        <SidebarLink
                                            key={child.text}
                                            item={{
                                                ...child,
                                                isSubMenu: true,
                                                parentIconWidth: 36,
                                            }}
                                            onClick={isMobile ? handleDrawerToggle : undefined}
                                        />
                                    ))}
                                </List>
                            )}
                        </Box>
                    ) : (
                        <SidebarLink
                            key={item.text}
                            item={item}
                            onClick={isMobile ? handleDrawerToggle : undefined}
                        />
                    )
                )}
            </List>
            <Divider/>
            <Box sx={{p: 2}}>
                <ListItemButton
                    sx={{
                        p: 1.5,
                        borderRadius: 2,
                        border: `1px dashed ${theme.palette.divider}`,
                        "&:hover": {
                            backgroundColor: `rgba(${theme.palette.error.main}, 0.08)`,
                            color: theme.palette.error.main,
                            "& .MuiListItemIcon-root": {
                                color: theme.palette.error.main,
                            },
                        },
                        transition: "all 0.2s",
                    }}
                    onClick={handleLogout}
                >
                    <ListItemIcon>
                        <LogOut size={20} color="action"/>
                    </ListItemIcon>
                    <ListItemText
                        primary="退出登录"
                        primaryTypographyProps={{
                            fontSize: "0.95rem",
                        }}
                    />
                </ListItemButton>
            </Box>
        </Box>
    );

    return (
        <Box sx={{display: "flex"}}>
            {isMobile && (
                <Box sx={{position: "fixed", top: 10, left: 10, zIndex: 1200}}>
                    <IconButton
                        color="primary"
                        aria-label="打开菜单"
                        edge="start"
                        onClick={handleDrawerToggle}
                        sx={{
                            bgcolor: "background.paper",
                            boxShadow: 1,
                            "&:hover": {bgcolor: "background.paper"},
                        }}
                    >
                        <Menu/>
                    </IconButton>
                </Box>
            )}

            <Box
                component="nav"
                sx={{width: {md: drawerWidth}, flexShrink: {md: 0}}}
            >
                <Drawer
                    variant="temporary"
                    open={mobileOpen}
                    onClose={handleDrawerToggle}
                    ModalProps={{
                        keepMounted: true,
                    }}
                    sx={{
                        display: {xs: "block", md: "none"},
                        "& .MuiDrawer-paper": {
                            boxSizing: "border-box",
                            width: drawerWidth,
                        },
                    }}
                >
                    {drawer}
                </Drawer>

                <Drawer
                    variant="permanent"
                    sx={{
                        display: {xs: "none", md: "block"},
                        "& .MuiDrawer-paper": {
                            boxSizing: "border-box",
                            width: drawerWidth,
                        },
                    }}
                    open
                >
                    {drawer}
                </Drawer>
            </Box>

            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: 3,
                    width: {md: `calc(100% - ${drawerWidth}px)`},
                    height: "100vh",
                    display: "flex",
                    flexDirection: "column",
                }}
            >
                <Box
                    sx={{
                        flexGrow: 1,
                        maxWidth: "1600px",
                        mx: "auto",
                        width: "100%",
                    }}
                >
                    {children}
                </Box>
            </Box>
        </Box>
    );
}
