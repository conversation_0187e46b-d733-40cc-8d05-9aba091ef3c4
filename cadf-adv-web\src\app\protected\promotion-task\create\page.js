"use client"

import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Box, Button, CircularProgress, Container, FormControl, Grid, InputAdornment, InputLabel, MenuItem, Pagination, Paper, Select, Slider, Stack, Step, StepLabel, Stepper, TextField, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {ArrowLeft, ArrowRight, Check, Search, Plus} from 'lucide-react';
import {useRouter} from 'next/navigation';
import ProductCard from '@/components/ProductCard';
import {useDispatch} from "react-redux";
import {addAlert} from "@/core/components/redux/alert-slice";
import {AlertMsg, AlertType} from "@/core/components/alert";
import {advPromotionTaskApi} from "@/api/adv-promotion-task-api";
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DatePicker} from '@mui/x-date-pickers/DatePicker';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';

dayjs.locale('zh-cn');

// 步骤标题
const steps = ['选择要推广的产品', '设置推广详情'];

export default function CreateTaskPage() {
    const theme = useTheme();
    const router = useRouter();
    const dispatch = useDispatch();
    const [activeStep, setActiveStep] = useState(0);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [promotionCount, setPromotionCount] = useState(1);
    const [platform, setPlatform] = useState('小红书');
    const [startDate, setStartDate] = useState(dayjs());
    const [endDate, setEndDate] = useState(dayjs().add(7, 'day'));
    const [error, setError] = useState('');
    const [apiError, setApiError] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [appliedSearchQuery, setAppliedSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [products, setProducts] = useState([]);
    const [totalProducts, setTotalProducts] = useState(0);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const productsPerPage = 5;
    const [noMaterialDialogOpen, setNoMaterialDialogOpen] = useState(false);
    const [pendingProduct, setPendingProduct] = useState(null);

    const fetchProducts = useCallback(async () => {
        setLoading(true);
        setApiError('');
        try {
            const response = await advPromotionTaskApi.query_product_overview(
                appliedSearchQuery,
                currentPage - 1,
                productsPerPage
            );
            if (response && response.products) {
                setProducts(response.products);
                setTotalProducts(response.total_count || 0);
            } else {
                setProducts([]);
                setTotalProducts(0);
                setApiError('未能获取产品数据');
            }
        } catch (err) {
            console.error("获取产品失败:", err);
            setApiError(err.message || '获取产品列表时出错');
            setProducts([]);
            setTotalProducts(0);
        } finally {
            setLoading(false);
        }
    }, [appliedSearchQuery, currentPage, productsPerPage]);

    useEffect(() => {
        fetchProducts();
    }, [fetchProducts]);

    const handleNext = async () => {
        setError('');

        if (activeStep === 0) {
            if (!selectedProduct) {
                setError('请选择一个产品进行推广');
                return;
            }
            if (!selectedProduct.material_count || selectedProduct.material_count <= 0) {
                setPendingProduct(selectedProduct);
                setNoMaterialDialogOpen(true);
                return;
            }
            setActiveStep((prev) => prev + 1);
        } else if (activeStep === 1) {
            if (!platform) {
                setError('请选择推广平台');
                return;
            }
            if (!startDate || !endDate || !startDate.isValid() || !endDate.isValid()) {
                setError('请选择有效的开始和结束日期');
                return;
            }
            if (endDate.isBefore(startDate)) {
                setError('结束日期不能早于开始日期');
                return;
            }
            if (!promotionCount || promotionCount <= 0 || promotionCount > selectedProduct?.material_count) {
                setError(`推广数量必须在 1 到 ${selectedProduct?.material_count || 0} 之间`);
                return;
            }

            setSubmitting(true);
            setApiError('');
            try {
                console.log("提交任务:", {
                    productId: selectedProduct.id_,
                    count: promotionCount,
                    platform: platform,
                    startDate: startDate.format('YYYY-MM-DD'),
                    endDate: endDate.format('YYYY-MM-DD'),
                });
                const response = await advPromotionTaskApi.create_promotion_task(
                    selectedProduct.id_,
                    promotionCount,
                    platform,
                    startDate.format('YYYY-MM-DD'),
                    endDate.format('YYYY-MM-DD'),
                );
                console.log("任务创建响应:", response);
                dispatch(addAlert({type: AlertType.SUCCESS, message: response?.message || AlertMsg.CREATE}));
                router.push('/protected/promotion-task');

            } catch (err) {
                console.error("创建任务失败:", err);
                const errorMsg = err?.response?.data?.message || err.message || '创建推广任务时出错';
                setApiError(errorMsg);
                setError('');
            } finally {
                setSubmitting(false);
            }
        }
    };

    const handleBack = () => {
        setError('');
        setApiError('');
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleProductSelect = (product) => {
        if (!product.material_count || product.material_count <= 0) {
            setPendingProduct(product);
            setNoMaterialDialogOpen(true);
            return;
        }
        setSelectedProduct(product);
        setError('');
        setPromotionCount(Math.min(1, product.material_count || 1));
    };

    const handlePromotionCountChange = (event) => {
        const value = parseInt(event.target.value, 10);
        const maxCount = selectedProduct?.material_count || 1;

        if (!isNaN(value)) {
            setPromotionCount(Math.max(1, Math.min(value, maxCount)));
        } else {
            setPromotionCount(1);
        }
    };

    const handlePlatformChange = (event) => setPlatform(event.target.value);
    const handleStartDateChange = (newValue) => setStartDate(newValue);
    const handleEndDateChange = (newValue) => setEndDate(newValue);

    const handleSearchInputChange = (event) => {
        setSearchQuery(event.target.value);
    };
    const handleSearchClick = () => {
        setAppliedSearchQuery(searchQuery);
        setCurrentPage(1);
    };
    const handlePageChange = (event, value) => {
        setCurrentPage(value);
    };

    const totalPages = Math.ceil(totalProducts / productsPerPage);

    const isNextDisabled = () => {
        if (activeStep === 0) {
            return !selectedProduct || loading || !selectedProduct.material_count || selectedProduct.material_count <= 0;
        }
        if (activeStep === 1) {
            const maxCount = selectedProduct?.material_count || 0;
            return (
                !platform ||
                !startDate || !endDate || !startDate.isValid() || !endDate.isValid() || endDate.isBefore(startDate) ||
                !promotionCount || promotionCount <= 0 || promotionCount > maxCount ||
                submitting || loading
            );
        }
        return false;
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{mb: 4}}>
                    <Button
                        startIcon={<ArrowLeft/>}
                        onClick={() => router.push('/protected/promotion-task')}
                        sx={{mb: 2, textTransform: 'none'}}
                    >
                        返回任务列表
                    </Button>
                    <Typography variant="h4" fontWeight="bold">
                        创建新任务
                    </Typography>
                </Box>

                <Paper
                    elevation={0}
                    sx={{
                        p: 4,
                        mb: 4,
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <Stepper activeStep={activeStep} sx={{mb: 4}}>
                        {steps.map((label) => (
                            <Step key={label}>
                                <StepLabel>{label}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>

                    {(error || apiError) && (
                        <Alert severity="error" sx={{mb: 3}}>
                            {error || apiError}
                        </Alert>
                    )}

                    {activeStep === 0 ? (
                        <>
                            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                                <Typography variant="h6">
                                    选择你要推广的产品
                                </Typography>
                                <Box sx={{display: 'flex', gap: 1}}>
                                    <TextField
                                        placeholder="搜索产品标题"
                                        size="small"
                                        value={searchQuery}
                                        onChange={handleSearchInputChange}
                                        onKeyPress={(ev) => {
                                            if (ev.key === 'Enter') {
                                                handleSearchClick();
                                                ev.preventDefault();
                                            }
                                        }}
                                        sx={{width: 250}}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Search size={20}/>
                                                </InputAdornment>
                                            ),
                                        }}
                                    />
                                    <Button
                                        variant="contained"
                                        onClick={handleSearchClick}
                                        disabled={loading}
                                        aria-label="搜索产品"
                                        sx={{minWidth: 0, px: 1.5}}
                                    >
                                        <Search size={20}/>
                                    </Button>
                                </Box>
                            </Box>
                            {loading ? (<Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200}}><CircularProgress/></Box>)
                                : products.length === 0 ? (<Alert severity="info" sx={{mb: 2}}>{appliedSearchQuery ? '没有找到匹配的产品' : '没有可用于创建推广任务的产品'}</Alert>)
                                    : (<>
                                        <Grid container spacing={3}>
                                            <Grid item xs={12} sm={6} md={4} lg={3} key="add-new-product">
                                                <Box
                                                    sx={{
                                                        height: '100%',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        borderStyle: 'dashed',
                                                        borderWidth: 2,
                                                        borderColor: 'primary.main',
                                                        backgroundColor: 'primary.50',
                                                        transition: 'all 0.3s',
                                                        '&:hover': {
                                                            transform: 'translateY(-8px)',
                                                            boxShadow: 3,
                                                            borderColor: 'primary.dark'
                                                        },
                                                        cursor: 'pointer',
                                                        aspectRatio: '3/4',
                                                        width: '100%'
                                                    }}
                                                    onClick={() => router.push('/protected/product-management/new')}
                                                >
                                                    <Box sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        bgcolor: 'primary.main',
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        mb: 2
                                                    }}>
                                                        <Plus size={30} color="#fff" />
                                                    </Box>
                                                    <Typography variant="h6" color="primary.main" sx={{fontWeight: 'medium'}}>
                                                        新增产品
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary" sx={{mt: 1}}>
                                                        添加产品继续生成
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                            {products.map((product) => (
                                                <Grid item xs={12} sm={6} md={4} lg={3} key={product.id_}>
                                                    <Box
                                                        sx={{
                                                            border: selectedProduct?.id_ === product.id_
                                                                ? `2px solid ${theme.palette.primary.main}`
                                                                : `1px solid ${theme.palette.divider}`,
                                                            borderRadius: 2,
                                                            height: '100%',
                                                            transition: 'all 0.2s',
                                                            cursor: 'pointer',
                                                            boxShadow: selectedProduct?.id_ === product.id_
                                                                ? `0 0 0 2px ${theme.palette.primary.light}`
                                                                : undefined,
                                                            '&:hover': {
                                                                transform: 'translateY(-4px)',
                                                                boxShadow: selectedProduct?.id_ !== product.id_ ? theme.shadows[3] : `0 0 0 2px ${theme.palette.primary.light}`,
                                                                borderColor: selectedProduct?.id_ !== product.id_ ? theme.palette.grey[400] : theme.palette.primary.main,
                                                            },
                                                        }}
                                                        onClick={() => handleProductSelect(product)}
                                                    >
                                                        <ProductCard
                                                            images={product.image_url ? [product.image_url] : ['https://placehold.co/300x200?text=No+Image']}
                                                        >
                                                            <Box sx={{p: 2}}>
                                                                <Typography variant="subtitle1" sx={{fontWeight: 'medium', mb: 1, noWrap: true}} title={product.title}>
                                                                    {product.title}
                                                                </Typography>
                                                                <Typography variant="body2" color="text.secondary">
                                                                    可用素材数: <Typography component="span" color="primary" fontWeight="bold">{product.material_count}</Typography>
                                                                </Typography>
                                                            </Box>
                                                        </ProductCard>
                                                    </Box>
                                                </Grid>
                                            ))}
                                        </Grid>
                                        {totalPages > 1 && (
                                            <Box sx={{display: 'flex', justifyContent: 'center', mt: 4}}>
                                                <Pagination
                                                    count={totalPages}
                                                    page={currentPage}
                                                    onChange={handlePageChange}
                                                    color="primary"
                                                    showFirstButton
                                                    showLastButton
                                                    disabled={loading}
                                                />
                                            </Box>
                                        )}
                                    </>)
                            }
                        </>
                    ) : (
                        <>
                            <Typography variant="h6" sx={{mb: 3}}>
                                设置推广详情
                            </Typography>

                            <Grid container spacing={4}>
                                <Grid item xs={12} md={4} lg={3}>
                                    <Paper variant="outlined" sx={{borderRadius: 2, overflow: 'hidden', height: '100%'}}>
                                        <ProductCard
                                            images={selectedProduct?.image_url ? [selectedProduct.image_url] : ['https://placehold.co/300x200?text=No+Image']}
                                        >
                                            <Box sx={{p: 2}}>
                                                <Typography variant="subtitle1" sx={{fontWeight: 'medium', mb: 1, noWrap: true}} title={selectedProduct?.title}>
                                                    {selectedProduct?.title || '未选择产品'}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary">
                                                    可用素材数: <Typography component="span" color="primary" fontWeight="bold">{selectedProduct?.material_count || 0}</Typography>
                                                </Typography>
                                            </Box>
                                        </ProductCard>
                                    </Paper>
                                </Grid>

                                <Grid item xs={12} md={8} lg={9}>
                                    <Paper variant="outlined" sx={{p: 3, borderRadius: 2, height: '100%'}}>
                                        <Stack spacing={3}>
                                            <FormControl fullWidth required>
                                                <InputLabel id="platform-select-label">推广平台</InputLabel>
                                                <Select
                                                    labelId="platform-select-label"
                                                    id="platform-select"
                                                    value={platform}
                                                    label="推广平台"
                                                    onChange={handlePlatformChange}
                                                >
                                                    <MenuItem value="小红书">小红书</MenuItem>
                                                </Select>
                                            </FormControl>

                                            <Paper variant="outlined" sx={{p: 3, borderRadius: 2}}>
                                                <Typography variant="subtitle1" fontWeight="medium" sx={{mb: 1}}>
                                                    推广数量
                                                </Typography>
                                                <TextField
                                                    label="设置数量"
                                                    type="number"
                                                    value={promotionCount}
                                                    onChange={handlePromotionCountChange}
                                                    InputProps={{
                                                        inputProps: {min: 1, max: selectedProduct?.material_count || 1}
                                                    }}
                                                    fullWidth
                                                    size="small"
                                                    helperText={`最多 ${selectedProduct?.material_count || 0} 个可用素材`}
                                                    sx={{mb: 2}}
                                                    required
                                                />
                                                <Slider
                                                    value={typeof promotionCount === 'number' && promotionCount > 0 ? promotionCount : 1}
                                                    onChange={(_, newValue) => setPromotionCount(newValue)}
                                                    min={1}
                                                    max={selectedProduct?.material_count || 1}
                                                    step={1}
                                                    marks
                                                    valueLabelDisplay="auto"
                                                    aria-labelledby="promotion-count-slider"
                                                    disabled={!selectedProduct || (selectedProduct.material_count || 0) <= 1}
                                                />
                                            </Paper>

                                            <Stack direction={{xs: 'column', sm: 'row'}} spacing={2}>
                                                <DatePicker
                                                    label="开始日期"
                                                    value={startDate}
                                                    onChange={handleStartDateChange}
                                                    minDate={dayjs()}
                                                    format="YYYY年MM月DD日"
                                                    slots={{textField: (params) => <TextField {...params} required fullWidth/>}}
                                                />
                                                <DatePicker
                                                    label="结束日期"
                                                    value={endDate}
                                                    onChange={handleEndDateChange}
                                                    minDate={startDate || dayjs()}
                                                    format="YYYY年MM月DD日"
                                                    slots={{textField: (params) => <TextField {...params} required fullWidth/>}}
                                                />
                                            </Stack>

                                            <Alert severity="info">
                                                请仔细核对推广详情，创建后任务的基本设置（如产品、平台）将无法修改。
                                            </Alert>
                                        </Stack>
                                    </Paper>
                                </Grid>
                            </Grid>
                        </>
                    )}

                    <Box sx={{display: 'flex', justifyContent: 'flex-end', mt: 6, pt: 3, borderTop: `1px solid ${theme.palette.divider}`}}>
                        <Button
                            disabled={activeStep === 0 || submitting}
                            onClick={handleBack}
                            sx={{mr: 1}}
                        >
                            上一步
                        </Button>
                        <Button
                            variant="contained"
                            endIcon={activeStep === steps.length - 1 ? <Check/> : <ArrowRight/>}
                            onClick={handleNext}
                            disabled={isNextDisabled()}
                        >
                            {activeStep === steps.length - 1 ? (submitting ? '创建中...' : '完成创建') : '下一步'}
                        </Button>
                        {submitting && (
                            <CircularProgress size={24} sx={{ml: 1, position: 'relative', top: '4px'}}/>
                        )}
                    </Box>
                </Paper>
            </Container>
            <Dialog open={noMaterialDialogOpen} onClose={() => setNoMaterialDialogOpen(false)}>
                <DialogTitle>该产品没有可用素材</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        该产品暂无可用素材，无法创建推广任务。是否前往 AI 图文生成页面为该产品生成素材？
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setNoMaterialDialogOpen(false)} color="inherit">取消</Button>
                    <Button onClick={() => {
                        setNoMaterialDialogOpen(false);
                        if (pendingProduct) {
                            router.push(`/protected/ai-image-text-generation/create?productId=${pendingProduct.id_}`);
                        }
                    }} color="primary" variant="contained">去生成素材</Button>
                </DialogActions>
            </Dialog>
        </LocalizationProvider>
    );
} 