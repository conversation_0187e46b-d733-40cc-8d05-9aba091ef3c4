import time

from mongoengine import *
from mongoengine.fields import (
    <PERSON>Field,
    ListField,
    IntField,
    EmbeddedDocumentField,
    FloatField,
    DictField,
    DynamicField,
    BooleanField,
)

from omni.mongo.mongo_client import init_mongo_client

init_mongo_client()


# 数据字典 / 配置项
class DataDictionary(Document):
    category = StringField()  # 字典类别
    key = StringField()  # 字典键
    value = DynamicField()  # 字典值

    meta = {
        "collection": "data_dictionary",
        "indexes": [
            {"fields": ("category", "key"), "unique": True}  # 确保同一类别下key唯一
        ],
        "strict": False,
    }


# 用户
class User(Document):
    username = StringField()  # 用户名
    password = StringField()  # 密码
    # 用户角色列表.
    # 可选值:
    # - advertiser(广告主)
    # - creator(流量主)
    # - captain(舰长)
    # - crew(舰员)
    # - admin(超级管理员)
    # 默认为 ['admin']
    roles = ListField(
        StringField(
            choices=(
                "user",
                "advertiser",
                "creator",
                "captain",
                "admin",
            )
        ),
    )
    crew_invite_code = StringField()  # 舰员邀请码
    create_at = IntField()  # 创建时间(时间戳)

    meta = {
        "collection": "user",
        "strict": False,
    }


# 账号
class Account(Document):
    user_id = StringField()  # 所属用户ID
    name = StringField()  # 账号名称
    platform = StringField(
        choices=("小红书", "抖音", "微博", "哔哩哔哩", "快手", "知乎")
    )  # 平台类型
    domain = StringField()  # 账号领域
    cookie = ListField(DictField())  # 修改字段类型: 存储原始 Cookie 对象列表
    status = StringField(
        choices=("在线", "离线", "封禁")
    )  # 账号状态 (中文)
    last_login_check_at = IntField()  # 最后一次登录检测时间
    create_at = IntField()  # 创建时间(时间戳)

    meta = {
        "collection": "account",
        "strict": False,
    }


# 账号笔记指标(定时爬取)
class AccountMetrics(Document):
    account_id = StringField()  # 关联的 Account ID
    platform = StringField()  # 平台 (例如: '小红书', '抖音')
    crawled_at = IntField(default=lambda: int(time.time()))  # 爬取时间戳
    # 单个笔记的数据
    title = StringField()  # 笔记/帖子标题
    publish_time = IntField()  # 笔记/帖子发布时间 (Unix 时间戳)
    view_count = IntField()  # 浏览量
    like_count = IntField()  # 点赞量
    comment_count = IntField()  # 评论量
    favorite_count = IntField()  # 收藏量
    share_count = IntField()  # 分享量

    meta = {
        "collection": "account_metrics",  # 更新集合名称
        "strict": False,
    }


# 图片信息 (用于嵌入到产品中)
class ImageInfo(EmbeddedDocument):
    oss_key = StringField()  # OSS Key
    order = IntField()  # 图片顺序


# 产品
class Product(Document):
    user_id = StringField()  # 所属用户ID
    title = StringField()  # 产品标题
    description = StringField()  # 产品描述
    domain = ListField(StringField())  # 产品所属领域列表
    images = ListField(EmbeddedDocumentField(ImageInfo))  # 产品图片列表
    create_at = IntField()  # 创建时间(时间戳)
    is_deleted = BooleanField(default=False)  # 删除标记

    meta = {
        "collection": "product",
        "strict": False,
    }


# AI基础素材管理
class AiBasicMaterial(Document):
    user_id = StringField()  # 用户ID
    platform = StringField(
        choices=("小红书", "抖音", "微博", "哔哩哔哩", "快手", "知乎")
    )  # 平台类型（如：小红书、抖音、微博等）
    domain = ListField(StringField())  # 素材所属领域列表
    share_url = StringField()  # 分享链接
    title = StringField()  # 素材标题
    content = StringField()  # 文本内容
    images = ListField(EmbeddedDocumentField(ImageInfo))  # 图片列表
    fetch_status = StringField(
        choices=("待爬取", "爬取中", "已完成", "失败"), default="待爬取"
    )  # 素材获取状态
    create_at = IntField()  # 创建时间(时间戳)
    is_deleted = BooleanField(default=False)  # 删除标记

    meta = {
        "collection": "ai_basic_material",
        "strict": False,
    }


# AI生成任务
class AiGenerationTask(Document):
    user_id = StringField()  # 用户ID
    product_id = StringField()  # 关联的产品ID (可选)
    task_name = StringField()  # 任务名称
    create_at = IntField()  # 创建时间(时间戳)
    is_deleted = BooleanField(default=False)  # 删除标记

    meta = {
        "collection": "ai_generation_task",
        "strict": False,
    }


# AI生成素材
class AiGeneratedMaterial(Document):
    user_id = StringField()  # 用户ID
    product_id = StringField()  # 关联的产品ID (可选)
    material_id = StringField()  # 关联的素材ID (可选)
    task_id = StringField()  # 关联的AI生成任务ID (可选)
    title = StringField()  # 素材标题
    content = StringField()  # 文本内容
    images = ListField(EmbeddedDocumentField(ImageInfo))  # 图片列表
    image_generation_status = StringField(
        choices=("待生成", "生成中", "已完成", "失败"), default="待生成"
    )  # 图片生成状态
    image_generation_fail_reason = StringField()  # 图片生成失败原因
    text_generation_status = StringField(
        choices=("待生成", "生成中", "已完成", "失败"), default="待生成"
    )  # 文本生成状态
    text_generation_fail_reason = StringField()  # 文本生成失败原因
    token_consumption = IntField()  # Token 消耗
    create_at = IntField()  # 创建时间(时间戳)
    is_deleted = BooleanField(default=False)  # 删除标记

    meta = {
        "collection": "ai_generated_material",
        "strict": False,
    }


# 产品推广任务
class PromotionTask(Document):
    user_id = StringField()  # 所属用户ID (广告主)
    product_id = StringField()  # 产品ID
    platform = StringField(
        choices=("小红书", "抖音", "微博", "哔哩哔哩", "快手", "知乎")
    )  # 发布平台
    start_date = IntField()  # 开始时间(时间戳)
    end_date = IntField()  # 结束时间(时间戳)
    create_at = IntField()  # 创建时间(时间戳)
    is_deleted = BooleanField(default=False)  # 删除标记

    meta = {
        "collection": "promotion_task",
        "strict": False,
    }


# 用户接取的推广任务
class PromotionTaskDetail(Document):
    # 关联信息
    promotion_task_id = StringField()  # 关联的 PromotionTask ID
    ai_generated_material_id = StringField()  # 关联的 AiGeneratedMaterial 的ID

    # 用户与账号信息 (当 user_id 为 null 时表示任务素材可用待分配)
    user_id = StringField()  # 接任务的用户ID (流量主) - Nullable
    account_id = StringField()  # 执行任务的账号ID
    accepted_at = IntField()  # 任务接取时间(时间戳)

    # 已发布作品信息
    publish_url = StringField()  # 发布链接
    publish_at = IntField()  # 发布时间(时间戳)

    # 验证状态
    validation_status = StringField(
        choices=("待验证", "成功", "失败"), default="待验证"
    )
    # 验证信息
    validation_details = StringField()  # 验证详情 (失败原因或错误信息)

    # 任务指标 (与 AccountMetrics 保持一致)
    view_count = IntField()  # 浏览量
    like_count = IntField()  # 点赞量
    comment_count = IntField()  # 评论量
    favorite_count = IntField()  # 收藏量
    share_count = IntField()  # 转发量

    meta = {
        "collection": "promotion_task_detail",
        "strict": False,
    }


# 产品流量统计
class ProductTraffic(Document):
    user_id = StringField()  # 广告主用户ID
    product_id = StringField()  # 产品ID
    platforms = ListField(StringField())  # 任务平台列表
    total_view_count = IntField(default=0)  # 总浏览量
    total_like_count = IntField(default=0)  # 总点赞量
    total_comment_count = IntField(default=0)  # 总评论量
    total_favorite_count = IntField(default=0)  # 总收藏量
    total_share_count = IntField(default=0)  # 总分享量
    last_updated_at = IntField(default=lambda: int(time.time()))  # 最后更新时间

    meta = {
        "collection": "product_traffic",  # 更新集合名称
        "indexes": [
            {
                "fields": ("user_id", "product_id"),
                "unique": True,
            }
        ],
        "strict": False,
    }


# 每日任务收益记录
class DailyTaskRevenue(Document):
    user_id = StringField()  # 用户ID
    promotion_task_detail_id = StringField()  # 关联的 PromotionTaskDetail ID
    date = IntField()  # 记录的日期 (当天零点的时间戳)
    daily_views = IntField(default=0)  # 此任务在当日新增的浏览量
    daily_revenue = IntField(default=0)  # 此任务在当日产生的收益金额
    status = StringField(
        choices=("未结算", "已结算"), default="未结算"
    )  # 收益状态 (未结算, 已结算)
    settled_at = IntField()  # 结算时间 (时间戳, 仅当 status 为 已结算 时有效)
    created_at = IntField(default=lambda: int(time.time()))  # 记录创建时间

    meta = {
        "collection": "daily_task_revenue",
        "indexes": [
            ("user_id", "date"),  # 方便按用户和日期查询
            ("promotion_task_detail_id", "date"),  # 方便查询特定任务的日收益
        ],
        "strict": False,
    }


# 舰员管理
class CrewManagement(Document):
    captain_user_id = StringField()  # 舰长用户ID
    crew_user_id = StringField()  # 舰员用户ID
    create_at = IntField()  # 创建时间(时间戳)

    meta = {
        "collection": "crew_management",
        "strict": False,
    }


# 舰长每日收益记录
class CaptainDailyRevenue(Document):
    captain_user_id = StringField()  # 舰长用户ID
    crew_user_id = StringField()  # 舰员用户ID
    date = IntField()  # 出账日期 (当天零点的时间戳)
    daily_revenue = IntField()  # 舰长从该舰员本日获得的佣金金额 (单位: 元)
    daily_task_revenue_ids = ListField(
        StringField()
    )  # 关联的 DailyTaskRevenue 记录 ID 列表
    status = StringField(
        choices=("未结算", "已结算"), default="未结算"
    )  # 结算状态
    settled_at = IntField()  # 结算时间(时间戳, 仅当 status 为 已结算 时有效)

    meta = {
        "collection": "captain_daily_revenue",  # 更新集合名称
        "strict": False,
    }
