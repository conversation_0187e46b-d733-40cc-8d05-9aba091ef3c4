const {app, BrowserWindow} = require('electron');
const path = require('path');
const ipcMainHandler = require('./ipcHandler/ipcMainHandler');
const {setupAutoUpdater} = require("./autoUpdater");
const {ENV_CONFIG} = require("./env");
const {registerIpcHandlers} = require('./ipcHandler/ipcHandlers');
// 创建主窗口
const createWindow = () => {
    const mainWindow = new BrowserWindow({
        width: 800,
        height: 600,
        icon: path.join(__dirname, 'logo.png'),
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: false,
            contextIsolation: true
        }
    });

    mainWindow.maximize();
    mainWindow.loadURL(ENV_CONFIG.BASE_URL).then(() => {
        if (ENV_CONFIG.ENV === 'dev') {
            mainWindow.webContents.openDevTools();
        }
    });
};

// 主程序开始
app.whenReady().then(() => {
    createWindow();
    if (ENV_CONFIG.ENV === 'prod') {
        setupAutoUpdater()
    }
    registerIpcHandlers(ipcMainHandler);
});

app.on('window-all-closed', () => {
    app.quit();
});
