import time
from datetime import datetime

from mongoengine import Q, DoesNotExist

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.log.log import olog
from repository.models import (
    AiGeneratedMaterial,
    PromotionTaskDetail,
    Product,
    PromotionTask,
    Account,  # 引入 Account 用于获取平台选项
    AiGenerationTask # <-- 添加 AiGenerationTask 导入
)


# Define timestamp_to_str function directly
def timestamp_to_str(timestamp: int | float | None, format="%Y-%m-%d %H:%M:%S") -> str | None:
    """Converts a Unix timestamp to a formatted string."""
    if timestamp is None:
        return None
    try:
        dt_object = datetime.fromtimestamp(int(timestamp))
        return dt_object.strftime(format)
    except (TypeError, ValueError):
        # Handle potential errors if timestamp is not a valid number
        olog.error(f"转换时间戳时出错: {timestamp}")
        return None


def get_available_material_ids(product_id: str) -> set[str]:
    """辅助函数：获取指定产品当前可用的素材ID集合。"""
    if not product_id:
        return set()

    try:
        # 1. 获取所有状态为已完成且未被删除的候选素材
        candidate_materials_query = AiGeneratedMaterial.objects.filter(
            product_id=product_id,
            image_generation_status='已完成',
            text_generation_status='已完成',
            is_deleted=False
        )
        # olog.debug(f"产品 {product_id} 初始候选素材查询结果数: {len(candidate_materials_query)}")

        if not candidate_materials_query:
            # olog.debug(f"产品 {product_id} 没有找到任何状态为已完成且未删除的 AiGeneratedMaterial。")
            return set()

        # 2. 提取这些素材关联的 task_id (如果存在且不为空)
        task_ids_from_materials = {
            mat.task_id for mat in candidate_materials_query if mat.task_id and mat.task_id.strip()
        }
        # olog.debug(f"产品 {product_id} 的候选素材中提取到的 task_ids: {task_ids_from_materials}")

        # 3. 查询这些 task_id 中哪些是未被删除的 AiGenerationTask
        valid_task_ids = set()
        if task_ids_from_materials:
            valid_tasks_query = AiGenerationTask.objects.filter(
                id__in=list(task_ids_from_materials),
                is_deleted=False
            ).scalar('id')  # 只获取 ID
            valid_task_ids = {str(tid) for tid in valid_tasks_query}
            # olog.debug(f"产品 {product_id} 的有效（未删除）的 AiGenerationTask IDs: {valid_task_ids}")

        # 4. 筛选素材：保留那些 (a) 没有 task_id 的，或者 (b) 其 task_id 存在于 valid_task_ids 中的
        completed_material_ids = set()
        for mat in candidate_materials_query:
            # 条件a: 素材没有关联 task_id (或者 task_id 为空字符串)
            if not mat.task_id or not mat.task_id.strip():
                completed_material_ids.add(str(mat.id))
            # 条件b: 素材关联的 task_id 存在且有效（未被删除）
            elif mat.task_id in valid_task_ids:
                completed_material_ids.add(str(mat.id))
        # olog.debug(f"产品 {product_id} 经过 AiGenerationTask 删除状态过滤后的素材 IDs: {completed_material_ids}")

        if not completed_material_ids:
            # olog.debug(f"产品 {product_id} 在 AiGenerationTask 过滤后没有可用素材。")
            return set()

        # 5. 获取这些素材中已被锁定的 ID (即已在 PromotionTaskDetail 中使用的)
        locked_details_query = PromotionTaskDetail.objects.filter(
            ai_generated_material_id__in=list(completed_material_ids)
        ).scalar('ai_generated_material_id')  # 只获取被锁定的素材 ID
        #确保 locked_material_ids 中的 ID 都是字符串，以便与 completed_material_ids 进行集合运算
        locked_material_ids = {str(material_id) for material_id in locked_details_query}
        # olog.debug(f"产品 {product_id} 已锁定的素材 IDs: {locked_material_ids}")

        # 6. 计算最终可用的素材 ID (已完成、任务未删、且未被锁定)
        available_material_ids_set = completed_material_ids - locked_material_ids
        olog.info(f"产品 {product_id} 最终可用素材 IDs: {available_material_ids_set} (数量: {len(available_material_ids_set)})")
        return available_material_ids_set

    except Exception as e:
        olog.error(f"计算产品 {product_id} 的可用素材ID时出错: {e}", exc_info=True)
        # 出错时返回空集合，避免引发上层异常
        return set()


@register_handler('adv_promotion_task')  # Updated handler name
class AdvPromotionTaskApi:

    @auth_required(['admin', 'advertiser'])
    def query_product_overview(self, data: dict):
        user_id = data.get('user_id')
        page = data.get('page', 0)
        limit = data.get('limit', 10)
        search_filter = data.get('search')

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        try:
            page = int(page)
            limit = int(limit)
            if page < 0 or limit <= 0:
                raise ValueError("Page and limit must be non-negative.")
        except (ValueError, TypeError):
            raise MException("分页参数 'page' 和 'limit' 必须是有效的非负整数。")

        skip = page * limit

        # --- 1. 构建 Product 查询条件 ---
        product_query = Q(user_id=user_id) & Q(is_deleted=False)
        if search_filter:
            product_query &= Q(title__icontains=search_filter)

        # --- 2. 获取总数并进行分页查询 ---
        try:
            # 先获取满足条件的总产品数
            total_count = Product.objects(product_query).count()

            # 然后应用排序和分页获取当前页的产品
            paginated_products = Product.objects(product_query) \
                .only('id', 'title', 'images', 'create_at') \
                .order_by('-create_at') \
                .skip(skip) \
                .limit(limit)
        except Exception as e:
            olog.error(f"查询产品时出错 (user_id: {user_id}, filter: {search_filter}): {e}")
            raise MException("查询产品列表时出错")

        # --- 3. 为当前页产品计算可用素材数并格式化结果 ---
        result_list = []
        if paginated_products:
            oss_client = OSSClient.getInstance()
            # 预先获取本页所有产品的 Product ID 字符串列表
            product_ids_on_page = [str(p.id) for p in paginated_products]

            # --- 遍历当前页的产品，计算可用数并组装结果 ---
            for product in paginated_products:
                product_id_str = str(product.id)
                # 调用辅助函数获取可用素材ID并计算数量
                available_material_ids = get_available_material_ids(product_id_str)
                available_material_count = len(available_material_ids)

                # 处理图片签名
                signed_image_url = None
                if product.images:
                    image_to_use = next((img for img in product.images if hasattr(img, 'order') and img.order == 1), product.images[0])
                    if hasattr(image_to_use, 'oss_key'):
                        oss_key = image_to_use.oss_key
                        if oss_key:
                            try:
                                signed_image_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                            except Exception as e:
                                olog.error(f"为 key {oss_key} 签名 URL 时出错: {e}")

                result_list.append({
                    'id_': product_id_str,
                    'title': product.title,
                    'image_url': signed_image_url,
                    'material_count': available_material_count  # 显示计算出的数量，可能为 0
                })

        # --- 4. 返回结果 ---
        return {
            'products': result_list,
            'total_count': total_count  # 返回的是 Product 的总数
        }

    @auth_required(['admin', 'advertiser'])
    def create_promotion_task(self, data: dict):
        user_id = data.get('user_id')
        product_id = data.get('product_id')
        count = data.get('count')
        platform = data.get('platform')
        start_date_str = data.get('start_date')  # 前端传字符串 YYYY-MM-DD
        end_date_str = data.get('end_date')  # 前端传字符串 YYYY-MM-DD

        # --- 参数校验 ---
        if not product_id or not isinstance(count, int) or count <= 0:
            raise MException("缺少必要的参数 'product_id' 或 'count' 无效")
        if not platform:
            raise MException("必须选择发布平台")
        if platform not in Account.platform.choices:
            raise MException(f"无效的平台类型: {platform}")
        if not start_date_str or not end_date_str:
            raise MException("必须提供任务开始和结束日期")

        # 将日期字符串转换为时间戳 (假设 YYYY-MM-DD 格式)
        try:
            # 注意：这里假设日期是当天 00:00:00 开始，结束日期是当天 23:59:59 结束
            # 如果需要更精确的时间，前端应传递带时间的字符串或时间戳
            start_timestamp = int(time.mktime(time.strptime(start_date_str, "%Y-%m-%d")))
            # 结束时间戳设为当天最后一秒
            end_timestamp = int(time.mktime(time.strptime(end_date_str + " 23:59:59", "%Y-%m-%d %H:%M:%S")))
            if end_timestamp < start_timestamp:
                raise MException("结束日期不能早于开始日期")
        except ValueError:
            raise MException("日期格式无效，请使用 YYYY-MM-DD 格式")

        # --- 查找产品 ---
        try:
            product = Product.objects.get(id=product_id, user_id=user_id)
        except DoesNotExist:
            raise MException(f"未找到属于您的产品，ID: {product_id}")
        except Exception as e:
            # 考虑记录更详细的错误日志
            olog.error(f"查找产品 {product_id} 时出错: {e}")
            raise MException(f"查找产品时出错: {e}")

        # --- 高效查找可用素材 ---
        # 调用辅助函数获取可用素材ID
        available_material_ids_set = get_available_material_ids(product_id)
        available_material_count = len(available_material_ids_set)

        if not available_material_ids_set:
            raise MException(f"产品 '{product.title}' 没有可用的已完成素材。")

        if available_material_count < count:
            raise MException(f"产品 '{product.title}' 可用的素材数量 ({available_material_count}) 不足以满足请求的数量 ({count})。")

        # 从可用 ID 集合中选择所需数量
        material_ids_to_use = list(available_material_ids_set)[:count]

        # --- 创建任务 (使用新参数) ---
        try:
            promotion_task = PromotionTask(
                user_id=user_id,
                product_id=product_id,
                platform=platform,  # 使用传入的平台
                start_date=start_timestamp,  # 使用转换后的开始时间戳
                end_date=end_timestamp,  # 使用转换后的结束时间戳
                create_at=int(time.time())
            )
            promotion_task.save()
            promotion_task_id = str(promotion_task.id)

            task_details_to_create = []
            for material_id in material_ids_to_use:
                task_detail = PromotionTaskDetail(
                    promotion_task_id=promotion_task_id,
                    ai_generated_material_id=material_id,
                    user_id=None,
                    account_id=None
                )
                task_details_to_create.append(task_detail)

            if task_details_to_create:
                PromotionTaskDetail.objects.insert(task_details_to_create)

            return {
                'message': '推广任务创建成功',
                'promotion_task_id': promotion_task_id,
                'promotion_task_details_created': len(task_details_to_create)
            }

        except Exception as e:
            # 简单的回滚逻辑：如果 UserPromotionTask 创建失败，尝试删除已创建的 ProductPromotionTask
            # 注意：这不是严格的事务回滚
            if 'promotion_task' in locals() and hasattr(promotion_task, 'id') and promotion_task.id:
                try:
                    PromotionTask.objects(id=promotion_task.id).delete()
                except Exception as delete_err:
                    # 实际应用中应使用更健壮的日志记录
                    olog.warning(f"警告：回滚 PromotionTask {promotion_task.id} 失败: {delete_err}")
            olog.error(f"创建推广任务时发生错误: {e}")
            raise MException(f"创建推广任务时发生错误: {e}")

    @auth_required(['admin', 'advertiser'])
    def query_promotion_tasks(self, data: dict):
        user_id = data.get('user_id')
        page = data.get('page', 0)
        limit = data.get('limit', 10)

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        try:
            page = int(page)
            limit = int(limit)
            if page < 0 or limit <= 0:
                raise ValueError("Page and limit must be non-negative.")
        except (ValueError, TypeError):
            raise MException("分页参数 'page' 和 'limit' 必须是有效的非负整数。")

        skip = page * limit

        # --- 1. 查询总任务数 ---
        try:
            total_count = PromotionTask.objects(user_id=user_id, is_deleted=False).count()
        except Exception as e:
            olog.error(f"查询用户 {user_id} 的推广任务总数时出错: {e}")
            raise MException("查询任务总数时出错")

        # --- 2. 分页查询推广任务 ---
        try:
            paginated_tasks = PromotionTask.objects(user_id=user_id, is_deleted=False) \
                .order_by('-create_at') \
                .skip(skip) \
                .limit(limit)
        except Exception as e:
            olog.error(f"分页查询用户 {user_id} 的推广任务时出错: {e}")
            raise MException("查询推广任务列表时出错")

        # --- 3. 准备结果，包含产品信息和计数 ---
        result_list = []
        oss_client = OSSClient.getInstance()

        # 优化：预先获取本页所有任务的产品ID和任务ID
        product_ids = [task.product_id for task in paginated_tasks]
        task_ids = [str(task.id) for task in paginated_tasks]

        # 优化：批量查询产品信息
        products_info = {}
        if product_ids:
            try:
                products = Product.objects(id__in=product_ids).only('id', 'title', 'images')
                products_info = {str(p.id): {'title': p.title, 'images': p.images} for p in products}
            except Exception as e:
                olog.error(f"批量查询产品信息 {product_ids} 时出错: {e}")
                # 出错时继续，产品信息将显示为默认值

        # 优化：批量查询任务详情计数
        total_detail_counts = {task_id: 0 for task_id in task_ids}  # 初始化计数
        completed_detail_counts = {task_id: 0 for task_id in task_ids}  # 初始化计数
        if task_ids:
            try:
                # 一次性查询本页任务关联的所有详情
                details_cursor = PromotionTaskDetail.objects.filter(
                    promotion_task_id__in=task_ids
                ).only('promotion_task_id', 'publish_url')  # 只获取需要的字段

                # 在内存中进行计数
                for detail in details_cursor:
                    task_id = detail.promotion_task_id
                    if task_id in total_detail_counts:
                        total_detail_counts[task_id] += 1
                        # 检查 publish_url 是否有效来判断是否完成
                        if detail.publish_url and detail.publish_url.strip():
                            completed_detail_counts[task_id] += 1

            except Exception as e:
                olog.error(f"批量查询任务详情 {task_ids} 时出错: {e}")
                # 出错时继续，计数将保持为 0

        # --- 4. 组装结果列表 ---
        for task in paginated_tasks:
            task_id_str = str(task.id)
            product_info = products_info.get(task.product_id, {})
            product_name = product_info.get('title', "未知产品")
            images_list = product_info.get('images')
            signed_image_url = None

            if images_list:
                # 优先选择 order=1 的图片，否则选择第一个
                image_to_use = next((img for img in images_list if hasattr(img, 'order') and img.order == 1), images_list[0] if images_list else None)
                if image_to_use and hasattr(image_to_use, 'oss_key'):
                    oss_key = image_to_use.oss_key
                    if oss_key:
                        try:
                            signed_image_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                        except Exception as e:
                            olog.error(f"为 key {oss_key} 签名 URL 时出错: {e}")

            # 从计数字典中获取值
            task_total_count = total_detail_counts.get(task_id_str, 0)
            task_completed_count = completed_detail_counts.get(task_id_str, 0)

            result_list.append({
                'id_': task_id_str,
                'productName': product_name,
                'productImage': signed_image_url,
                'platform': task.platform,
                'taskCount': task_total_count,
                'completedCount': task_completed_count,
                'createdAt': timestamp_to_str(task.create_at, format="%Y-%m-%d %H:%M"),
            })

        # --- 5. 返回结果 ---
        return {
            'tasks': result_list,
            'total_count': total_count
        }

    @auth_required(['admin', 'advertiser'])
    def query_promotion_task_detail(self, data: dict):
        user_id = data.get('user_id')
        task_id = data.get('task_id')

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")
        if not task_id:
            raise MException("缺少必要的参数 'task_id'")

        try:
            # 1. 查询产品推广任务本身
            task = PromotionTask.objects.get(id=task_id, user_id=user_id, is_deleted=False)  # 增加 is_deleted 判断
        except DoesNotExist:
            raise MException(f"未找到 ID 为 {task_id} 的推广任务或您无权访问")
        except Exception as e:
            olog.error(f"查询 PromotionTask {task_id} 时出错: {e}")
            raise MException("查询任务详情时出错")

        oss_client = OSSClient.getInstance()

        # 2. 查询关联的产品信息 和 计算可用素材数
        product_name = "未知产品"
        product_image_url = None
        product_id_str = task.product_id  # 获取产品ID
        available_material_count = 0  # 初始化可用素材数

        try:
            product = Product.objects.only('id', 'title', 'images').get(id=product_id_str)  # 使用 only 获取必要字段
            product_name = product.title
            if product.images:
                image_to_use = next((img for img in product.images if hasattr(img, 'order') and img.order == 1), product.images[0] if product.images else None)
                if image_to_use and hasattr(image_to_use, 'oss_key'):
                    oss_key = image_to_use.oss_key
                    if oss_key:
                        try:
                            product_image_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                        except Exception as e:
                            olog.error(f"为产品图片 key {oss_key} 签名 URL 时出错: {e}")

            # --- 计算该产品的可用素材数 ---
            try:
                # 调用辅助函数获取可用素材ID并计算数量
                available_material_ids = get_available_material_ids(product_id_str)
                available_material_count = len(available_material_ids)
            except Exception as e:  # 辅助函数内部处理了异常，这里可以简化
                olog.error(f"调用 get_available_material_ids 计算产品 {product_id_str} 可用素材时出错: {e}")
                available_material_count = 0  # 保守设置为0

        except DoesNotExist:
            olog.warning(f"警告: 未找到推广任务 {task_id} 关联的产品 {product_id_str}")
        except Exception as e:
            olog.error(f"查询产品 {product_id_str} 信息时出错: {e}")
            # 出错时，产品信息为默认值，可用素材数为 0

        # 3. 查询关联的任务详情数量统计 (不再查询具体素材列表)
        total_count = 0
        completed_count = 0
        try:
            # 只需统计数量，不需要获取具体内容
            details_cursor = PromotionTaskDetail.objects.filter(
                promotion_task_id=task_id
            ).only('publish_url')  # 只需要 publish_url 来判断是否完成

            details_list = list(details_cursor)  # 获取列表以便迭代
            total_count = len(details_list)
            completed_count = sum(1 for detail in details_list if detail.publish_url and detail.publish_url.strip())

        except Exception as e:
            olog.error(f"统计任务 {task_id} 的详情数量时出错: {e}")
            # 出错时保持计数为 0

        in_progress_count = total_count - completed_count  # 进行中 = 总数 - 已完成

        # 4. 组装任务描述
        description = f"这是产品 {product_name} 的发布任务，计划在 {task.platform} 平台发布 {total_count} 个素材。"

        # 5. 组装最终返回结果 (包含可用素材数)
        return {
            'id_': task_id,
            'productName': product_name,
            'productImage': product_image_url,
            'taskCount': total_count,
            'completedCount': completed_count,
            'inProgressCount': max(0, in_progress_count),  # 确保不为负数
            'availableMaterialCount': available_material_count,  # 新增：返回可用素材数
            'createdAt': timestamp_to_str(task.create_at, format="%Y-%m-%d %H:%M"),
            'startDate': timestamp_to_str(task.start_date, format="%Y-%m-%d"),  # 返回开始日期
            'endDate': timestamp_to_str(task.end_date, format="%Y-%m-%d"),  # 返回结束日期
            'platform': task.platform,
            'description': description,
            # 'materials': material_details_list, # 不再返回素材列表
        }

    @auth_required(['admin', 'advertiser'])
    def query_promotion_task_materials(self, data: dict):
        user_id = data.get('user_id')  # 确保权限检查（虽然此方法内未直接使用 user_id 进行数据过滤）
        task_id = data.get('task_id')
        page = data.get('page', 0)
        limit = data.get('limit', 5)  # 默认每页 5 个

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")
        if not task_id:
            raise MException("缺少必要的参数 'task_id'")

        try:
            page = int(page)
            limit = int(limit)
            if page < 0 or limit <= 0:
                raise ValueError("Page and limit must be non-negative.")
        except (ValueError, TypeError):
            raise MException("分页参数 'page' 和 'limit' 必须是有效的非负整数。")

        skip = page * limit

        # 检查任务是否存在且属于该用户 (可选，但增加安全性)
        try:
            PromotionTask.objects.get(id=task_id, user_id=user_id, is_deleted=False)
        except DoesNotExist:
            raise MException(f"未找到 ID 为 {task_id} 的推广任务或您无权访问")
        except Exception as e:
            olog.error(f"检查 PromotionTask {task_id} 权限时出错: {e}")
            raise MException("检查任务权限时出错")

        oss_client = OSSClient.getInstance()
        material_details_list = []
        total_material_count = 0

        try:
            # 1. 查询总的关联详情数量
            total_material_count = PromotionTaskDetail.objects.filter(
                promotion_task_id=task_id
            ).count()

            # 2. 分页查询关联的 PromotionTaskDetail
            paginated_task_details = list(PromotionTaskDetail.objects.filter(
                promotion_task_id=task_id
            ).only('ai_generated_material_id', 'publish_url', 'publish_at')  # 包含需要字段
                                          .order_by('id')  # 添加排序以确保分页一致性
                                          .skip(skip)
                                          .limit(limit))

            material_ids = [td.ai_generated_material_id for td in paginated_task_details]
            material_details_map = {}

            if material_ids:
                # 3. 批量查询对应的素材信息
                materials_cursor = AiGeneratedMaterial.objects.filter(
                    id__in=material_ids
                ).only('id', 'title', 'images')  # 只需要 id, title, images

                # 构建素材信息映射
                for mat in materials_cursor:
                    signed_image_urls = []
                    if mat.images:
                        for img_info in mat.images:
                            if hasattr(img_info, 'oss_key'):
                                oss_key = img_info.oss_key
                                if oss_key:
                                    try:
                                        signed_url = oss_client.signed_url(SignedMethod.GET, oss_key)
                                        signed_image_urls.append(signed_url)
                                    except Exception as e:
                                        olog.error(f"为素材图片 key {oss_key} 签名 URL 时出错: {e}")
                    material_details_map[str(mat.id)] = {
                        'title': mat.title,
                        'images': signed_image_urls
                    }

            # 4. 组装当前页的素材列表
            for td in paginated_task_details:
                mat_id_str = td.ai_generated_material_id
                mat_details = material_details_map.get(mat_id_str)
                if mat_details:
                    # 根据 publish_url 判断状态
                    is_published = bool(td.publish_url and td.publish_url.strip())
                    # 注意：这里没有"发布中"状态，只有"已发布"或"待发布"
                    material_status = '已发布' if is_published else '待发布'
                    published_at = timestamp_to_str(td.publish_at) if is_published and td.publish_at else None

                    material_details_list.append({
                        'id_': mat_id_str,  # 使用素材 ID 作为唯一标识
                        'title': mat_details['title'],
                        'images': mat_details['images'],
                        'status': material_status,
                        'publishedAt': published_at,
                    })
                else:
                    # 如果找不到对应的 AiGeneratedMaterial，可以记录警告或跳过
                    olog.warning(f"未找到 PromotionTaskDetail {td.id} 关联的 AiGeneratedMaterial {mat_id_str}")


        except Exception as e:
            olog.error(f"分页查询任务 {task_id} 的素材列表时出错 (page: {page}, limit: {limit}): {e}")
            raise MException("查询素材列表时出错")  # 向上抛出异常，让前端知道出错了

        # 5. 返回分页结果和总数
        return {
            'materials': material_details_list,
            'total_count': total_material_count
        }

    @auth_required(['admin', 'advertiser'])
    def delete_promotion_task(self, data: dict):
        user_id = data.get('user_id')
        task_id = data.get('task_id')

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")
        if not task_id:
            raise MException("缺少必要的参数 'task_id'")

        try:
            # 查找任务，确保它存在并且属于该用户，并且尚未被删除
            task = PromotionTask.objects.get(id=task_id, user_id=user_id, is_deleted=False)
        except DoesNotExist:
            # 如果任务不存在或已被删除，可以考虑返回成功或特定消息，因为目标状态（不存在）已达到
            # 或者抛出异常让前端知道任务找不到
            raise MException(f"未找到 ID 为 {task_id} 的推广任务或已被删除")
        except Exception as e:
            olog.error(f"查找待删除的 PromotionTask {task_id} 时出错: {e}")
            raise MException("查找任务时出错")

        try:
            # 标记为删除，而不是物理删除
            task.is_deleted = True
            task.save()
            olog.info(f"用户 {user_id} 标记删除推广任务 {task_id}")
            return {'message': '推广任务已成功删除'}
        except Exception as e:
            olog.error(f"标记删除 PromotionTask {task_id} 时出错: {e}")
            raise MException("删除任务时出错")

    @auth_required(['admin', 'advertiser'])
    def append_promotion_task(self, data: dict):
        user_id = data.get('user_id')
        task_id = data.get('task_id')
        additional_count = data.get('additional_count')
        new_end_date_str = data.get('new_end_date')  # 前端传 "YYYY-MM-DD"

        # --- 参数校验 ---
        if not user_id:
            raise MException("缺少必要的参数 'user_id'")
        if not task_id:
            raise MException("缺少必要的参数 'task_id'")
        if not isinstance(additional_count, int) or additional_count <= 0:
            raise MException("追加数量 'additional_count' 必须是大于0的整数")
        if new_end_date_str:
            try:
                # 尝试解析日期，如果格式不对会抛出 ValueError
                new_end_timestamp = int(time.mktime(time.strptime(new_end_date_str + " 23:59:59", "%Y-%m-%d %H:%M:%S")))
            except ValueError:
                raise MException("结束日期格式无效，请使用 YYYY-MM-DD 格式")
        else:
            new_end_timestamp = None  # 如果不传，则不更新结束日期

        # --- 1. 查找现有的推广任务 ---
        try:
            task = PromotionTask.objects.get(id=task_id, user_id=user_id, is_deleted=False)
        except DoesNotExist:
            raise MException(f"未找到 ID 为 {task_id} 的推广任务或您无权访问")
        except Exception as e:
            olog.error(f"查找 PromotionTask {task_id} 时出错: {e}")
            raise MException("查找现有任务时出错")

        # 校验新的结束日期不能早于现有结束日期
        if new_end_timestamp is not None and new_end_timestamp < task.end_date:
            raise MException(f"新的结束日期 ({new_end_date_str}) 不能早于当前结束日期 ({timestamp_to_str(task.end_date, '%Y-%m-%d')})")

        product_id = task.product_id

        # --- 2. 查找该产品当前可用的素材 ---
        try:
            # 调用辅助函数获取可用素材ID
            available_material_ids_set = get_available_material_ids(product_id)
            current_available_count = len(available_material_ids_set)
        except Exception as e:  # 辅助函数内部处理了异常，这里可以简化
            olog.error(f"调用 get_available_material_ids 为产品 {product_id} 查找可用素材时出错: {e}")
            raise MException("查找可用素材时出错")  # 保持原有错误抛出

        if current_available_count < additional_count:
            raise MException(f"当前可用素材数量 ({current_available_count}) 不足以满足追加的数量 ({additional_count})。")

        # --- 3. 选择要追加的素材 ---
        material_ids_to_add = list(available_material_ids_set)[:additional_count]

        # --- 4. 创建新的任务详情记录 ---
        try:
            task_details_to_create = []
            for material_id in material_ids_to_add:
                task_detail = PromotionTaskDetail(
                    promotion_task_id=task_id,
                    ai_generated_material_id=material_id,
                    user_id=None,  # 保持未分配状态
                    account_id=None
                )
                task_details_to_create.append(task_detail)

            if task_details_to_create:
                PromotionTaskDetail.objects.insert(task_details_to_create)
                olog.info(f"为任务 {task_id} 追加了 {len(task_details_to_create)} 个素材")

            # --- 5. 更新任务的结束日期 (如果提供了新的日期) ---
            if new_end_timestamp is not None and new_end_timestamp > task.end_date:
                task.end_date = new_end_timestamp
                task.save()
                olog.info(f"更新任务 {task_id} 的结束日期为 {new_end_date_str}")

            return {
                'message': '推广任务追加成功',
                'task_id': task_id,
                'details_added': len(task_details_to_create),
                'end_date_updated': new_end_timestamp is not None and new_end_timestamp > task.end_date
            }

        except Exception as e:
            # 注意: 如果插入详情失败，没有简单的回滚。实际应用可能需要更复杂的事务管理或补偿逻辑
            olog.error(f"追加推广任务 {task_id} 时发生错误: {e}")
            raise MException(f"追加推广任务时发生错误: {e}")
