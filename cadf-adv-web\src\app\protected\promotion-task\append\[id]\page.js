"use client"

import React, {useEffect, useState} from 'react';
import {<PERSON>ert, Box, Button, CircularProgress, Container, Grid, Paper, Slider, Stack, TextField, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {ArrowLeft, Check} from 'lucide-react';
import {useParams, useRouter} from 'next/navigation';
import ProductCard from '@/components/ProductCard';
import {useDispatch} from "react-redux";
import {addAlert} from "@/core/components/redux/alert-slice";
import {AlertType} from "@/core/components/alert";
import {advPromotionTaskApi} from "@/api/adv-promotion-task-api";
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DatePicker} from '@mui/x-date-pickers/DatePicker';

dayjs.locale('zh-cn');

export default function AppendTaskPage() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams(); // Use useParams hook
    const dispatch = useDispatch();
    const taskId = params.id; // Get task ID from URL

    const [task, setTask] = useState(null);
    const [additionalCount, setAdditionalCount] = useState(1);
    const [newEndDate, setNewEndDate] = useState(null); // Initially null
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [apiError, setApiError] = useState('');

    useEffect(() => {
        const fetchTask = async () => {
            if (!taskId) return;
            setLoading(true);
            setApiError('');
            try {
                const response = await advPromotionTaskApi.queryPromotionTaskDetail(taskId);
                if (response && typeof response === 'object' && Object.keys(response).length > 0) {
                    setTask(response);
                    // Set initial end date for the DatePicker
                    setNewEndDate(response.endDate ? dayjs(response.endDate) : dayjs());
                    // Reset additional count if available count is 0
                    if (response.availableMaterialCount <= 0) {
                        setAdditionalCount(0);
                    }
                } else {
                    console.error('API响应无效或为空:', response);
                    setApiError("获取任务详情失败: 响应数据无效或为空");
                    setTask(null);
                }
            } catch (err) {
                console.error('获取任务详情失败:', err);
                const errorMessage = err?.response?.data?.message || err?.message || "获取任务详情时发生未知错误";
                setApiError(errorMessage);
                setTask(null);
            } finally {
                setLoading(false);
            }
        };
        fetchTask();
    }, [taskId]);

    const handleAdditionalCountChange = (event) => {
        const value = parseInt(event.target.value, 10);
        const maxCount = task?.availableMaterialCount || 0;

        if (!isNaN(value)) {
            setAdditionalCount(Math.max(0, Math.min(value, maxCount))); // Allow 0
        } else {
            setAdditionalCount(0);
        }
    };

    const handleEndDateChange = (newValue) => setNewEndDate(newValue);

    const handleSubmit = async () => {
        setError('');
        setApiError('');

        const currentEndDate = task?.endDate ? dayjs(task.endDate) : dayjs();
        const maxAvailable = task?.availableMaterialCount || 0;

        if (additionalCount <= 0) {
            setError('追加数量必须大于 0');
            return;
        }
        if (additionalCount > maxAvailable) {
            setError(`追加数量不能超过当前可用素材数 (${maxAvailable})`);
            return;
        }
        if (!newEndDate || !newEndDate.isValid()) {
            setError('请选择一个有效的结束日期');
            return;
        }
        // Ensure new end date is not before the current end date
        if (newEndDate.isBefore(currentEndDate, 'day')) {
            setError(`新的结束日期不能早于当前结束日期 (${currentEndDate.format('YYYY-MM-DD')})`);
            return;
        }

        setSubmitting(true);
        try {
            const formattedEndDate = newEndDate.format('YYYY-MM-DD');
            console.log("提交追加任务:", {
                taskId: taskId,
                additionalCount: additionalCount,
                newEndDate: formattedEndDate,
            });

            const response = await advPromotionTaskApi.appendPromotionTask(
                taskId,
                additionalCount,
                formattedEndDate
            );

            console.log("任务追加响应:", response);
            dispatch(addAlert({type: AlertType.SUCCESS, message: response?.message || '任务追加成功'}));
            router.push(`/protected/promotion-task/${taskId}`); // Go back to detail page

        } catch (err) {
            console.error("追加任务失败:", err);
            const errorMsg = err?.response?.data?.message || err.message || '追加推广任务时出错';
            setApiError(errorMsg);
        } finally {
            setSubmitting(false);
        }
    };

    const isSubmitDisabled = () => {
        const maxAvailable = task?.availableMaterialCount || 0;
        const currentEndDate = task?.endDate ? dayjs(task.endDate) : dayjs();
        return (
            loading ||
            submitting ||
            !task ||
            maxAvailable <= 0 || // Cannot append if no materials available
            additionalCount <= 0 ||
            additionalCount > maxAvailable ||
            !newEndDate || !newEndDate.isValid() ||
            newEndDate.isBefore(currentEndDate, 'day')
        );
    };

    if (loading) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{display: 'flex', justifyContent: 'center', mt: 5}}>
                    <CircularProgress/>
                </Box>
            </Container>
        );
    }

    if (apiError && !task) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Button
                    startIcon={<ArrowLeft/>}
                    onClick={() => router.back()} // Go back to previous page
                    sx={{mb: 2, textTransform: 'none'}}
                >
                    返回
                </Button>
                <Alert severity="error">加载任务详情失败: {apiError}</Alert>
            </Container>
        );
    }

    if (!task) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Button
                    startIcon={<ArrowLeft/>}
                    onClick={() => router.back()} // Go back to previous page
                    sx={{mb: 2, textTransform: 'none'}}
                >
                    返回
                </Button>
                <Alert severity="warning">未找到任务信息。</Alert>
            </Container>
        );
    }

    const maxAvailable = task.availableMaterialCount || 0;
    const currentEndDate = task.endDate ? dayjs(task.endDate) : dayjs();

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{mb: 4}}>
                    <Button
                        startIcon={<ArrowLeft/>}
                        onClick={() => router.push(`/protected/promotion-task/${taskId}`)} // Back to detail page
                        sx={{mb: 2, textTransform: 'none'}}
                    >
                        返回任务详情
                    </Button>
                    <Typography variant="h4" fontWeight="bold">
                        追加推广任务
                    </Typography>
                </Box>

                <Paper
                    elevation={0}
                    sx={{
                        p: 4,
                        mb: 4,
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    {(error || apiError) && (
                        <Alert severity="error" sx={{mb: 3}}>
                            {error || apiError}
                        </Alert>
                    )}

                    <Typography variant="h6" sx={{mb: 3}}>
                        设置追加详情
                    </Typography>

                    <Grid container spacing={4}>
                        {/* Product Info Display */}
                        <Grid item xs={12} md={4} lg={3}>
                            <Paper variant="outlined" sx={{borderRadius: 2, overflow: 'hidden', height: '100%'}}>
                                <ProductCard
                                    images={task.productImage ? [task.productImage] : ['https://placehold.co/300x200?text=No+Image']}
                                >
                                    <Box sx={{p: 2}}>
                                        <Typography variant="subtitle1" sx={{fontWeight: 'medium', mb: 1, noWrap: true}} title={task.productName}>
                                            {task.productName || '未知产品'}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            平台: {task.platform || '未知'}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            当前可用素材: <Typography component="span" color="primary" fontWeight="bold">{maxAvailable}</Typography>
                                        </Typography>
                                    </Box>
                                </ProductCard>
                            </Paper>
                        </Grid>

                        {/* Settings Form */}
                        <Grid item xs={12} md={8} lg={9}>
                            <Paper variant="outlined" sx={{p: 3, borderRadius: 2, height: '100%'}}>
                                <Stack spacing={3}>
                                    {maxAvailable <= 0 ? (
                                        <Alert severity="warning">
                                            该产品当前没有可用的素材，无法追加。
                                        </Alert>
                                    ) : (
                                        <Paper variant="outlined" sx={{p: 3, borderRadius: 2}}>
                                            <Typography variant="subtitle1" fontWeight="medium" sx={{mb: 1}}>
                                                追加推广数量
                                            </Typography>
                                            <TextField
                                                label="设置追加数量"
                                                type="number"
                                                value={additionalCount}
                                                onChange={handleAdditionalCountChange}
                                                InputProps={{
                                                    inputProps: {min: 0, max: maxAvailable}
                                                }}
                                                fullWidth
                                                size="small"
                                                helperText={`当前最多可追加 ${maxAvailable} 个素材`}
                                                sx={{mb: 2}}
                                                required
                                                error={additionalCount > maxAvailable}
                                            />
                                            <Slider
                                                value={typeof additionalCount === 'number' ? additionalCount : 0}
                                                onChange={(_, newValue) => setAdditionalCount(newValue)}
                                                min={0}
                                                max={maxAvailable}
                                                step={1}
                                                marks
                                                valueLabelDisplay="auto"
                                                aria-labelledby="additional-count-slider"
                                                disabled={maxAvailable <= 0}
                                            />
                                        </Paper>
                                    )}

                                    <DatePicker
                                        label="设置新的结束日期"
                                        value={newEndDate}
                                        onChange={handleEndDateChange}
                                        minDate={currentEndDate} // Cannot select date before current end date
                                        format="YYYY年MM月DD日"
                                        slots={{textField: (params) => <TextField {...params} required fullWidth/>}}
                                    />

                                    <Alert severity="info">
                                        请确认追加的数量和新的结束日期。此操作将为该任务添加新的待发布素材。
                                    </Alert>
                                </Stack>
                            </Paper>
                        </Grid>
                    </Grid>

                    {/* Action Buttons */}
                    <Box sx={{display: 'flex', justifyContent: 'flex-end', mt: 6, pt: 3, borderTop: `1px solid ${theme.palette.divider}`}}>
                        <Button
                            variant="outlined"
                            onClick={() => router.push(`/protected/promotion-task/${taskId}`)} // Back to detail page
                            sx={{mr: 1}}
                            disabled={submitting}
                        >
                            取消
                        </Button>
                        <Button
                            variant="contained"
                            endIcon={<Check/>}
                            onClick={handleSubmit}
                            disabled={isSubmitDisabled()}
                        >
                            {submitting ? '提交中...' : '确认追加'}
                        </Button>
                        {submitting && (
                            <CircularProgress size={24} sx={{ml: 1, position: 'relative', top: '4px'}}/>
                        )}
                    </Box>
                </Paper>
            </Container>
        </LocalizationProvider>
    );
} 