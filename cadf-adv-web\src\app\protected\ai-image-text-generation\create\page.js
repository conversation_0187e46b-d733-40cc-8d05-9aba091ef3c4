"use client";

import {useEffect, useState} from "react";
import {Box, Button, Container, Grid, IconButton, Pagination, Paper, Slider, <PERSON>ack, Step, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {BarChart, Check, ChevronLeft, ChevronRight, RefreshCcw, Search, Megaphone, Plus} from "lucide-react";
import ProductCard from "@/components/ProductCard";
import {productApi} from "@/api/product-api";
import {aiBasicMaterialApi} from "@/api/ai-basic-material-api";
import {aiGenerationTaskApi} from "@/api/ai-generation-task-api";
import Link from "next/link";

const steps = ["选择产品", "选择素材", "配置生成选项", "推广引导"];

export default function CreateTask() {
    const theme = useTheme();
    const router = useRouter();
    const [activeStep, setActiveStep] = useState(0);
    const [products, setProducts] = useState([]);
    const [materials, setMaterials] = useState([]);
    const [taskName, setTaskName] = useState("");
    const [generateCount, setGenerateCount] = useState(1);
    const [includeDescription, setIncludeDescription] = useState(true);
    const [includePrice, setIncludePrice] = useState(true);
    const [searchProducts, setSearchProducts] = useState("");
    const [productSearchInput, setProductSearchInput] = useState("");
    const [materialSearchInput, setMaterialSearchInput] = useState("");
    const [productPage, setProductPage] = useState(1);
    const [materialPage, setMaterialPage] = useState(1);
    const itemsPerPage = 5;
    const [loadingProducts, setLoadingProducts] = useState(true);
    const [productError, setProductError] = useState(null);
    const [productTotalCount, setProductTotalCount] = useState(0);
    const [loadingMaterials, setLoadingMaterials] = useState(true);
    const [materialError, setMaterialError] = useState(null);
    const [materialTotalCount, setMaterialTotalCount] = useState(0);
    const [taskSubmitted, setTaskSubmitted] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [taskId, setTaskId] = useState(null);

    // New function to load products with optional search term and page
    const loadProducts = async (searchTerm = "", page = 1) => {
        setLoadingProducts(true);
        setProductError(null);
        try {
            // Construct filter based on searchTerm
            // If searchTerm is provided, use it directly as the filter.
            // Otherwise, use an empty object.
            const filter = searchTerm ? searchTerm : {};

            // Call API with pagination parameters (page is 0-indexed)
            // Pass the modified filter (either the search string or an empty object)
            const response = await productApi.query_all(filter, page - 1, itemsPerPage);

            if (response && Array.isArray(response.products)) {
                setProductTotalCount(response.total_count || 0); // 从响应设置总数

                // 直接使用 API 返回的 URL
                const productsWithImages = response.products.map((product) => {
                    let imageUrl = `https://placehold.co/300x400/757de8/ffffff?text=${product.title}`; // 默认图片
                    // 检查 images 数组是否存在且不为空，并获取第一个图片的 URL
                    if (product.images && product.images.length > 0 && product.images[0].url) {
                        imageUrl = product.images[0].url;
                    } else {
                        console.warn(`产品 ${product.id_} 的 images 数组为空或第一个图片缺少 URL，使用默认图片。`);
                    }

                    return {
                        ...product,
                        id: product.id_,
                        name: product.title,
                        description: product.selling_points || '',
                        image: imageUrl,
                        selected: false
                    };
                });
                setProducts(productsWithImages);
            } else {
                console.error("Failed to fetch products: Invalid response format", response);
                setProductError("获取产品数据失败：无效的响应格式");
                setProducts([]);
                setProductTotalCount(0); // 重置总数
            }
        } catch (error) {
            console.error("Failed to fetch products:", error);
            setProductError("获取产品数据失败，请稍后重试");
            setProducts([]);
            setProductTotalCount(0); // 重置总数
        } finally {
            setLoadingProducts(false);
        }
    };

    const loadMaterials = async (searchTerm = "", page = 1) => {
        setLoadingMaterials(true);
        setMaterialError(null);
        try {
            const filter = searchTerm ? searchTerm : {};
            const response = await aiBasicMaterialApi.queryAll(filter, page - 1, itemsPerPage);

            if (response && Array.isArray(response.data)) {
                setMaterialTotalCount(response.total || 0);

                // 直接使用 API 返回的 URL (更正为 signed_url)
                const materialsWithImages = response.data.map((material) => {
                    let imageUrl = `https://placehold.co/300x400/81c784/ffffff?text=${material.title}`; // 默认图片
                    // 检查 images 数组是否存在且不为空，并获取第一个图片的 signed_url
                    if (material.images && material.images.length > 0 && material.images[0].signed_url) {
                        imageUrl = material.images[0].signed_url; // 使用 signed_url
                    } else {
                        console.warn(`素材 ${material.id_} 的 images 数组为空或第一个图片缺少 signed_url，使用默认图片。`);
                    }

                    return {
                        ...material,
                        id: material.id_,
                        name: material.title,
                        description: material.content || '',
                        image: imageUrl,
                        selected: false
                    };
                });
                setMaterials(materialsWithImages);
            } else {
                console.error("Failed to fetch materials: Invalid response format", response);
                setMaterialError("获取素材数据失败：无效的响应格式");
                setMaterials([]);
                setMaterialTotalCount(0);
            }
        } catch (error) {
            console.error("Failed to fetch materials:", error);
            setMaterialError("获取素材数据失败，请稍后重试");
            setMaterials([]);
            setMaterialTotalCount(0);
        } finally {
            setLoadingMaterials(false);
        }
    };

    useEffect(() => {
        loadProducts(); // Load initial products for page 1
        loadMaterials(); // Load initial materials for page 1
    }, []);

    // Product search handlers
    const handleProductSearch = () => {
        setProductPage(1); // Reset page when searching
        loadProducts(productSearchInput, 1); // Trigger API call with search term for page 1
    };

    const handleProductRefresh = () => {
        setProductSearchInput("");
        setProductPage(1); // Reset page when refreshing
        loadProducts("", 1); // Trigger API call without search term for page 1
    };

    // Material search handlers
    const handleMaterialSearch = () => {
        setMaterialPage(1); // Reset page when searching
        loadMaterials(materialSearchInput, 1); // Trigger API call with search term for page 1
    };

    const handleMaterialRefresh = () => {
        setMaterialSearchInput("");
        setMaterialPage(1); // Reset page when refreshing
        loadMaterials("", 1); // Trigger API call without search term for page 1
    };

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleSubmit = async () => {
        const selectedProduct = products.find((p) => p.selected);
        const selectedMaterials = materials.filter(m => m.selected); // 获取所有选中的素材
        const selectedMaterialIds = selectedMaterials.map(m => m.id); // 提取它们的 ID

        if (!selectedProduct || selectedMaterialIds.length === 0) { // 检查是否有选中的素材
            console.error("请先选择产品和至少一个素材");
            // 这里可以添加用户提示，例如使用 Snackbar
            return;
        }

        // 进入第四步但不提交任务
        setActiveStep(3);
    };

    // 添加实际提交任务的方法
    const submitTask = async () => {
        const selectedProduct = products.find((p) => p.selected);
        const selectedMaterials = materials.filter(m => m.selected);
        const selectedMaterialIds = selectedMaterials.map(m => m.id);

        if (!selectedProduct || selectedMaterialIds.length === 0) {
            console.error("请先选择产品和至少一个素材");
            return;
        }

        setSubmitting(true);
        try {
            const response = await aiGenerationTaskApi.create(
                selectedProduct.id,
                selectedMaterialIds,
                generateCount
            );

            console.log("任务创建成功:", response);
            // 设置任务已提交状态
            setTaskSubmitted(true);
            // 如果响应中包含任务ID，保存它
            if (response && response.id_) {
                setTaskId(response.id_);
            }
        } catch (error) {
            console.error("任务创建失败:", error);
            // 这里可以添加错误提示
        } finally {
            setSubmitting(false);
        }
    };

    const handleProductToggle = (id) => {
        setProducts(
            products.map((product) =>
                product.id === id
                    ? {...product, selected: true}
                    : {...product, selected: false}
            )
        );
    };

    const handleMaterialToggle = (id) => {
        setMaterials(
            materials.map((material) =>
                material.id === id ? {...material, selected: !material.selected} : material
            )
        );
    };

    const selectedMaterialsCount = materials.filter((m) => m.selected).length;

    const handleProductPageChange = (event, value) => {
        setProductPage(value);
        loadProducts(productSearchInput, value); // Load products for the new page
    };

    const handleMaterialPageChange = (event, value) => {
        setMaterialPage(value);
        loadMaterials(materialSearchInput, value); // Load materials for the new page
    };

    // 计算总页数
    const getProductTotalPages = () => {
        return Math.ceil(productTotalCount / itemsPerPage); // 使用总数和每页数量计算
    };

    const getMaterialTotalPages = () => {
        return Math.ceil(materialTotalCount / itemsPerPage);
    };

    // 判断当前步骤是否可以进行下一步
    const canProceed = () => {
        if (activeStep === 0) {
            return products.some(p => p.selected);
        } else if (activeStep === 1) {
            return selectedMaterialsCount > 0;
        }
        return true;
    };

    // 步骤内容
    const getStepContent = (step) => {
        switch (step) {
            case 0:
                return (
                    <Box>
                        <Box sx={{mb: 3}}>
                            <Typography variant="h5" fontWeight="medium" gutterBottom>
                                选择产品
                            </Typography>
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                                请选择要进行AI图文生成的产品
                            </Typography>
                            <Paper 
                                elevation={0} 
                                sx={{
                                    mt: 2, 
                                    mb: 3, 
                                    p: 2, 
                                    bgcolor: theme.palette.primary.light, 
                                    borderRadius: 1,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between"
                                }}
                            >
                                <Typography variant="body2" sx={{ color: theme.palette.primary.contrastText }}>
                                    没有找到想要的产品？添加一个新产品来继续
                                </Typography>
                                <Button 
                                    variant="contained" 
                                    color="primary" 
                                    onClick={() => router.push("/protected/product-management/new")}
                                    size="small"
                                    sx={{ 
                                        bgcolor: theme.palette.common.white, 
                                        color: theme.palette.primary.main,
                                        '&:hover': {
                                            bgcolor: theme.palette.grey[100]
                                        }
                                    }}
                                >
                                    添加新产品
                                </Button>
                            </Paper>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{mb: 3}}>
                                <TextField
                                    fullWidth
                                    placeholder="搜索产品名称或描述"
                                    variant="outlined"
                                    size="small"
                                    value={productSearchInput}
                                    onChange={(e) => setProductSearchInput(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') handleProductSearch();
                                    }}
                                />
                                <IconButton onClick={handleProductSearch} color="primary" aria-label="search products">
                                    <Search size={20}/>
                                </IconButton>
                                <IconButton onClick={handleProductRefresh} aria-label="refresh products">
                                    <RefreshCcw size={20}/>
                                </IconButton>
                            </Stack>
                        </Box>
                        {loadingProducts ? (
                            <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                <Typography color="text.secondary">正在加载产品...</Typography>
                            </Box>
                        ) : productError ? (
                            <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                <Typography color="error">{productError}</Typography>
                            </Box>
                        ) : (
                            <>
                                <Grid container spacing={3} sx={{
                                    display: 'flex', 
                                    flexWrap: 'wrap',  // 支持自动换行
                                    pb: 2,
                                    '& .MuiGrid-item': {
                                        width: '20%',  // 固定宽度为20%
                                        flexShrink: 0  // 防止压缩
                                    }
                                }}>
                                    <Grid item>
                                        <Link href="/protected/product-management/new" passHref style={{textDecoration: 'none', display: 'block', height: '100%'}}>
                                            <Paper
                                                sx={{
                                                    height: '100%',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    mx: 'auto',
                                                    borderStyle: 'dashed',
                                                    borderWidth: 2,
                                                    borderColor: 'primary.main',
                                                    backgroundColor: 'primary.50',
                                                    transition: 'all 0.3s',
                                                    '&:hover': {
                                                        transform: 'translateY(-8px)',
                                                        boxShadow: 3,
                                                        borderColor: 'primary.dark'
                                                    },
                                                    cursor: 'pointer',
                                                    aspectRatio: '3/4',
                                                    width: '100%'
                                                }}
                                            >
                                                <Box sx={{
                                                    width: 60,
                                                    height: 60,
                                                    borderRadius: '50%',
                                                    bgcolor: 'primary.main',
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    mb: 2
                                                }}>
                                                    <Plus size={30} color="#fff" />
                                                </Box>
                                                <Typography variant="h6" color="primary.main" sx={{fontWeight: 'medium'}}>
                                                    新增产品
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{mt: 1}}>
                                                    添加产品继续生成
                                                </Typography>
                                            </Paper>
                                        </Link>
                                    </Grid>
                                    {products.map((product) => (
                                        <Grid item key={product.id}>
                                            <ProductCard
                                                images={[product.image]}
                                                onClick={() => handleProductToggle(product.id)}
                                                coverOverlay={product.selected ? (
                                                    <Box
                                                        sx={{
                                                            width: '100%',
                                                            height: '100%',
                                                            bgcolor: 'rgba(117, 125, 232, 0.6)', // 主题主色+透明
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            borderRadius: 2,
                                                        }}
                                                    >
                                                        <Check size={64} color="#fff" strokeWidth={3} />
                                                    </Box>
                                                ) : null}
                                            >
                                                <Box sx={{pt: 2, px: 2, pb: 4, position: "relative"}}>
                                                    <Typography variant="h6" component="div" sx={{ mb: 3, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '100%' }}>
                                                        {product.name}
                                                    </Typography>
                                                    <Typography variant="caption" color="text.secondary" sx={{ position: 'absolute', bottom: 8, left: 8 }}>
                                                        点击选择
                                                    </Typography>
                                                </Box>
                                            </ProductCard>
                                        </Grid>
                                    ))}
                                    {products.length === 0 && !loadingProducts && (
                                        <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                            <Typography color="text.secondary">未找到匹配的产品</Typography>
                                        </Box>
                                    )}
                                </Grid>
                                {getProductTotalPages() > 1 && (
                                    <Box sx={{mt: 3, display: "flex", justifyContent: "center"}}>
                                        <Pagination
                                            count={getProductTotalPages()}
                                            page={productPage}
                                            onChange={handleProductPageChange}
                                            color="primary"
                                        />
                                    </Box>
                                )}
                            </>
                        )}
                        <Box sx={{mt: 3, display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                            <Typography color="text.secondary">
                                已选择 {products.filter(p => p.selected).length}/1 个产品
                                {productTotalCount > 0 && ` (共 ${productTotalCount} 个)`}
                            </Typography>
                        </Box>
                    </Box>
                );
            case 1:
                return (
                    <Box>
                        <Box sx={{mb: 3}}>
                            <Typography variant="h5" fontWeight="medium" gutterBottom>
                                选择素材
                            </Typography>
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                                请选择需要对标的参考素材
                            </Typography>
                            <Paper 
                                elevation={0} 
                                sx={{
                                    mt: 2, 
                                    mb: 3, 
                                    p: 2, 
                                    bgcolor: theme.palette.primary.light, 
                                    borderRadius: 1,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between"
                                }}
                            >
                                <Typography variant="body2" sx={{ color: theme.palette.primary.contrastText }}>
                                    没有找到合适的素材？添加一个新素材来继续
                                </Typography>
                                <Button 
                                    variant="contained" 
                                    color="primary" 
                                    onClick={() => router.push("/protected/ai-base-material-management")}
                                    size="small"
                                    sx={{ 
                                        bgcolor: theme.palette.common.white, 
                                        color: theme.palette.primary.main,
                                        '&:hover': {
                                            bgcolor: theme.palette.grey[100]
                                        }
                                    }}
                                >
                                    添加新素材
                                </Button>
                            </Paper>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{mb: 3}}>
                                <TextField
                                    fullWidth
                                    placeholder="搜索素材名称或描述"
                                    variant="outlined"
                                    size="small"
                                    value={materialSearchInput}
                                    onChange={(e) => setMaterialSearchInput(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') handleMaterialSearch();
                                    }}
                                />
                                <IconButton onClick={handleMaterialSearch} color="primary" aria-label="search materials">
                                    <Search size={20}/>
                                </IconButton>
                                <IconButton onClick={handleMaterialRefresh} aria-label="refresh materials">
                                    <RefreshCcw size={20}/>
                                </IconButton>
                            </Stack>
                        </Box>
                        {loadingMaterials ? (
                            <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                <Typography color="text.secondary">正在加载素材...</Typography>
                            </Box>
                        ) : materialError ? (
                            <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                <Typography color="error">{materialError}</Typography>
                            </Box>
                        ) : (
                            <>
                                <Grid container spacing={3} sx={{
                                    display: 'flex', 
                                    flexWrap: 'wrap',  // 支持自动换行
                                    pb: 2,
                                    '& .MuiGrid-item': {
                                        width: '20%',  // 固定宽度为20%
                                        flexShrink: 0  // 防止压缩
                                    }
                                }}>
                                    <Grid item>
                                        <Link href="/protected/ai-base-material-management" passHref style={{textDecoration: 'none', display: 'block', height: '100%'}}>
                                            <Paper
                                                sx={{
                                                    height: '100%',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    mx: 'auto',
                                                    borderStyle: 'dashed',
                                                    borderWidth: 2,
                                                    borderColor: 'primary.main',
                                                    backgroundColor: 'primary.50',
                                                    transition: 'all 0.3s',
                                                    '&:hover': {
                                                        transform: 'translateY(-8px)',
                                                        boxShadow: 3,
                                                        borderColor: 'primary.dark'
                                                    },
                                                    cursor: 'pointer',
                                                    aspectRatio: '3/4',
                                                    width: '100%'
                                                }}
                                            >
                                                <Box sx={{
                                                    width: 60,
                                                    height: 60,
                                                    borderRadius: '50%',
                                                    bgcolor: 'primary.main',
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    mb: 2
                                                }}>
                                                    <Plus size={30} color="#fff" />
                                                </Box>
                                                <Typography variant="h6" color="primary.main" sx={{fontWeight: 'medium'}}>
                                                    新增素材
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{mt: 1}}>
                                                    添加素材继续生成
                                                </Typography>
                                            </Paper>
                                        </Link>
                                    </Grid>
                                    {materials.map((material) => (
                                        <Grid item key={material.id}>
                                            <ProductCard
                                                images={[material.image]}
                                                onClick={() => handleMaterialToggle(material.id)}
                                                coverOverlay={material.selected ? (
                                                    <Box
                                                        sx={{
                                                            width: '100%',
                                                            height: '100%',
                                                            bgcolor: 'rgba(117, 125, 232, 0.6)', // 主题主色+透明
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            borderRadius: 2,
                                                        }}
                                                    >
                                                        <Check size={64} color="#fff" strokeWidth={3} />
                                                    </Box>
                                                ) : null}
                                            >
                                                <Box sx={{pt: 2, px: 2, pb: 4, position: "relative"}}>
                                                    <Typography variant="h6" component="div" sx={{ mb: 3, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '100%' }}>
                                                        {material.name}
                                                    </Typography>
                                                    <Typography variant="caption" color="text.secondary" sx={{ position: 'absolute', bottom: 8, left: 8 }}>
                                                        点击选择
                                                    </Typography>
                                                </Box>
                                            </ProductCard>
                                        </Grid>
                                    ))}
                                    {materials.length === 0 && !loadingMaterials && (
                                        <Box sx={{p: 4, width: "100%", textAlign: "center"}}>
                                            <Typography color="text.secondary">未找到匹配的素材</Typography>
                                        </Box>
                                    )}
                                </Grid>
                                {getMaterialTotalPages() > 1 && (
                                    <Box sx={{mt: 3, display: "flex", justifyContent: "center"}}>
                                        <Pagination
                                            count={getMaterialTotalPages()}
                                            page={materialPage}
                                            onChange={handleMaterialPageChange}
                                            color="primary"
                                        />
                                    </Box>
                                )}
                            </>
                        )}
                        <Box sx={{mt: 3, display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                            <Typography color="text.secondary">
                                已选择 {selectedMaterialsCount} 个素材
                                {materialTotalCount > 0 && ` (共 ${materialTotalCount} 个)`}
                            </Typography>
                        </Box>
                    </Box>
                );
            case 2:
                return (
                    <Box>
                        <Box sx={{mb: 4}}>
                            <Typography variant="h5" fontWeight="medium" gutterBottom>
                                配置生成选项
                            </Typography>
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                                配置AI图文生成的相关选项
                            </Typography>
                        </Box>
                        <Grid container spacing={4}>
                            <Grid item xs={12}>
                                <Paper sx={{p: 3, height: "100%"}}>
                                    <Typography variant="h6" gutterBottom>
                                        基本设置
                                    </Typography>
                                    <Box sx={{mb: 3}}>
                                        <Typography variant="subtitle2" gutterBottom>
                                            生成数量
                                        </Typography>
                                        <TextField
                                            fullWidth
                                            value={generateCount}
                                            onChange={(e) => {
                                                const value = parseInt(e.target.value, 10);
                                                if (!isNaN(value)) {
                                                    setGenerateCount(value);
                                                }
                                            }}
                                            inputProps={{
                                                type: 'number',
                                            }}
                                            variant="outlined"
                                            size="medium"
                                        />
                                    </Box>
                                </Paper>
                            </Grid>
                        </Grid>
                    </Box>
                );
            case 3:
                // 获取已选产品和素材
                const selectedProduct = products.find(p => p.selected);
                const selectedMaterials = materials.filter(m => m.selected);
                return (
                    <Box>
                        <Box sx={{mb: 4}}>
                            <Typography variant="h5" fontWeight="medium" gutterBottom>
                                任务摘要
                            </Typography>
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                                {taskSubmitted
                                    ? "任务创建成功！马上用推广工具让内容触达更多用户。"
                                    : "请确认任务信息，然后提交任务"}
                            </Typography>
                        </Box>

                        {/* 根据任务提交状态显示不同内容 */}
                        {taskSubmitted ? (
                            // 任务已提交，显示推广引导
                            <Paper
                                elevation={0}
                                sx={{
                                    mt: 4, // 任务提交后，此部分需要有顶部间距
                                    mb: 4, 
                                    p: 3, 
                                    bgcolor: theme.palette.success.light, 
                                    borderRadius: 2,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between"
                                }}
                            >
                                <Stack direction={{xs: "column", sm: "row"}} spacing={3} alignItems="center" sx={{width: "100%"}}>
                                    <Megaphone size={28} color={theme.palette.success.contrastText}/>
                                    <Box sx={{flexGrow: 1}}>
                                        <Typography variant="h6" fontWeight="medium" color={theme.palette.success.contrastText}>
                                            AI图文生成任务已创建成功！可以使用推广工具开始推广
                                        </Typography>
                                    </Box>
                                    <Button
                                        variant="contained"
                                        color="success"
                                        onClick={() => router.push("/protected/promotion-task/create")}
                                    >
                                        创建推广任务
                                    </Button>
                                </Stack>
                            </Paper>
                        ) : (
                            // 任务未提交，显示任务摘要
                            <Box sx={{p: 3, bgcolor: theme.palette.primary.light, borderRadius: 2}}>
                                <Stack direction={{xs: "column", sm: "row"}} spacing={3} alignItems="center">
                                    <BarChart size={24} color={theme.palette.primary.contrastText}/>
                                    <Box>
                                        <Typography variant="subtitle1" fontWeight="medium" color={theme.palette.primary.contrastText}>
                                            任务摘要
                                        </Typography>
                                        <Typography variant="body2" color={theme.palette.primary.contrastText}>
                                            已选择 {selectedProduct ? 1 : 0} 个产品和 {selectedMaterials.length} 个素材，
                                            将生成 {generateCount} 份AI图文内容。
                                        </Typography>
                                    </Box>
                                </Stack>
                                
                                <Grid container spacing={3} sx={{ mt: 2 }}>
                                    {/* 左侧：产品部分 */}
                                    <Grid item xs={12} sm={4} md={3}>
                                        <Typography variant="subtitle2" sx={{mb: 1}} color={theme.palette.primary.contrastText}>所选产品</Typography>
                                        {selectedProduct ? (
                                            <Box sx={{ maxWidth: '100%' }}>
                                                <ProductCard
                                                    images={[selectedProduct.image]}
                                                    coverOverlay={null}
                                                    sx={{ height: '280px' }}
                                                >
                                                    <Box sx={{pt: 1, px: 1.5, pb: 2}}>
                                                        <Typography variant="subtitle1" component="div" sx={{ mb: 0.5, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '100%' }}>
                                                            {selectedProduct.name}
                                                        </Typography>
                                                        <Typography variant="body2" color="text.secondary" sx={{ 
                                                            display: '-webkit-box',
                                                            WebkitLineClamp: 3,
                                                            WebkitBoxOrient: 'vertical',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis',
                                                        }}>
                                                            {selectedProduct.description}
                                                        </Typography>
                                                    </Box>
                                                </ProductCard>
                                            </Box>
                                        ) : (
                                            <Typography color="text.secondary">未选择产品</Typography>
                                        )}
                                    </Grid>
                                    
                                    {/* 右侧：素材部分 */}
                                    <Grid item xs={12} sm={8} md={9}>
                                        <Typography variant="subtitle2" sx={{mb: 1}} color={theme.palette.primary.contrastText}>
                                            所选素材 ({selectedMaterials.length})
                                        </Typography>
                                        {selectedMaterials.length > 0 ? (
                                            <Box sx={{ 
                                                display: 'flex',
                                                flexWrap: 'nowrap',
                                                gap: 2,
                                                overflowX: 'auto',
                                                pb: 1,
                                                '&::-webkit-scrollbar': {
                                                    height: '8px',
                                                },
                                                '&::-webkit-scrollbar-thumb': {
                                                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                                                    borderRadius: '4px',
                                                }
                                            }}>
                                                {selectedMaterials.map(material => (
                                                    <Box 
                                                        key={material.id} 
                                                        sx={{ 
                                                            minWidth: '160px',
                                                            maxWidth: '160px',
                                                            flexShrink: 0 
                                                        }}
                                                    >
                                                        <ProductCard
                                                            images={[material.image]}
                                                            coverOverlay={null}
                                                            sx={{ height: '200px' }}
                                                        >
                                                            <Box sx={{pt: 1, px: 1.5, pb: 2}}>
                                                                <Typography variant="subtitle2" component="div" sx={{ 
                                                                    mb: 0.5, 
                                                                    whiteSpace: 'nowrap', 
                                                                    overflow: 'hidden', 
                                                                    textOverflow: 'ellipsis', 
                                                                    width: '100%',
                                                                    fontSize: '0.8rem'
                                                                }}>
                                                                    {material.name}
                                                                </Typography>
                                                                <Typography variant="body2" color="text.secondary" sx={{ 
                                                                    display: '-webkit-box',
                                                                    WebkitLineClamp: 2,
                                                                    WebkitBoxOrient: 'vertical',
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis',
                                                                    fontSize: '0.75rem'
                                                                }}>
                                                                    {material.description}
                                                                </Typography>
                                                            </Box>
                                                        </ProductCard>
                                                    </Box>
                                                ))}
                                            </Box>
                                        ) : (
                                            <Typography color="text.secondary">未选择素材</Typography>
                                        )}
                                    </Grid>
                                </Grid>
                            </Box>
                        )}

                        {/* New Paper for the waiting message */}
                        <Paper
                            elevation={0}
                            sx={{
                                mt: 3, // Add margin top
                                p: 2, // Add padding
                                bgcolor: theme.palette.warning.light, // Use warning color
                                borderRadius: 2,
                            }}
                        >
                            <Typography variant="body2" color={theme.palette.warning.contrastText}>
                                AI 图文生成需要一定时间，请稍后在任务列表中查看生成结果。
                            </Typography>
                        </Paper>

                        <Box sx={{mt: 4, display: "flex", justifyContent: "flex-end", gap: 2}}>
                            {!taskSubmitted ? (
                                // 未提交任务，显示提交按钮
                                <Button
                                    variant="contained"
                                    color="primary"
                                    onClick={submitTask}
                                    disabled={submitting}
                                >
                                    {submitting ? "提交中..." : "提交任务"}
                                </Button>
                            ) : null}
                        </Box>
                    </Box>
                );
            default:
                return "未知步骤";
        }
    };

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
            <Paper
                sx={{
                    p: 4,
                    mb: 3,
                    borderRadius: 2,
                    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.08)",
                }}
            >
                <Box sx={{display: "flex", justifyContent: "space-between", mb: 3}}>
                    <Typography variant="h4" fontWeight="bold">
                        创建AI图文生成任务
                    </Typography>
                </Box>

                <Stepper activeStep={activeStep} sx={{mb: 4}}>
                    {steps.map((label) => (
                        <Step key={label}>
                            <StepLabel>{label}</StepLabel>
                        </Step>
                    ))}
                </Stepper>

                <Box sx={{mt: 4, mb: 4}}>{getStepContent(activeStep)}</Box>

                <Box sx={{display: "flex", justifyContent: "space-between", pt: 2}}>
                    <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => router.push("/protected/ai-image-text-generation")}
                        startIcon={<ChevronLeft size={18}/>}
                    >
                        返回任务列表
                    </Button>
                    <Box>
                        <Button
                            color="inherit"
                            disabled={activeStep === 0}
                            onClick={handleBack}
                            startIcon={<ChevronLeft size={18}/>}
                            sx={{mr: 1}}
                        >
                            上一步
                        </Button>
                        {activeStep === steps.length - 1 ? (
                            // 第四步不显示任何按钮，因为已在步骤内容中提供了按钮
                            null
                        ) : (
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={handleNext}
                                endIcon={<ChevronRight size={18}/>}
                                disabled={!canProceed()}
                            >
                                下一步
                            </Button>
                        )}
                    </Box>
                </Box>
            </Paper>
        </Container>
    );
}
