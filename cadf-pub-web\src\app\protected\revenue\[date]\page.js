"use client";

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {Box, Card, CardContent, Grid, IconButton, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, useMediaQuery, useTheme} from '@mui/material';
import {ChevronLeft} from 'lucide-react';
import {dailyTaskRevenueApi} from '@/api/daily-task-revenue-api';
import {addAlert} from '@/core/components/redux/alert-slice';
import {AlertType} from '@/core/components/alert';
import {useDispatch} from 'react-redux';

export default function RevenueDetails() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const params = useParams();
    const dispatch = useDispatch();

    const date = params.date;

    const [detailsData, setDetailsData] = useState([]);
    const [totalViews, setTotalViews] = useState(0);
    const [totalRevenue, setTotalRevenue] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchDetails = async () => {
            if (!date) return;
            setIsLoading(true);
            try {
                const response = await dailyTaskRevenueApi.queryDetailsByDate(date);
                if (response && response.items) {
                    setDetailsData(response.items);
                    setTotalViews(response.totalViews || 0);
                    setTotalRevenue(response.totalRevenue || 0);
                } else {
                    setDetailsData([]);
                    setTotalViews(0);
                    setTotalRevenue(0);
                }
            } catch (error) {
                console.error(`获取日期 ${date} 的收益明细失败:`, error);
                dispatch(addAlert({type: AlertType.ERROR, message: '获取收益明细失败'}));
                setDetailsData([]);
                setTotalViews(0);
                setTotalRevenue(0);
            }
            setIsLoading(false);
        };

        fetchDetails();
    }, [date, dispatch]);

    const handleBack = () => {
        router.back();
    };

    return (
        <Box sx={{pt: 2, px: {xs: 1, sm: 2}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton onClick={handleBack} sx={{mr: 1}}>
                    <ChevronLeft/>
                </IconButton>
                <Typography variant="h4" sx={{
                    fontWeight: 'medium',
                    fontSize: {xs: '1.5rem', sm: '2rem', md: '2.25rem'}
                }}>
                    {date} 收益明细
                </Typography>
            </Box>

            {isLoading ? (
                <Typography>加载中...</Typography>
            ) : (
                <>
                    {/* 收益概览卡片 */}
                    <Grid container spacing={2} sx={{mb: 4}}>
                        <Grid item xs={12} sm={6}>
                            <Card>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        总浏览量
                                    </Typography>
                                    <Typography variant="h4" sx={{color: theme.palette.primary.main}}>
                                        {totalViews.toLocaleString()}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <Card>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        总收益金额
                                    </Typography>
                                    <Typography variant="h4" sx={{color: theme.palette.success.main}}>
                                        ¥{totalRevenue.toFixed(2)}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>

                    {/* 明细列表 */}
                    <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                        明细列表
                    </Typography>

                    {detailsData.length === 0 ? (
                        <Typography>当日无收益明细</Typography>
                    ) : isMobile ? (
                        // 移动端卡片式布局
                        <Box>
                            {detailsData.map((item) => (
                                <Card key={item.id} sx={{
                                    mb: 3,
                                    borderRadius: 2,
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                                    overflow: 'hidden'
                                }}>
                                    <Box sx={{
                                        height: '8px',
                                        width: '100%',
                                        bgcolor: item.status === "已结算"
                                            ? theme.palette.success.main
                                            : item.status === "已提现"
                                                ? theme.palette.info.main
                                                : theme.palette.warning.main
                                    }}/>
                                    <CardContent sx={{p: 3}}>
                                        {/* 任务来源/作品标题信息 - 与PC端表格对应 */}
                                        <Box sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'flex-start',
                                            mb: 2.5
                                        }}>
                                            <Typography
                                                variant="h6"
                                                fontWeight="medium"
                                                sx={{
                                                    mb: 0,
                                                    maxWidth: '70%',
                                                    lineHeight: 1.4
                                                }}
                                            >
                                                {item.url ? (
                                                    <a
                                                        href={item.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        style={{
                                                            color: theme.palette.primary.main,
                                                            textDecoration: 'none',
                                                        }}
                                                    >
                                                        {item.title}
                                                    </a>
                                                ) : (
                                                    item.title
                                                )}
                                            </Typography>
                                            <Box
                                                component="span"
                                                sx={{
                                                    px: 1.5,
                                                    py: 0.75,
                                                    borderRadius: 10,
                                                    fontSize: '0.8rem',
                                                    fontWeight: 'medium',
                                                    whiteSpace: 'nowrap',
                                                    backgroundColor: item.status === "已结算"
                                                        ? `rgba(46, 125, 50, 0.1)`
                                                        : item.status === "已提现"
                                                            ? `rgba(2, 136, 209, 0.1)`
                                                            : `rgba(237, 108, 2, 0.1)`,
                                                    color: item.status === "已结算"
                                                        ? 'success.main'
                                                        : item.status === "已提现"
                                                            ? 'info.main'
                                                            : 'warning.main',
                                                }}
                                            >
                                                {item.status}
                                            </Box>
                                        </Box>

                                        <Box sx={{
                                            p: 2,
                                            mb: 3,
                                            borderRadius: 2,
                                            bgcolor: 'rgba(0,0,0,0.02)',
                                            border: '1px solid rgba(0,0,0,0.03)'
                                        }}>
                                            <Typography variant="body2" color="textSecondary" gutterBottom>
                                                任务来源
                                            </Typography>
                                            <Typography variant="body1" fontWeight="medium">
                                                {item.task}
                                            </Typography>
                                        </Box>

                                        {/* 浏览量和收益信息 - 与PC端表格对应 */}
                                        <Box sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            mb: 3,
                                            p: 2,
                                            borderRadius: 2,
                                            bgcolor: 'rgba(0,0,0,0.01)'
                                        }}>
                                            <Box sx={{textAlign: 'center', flex: 1}}>
                                                <Typography variant="body2" color="textSecondary" gutterBottom>
                                                    浏览量
                                                </Typography>
                                                <Typography variant="h6" fontWeight="bold">
                                                    {item.views.toLocaleString()}
                                                </Typography>
                                            </Box>
                                            <Box sx={{
                                                textAlign: 'center',
                                                flex: 1,
                                                borderLeft: '1px solid rgba(0,0,0,0.06)'
                                            }}>
                                                <Typography variant="body2" color="textSecondary" gutterBottom>
                                                    收益金额
                                                </Typography>
                                                <Typography variant="h6" fontWeight="bold" color={theme.palette.success.main}>
                                                    ¥{item.revenue.toFixed(2)}
                                                </Typography>
                                            </Box>
                                        </Box>

                                        {/* 结算日期和出账日期信息 - 与PC端表格对应 */}
                                        <Box sx={{
                                            borderRadius: 2,
                                            border: '1px solid #f0f0f0',
                                            overflow: 'hidden',
                                            mt: 3
                                        }}>
                                            <Box sx={{
                                                display: 'flex',
                                                borderBottom: '1px solid #f0f0f0',
                                            }}>
                                                <Box sx={{
                                                    flex: 1,
                                                    p: 2,
                                                    bgcolor: 'rgba(0,0,0,0.01)',
                                                    borderRight: '1px solid #f0f0f0'
                                                }}>
                                                    <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                        结算日期
                                                    </Typography>
                                                </Box>
                                                <Box sx={{
                                                    flex: 1,
                                                    p: 2,
                                                    bgcolor: 'rgba(0,0,0,0.01)'
                                                }}>
                                                    <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                        出账日期
                                                    </Typography>
                                                </Box>
                                            </Box>
                                            <Box sx={{
                                                display: 'flex',
                                            }}>
                                                <Box sx={{
                                                    flex: 1,
                                                    p: 2,
                                                    borderRight: '1px solid #f0f0f0'
                                                }}>
                                                    <Typography variant="body1" sx={{textAlign: 'center'}}>
                                                        {item.settlementDate || '未结算'}
                                                    </Typography>
                                                </Box>
                                                <Box sx={{
                                                    flex: 1,
                                                    p: 2
                                                }}>
                                                    <Typography variant="body1" sx={{textAlign: 'center'}}>
                                                        {item.paymentDate || '-'}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </Box>
                                    </CardContent>
                                </Card>
                            ))}
                        </Box>
                    ) : (
                        // 桌面端表格布局
                        <TableContainer component={Paper} sx={{mb: 4}}>
                            <Table>
                                <TableHead>
                                    <TableRow sx={{backgroundColor: theme.palette.action.hover}}>
                                        <TableCell>任务来源</TableCell>
                                        <TableCell>作品标题</TableCell>
                                        <TableCell align="right">浏览量</TableCell>
                                        <TableCell align="right">收益金额 (元)</TableCell>
                                        <TableCell>出账日期</TableCell>
                                        <TableCell>结算日期</TableCell>
                                        <TableCell>状态</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {detailsData.map((item) => (
                                        <TableRow key={item.id}>
                                            <TableCell>{item.task}</TableCell>
                                            <TableCell>
                                                {item.url ? (
                                                    <a
                                                        href={item.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        style={{
                                                            color: theme.palette.primary.main,
                                                            textDecoration: 'none',
                                                        }}
                                                    >
                                                        {item.title}
                                                    </a>
                                                ) : (
                                                    item.title
                                                )}
                                            </TableCell>
                                            <TableCell align="right">{item.views.toLocaleString()}</TableCell>
                                            <TableCell align="right">{item.revenue.toFixed(2)}</TableCell>
                                            <TableCell>{item.paymentDate || '-'}</TableCell>
                                            <TableCell>{item.settlementDate || '未结算'}</TableCell>
                                            <TableCell>
                                                <Box
                                                    component="span"
                                                    sx={{
                                                        px: 1.5,
                                                        py: 0.5,
                                                        borderRadius: 1,
                                                        fontSize: '0.75rem',
                                                        backgroundColor: item.status === "已结算"
                                                            ? `rgba(${theme.palette.success.main}, 0.1)`
                                                            : item.status === "已提现"
                                                                ? `rgba(${theme.palette.info.main}, 0.1)`
                                                                : `rgba(${theme.palette.warning.main}, 0.1)`,
                                                        color: item.status === "已结算"
                                                            ? theme.palette.success.main
                                                            : item.status === "已提现"
                                                                ? theme.palette.info.main
                                                                : item.status === "失败"
                                                                    ? theme.palette.error.main
                                                                    : theme.palette.warning.main,
                                                    }}
                                                >
                                                    {item.status}
                                                </Box>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </>
            )}
        </Box>
    );
} 