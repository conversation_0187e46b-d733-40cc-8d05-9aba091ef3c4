from typing import Type  # 导入Optional类型和Type

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel

from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog

PROMPT = """
# 任务
将输入的数据转化为指定的结构化格式

#输入的数据
```{data}```

{format_instructions}
"""


def structured_output_agent(model_class: Type[BaseModel], raw_data: str) -> BaseModel:
    """
    将原始数据转换为指定的 Pydantic 模型格式。

    Args:
        model_class: 目标 Pydantic 模型类
        raw_data: 需要转换的原始数据

    Returns:
        转换后的 Pydantic 模型实例
    """
    olog.info(f"开始执行结构化输出代理")
    prompt = ChatPromptTemplate.from_template(PROMPT)
    llm = ChatModelFactory.get_llm()
    parser = PydanticOutputParser(pydantic_object=model_class)

    chain = prompt | llm | parser
    chain = chain.with_retry()
    params = {"data": raw_data, "format_instructions": parser.get_format_instructions()}
    config = RunnableConfig(tags=["structured_output_agent"])
    result = chain.invoke(params, config)
    return result
