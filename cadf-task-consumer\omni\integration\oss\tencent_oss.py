# -*- coding=utf-8
"""
oss:
  region: ap-guangzhou
  secret_id: AKIDPiyNkx0wlV8Yr14ylWRJJIhzAEAtUxKb
  secret_key: HQfojQsCKEzRoyuc6MQGsHVhtAHBxYvJ
  bucket_name: projects-1323741919
"""
import io
import os
import base64
from datetime import datetime
import random
import string
from pathlib import Path

from PIL import Image
from qcloud_cos import CosConfig, CosS3Client

from omni.config.config_loader import config_dict
from omni.log.log import olog


class SignedMethod:
    PUT = "PUT"
    POST = "POST"
    GET = "GET"
    DELETE = "DELETE"
    HEAD = "HEAD"


class OSSClient:
    _instance = None

    def __init__(self):
        if OSSClient._instance is not None:
            raise Exception(
                "This class is a singleton. Use getInstance() to get the instance."
            )
        self._initialize()

    def _initialize(self):
        secret_id = config_dict["oss"]["secret_id"]
        secret_key = config_dict["oss"]["secret_key"]
        region = config_dict["oss"]["region"]
        config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key)
        self.bucket_name = config_dict["oss"]["bucket_name"]
        self.client = CosS3Client(config)

    @classmethod
    def getInstance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def get_client(self):
        """
        暴露内部的 CosS3Client 客户端实例。

        :return: CosS3Client 实例
        """
        return self.client

    def signed_url(self, method, key):
        return self.client.get_presigned_url(
            Bucket=self.bucket_name, Key=key, Method=method, Expired=604800
        )

    def upload_file(self, parent_key, local_file_path):
        # 生成随机文件名
        suffix = os.path.splitext(local_file_path)[1]
        time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        rand_part = "".join(random.choices(string.ascii_letters + string.digits, k=6))
        key = f"{parent_key}/{time_str}_{rand_part}{suffix}"

        # 上传文件
        self.client.upload_file(
            Bucket=self.bucket_name,
            LocalFilePath=local_file_path,
            Key=key,
            PartSize=1,
            MAXThread=10,
            EnableMD5=False,
        )
        olog.info(f"文件上传成功，路径为: {key}")
        return key

    def download_file(self, key, local_file_dir="tmp"):
        """
        从 OSS 下载文件到本地目录。

        :param key: 文件在 OSS 中的 key
        :param local_file_dir: 本地存储目录，默认为 "tmp"
        :return: 下载后的本地文件路径
        """
        # 确保目录存在
        Path(local_file_dir).mkdir(parents=True, exist_ok=True)

        # 生成随机文件名
        file_extension = os.path.splitext(key)[1]
        time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        rand_part = "".join(random.choices(string.ascii_letters + string.digits, k=6))
        local_file_path = f"{local_file_dir}/{time_str}_{rand_part}{file_extension}"

        # 下载文件
        response = self.client.get_object(Bucket=self.bucket_name, Key=key)
        response["Body"].get_stream_to_file(local_file_path)
        olog.info(f"文件下载成功，路径为: {local_file_path}")
        return local_file_path

    def upload_bytes(self, parent_key: str, file_extension: str, file_bytes: bytes) -> str:
        """
        将原始的 bytes 数据上传到 OSS，通过临时文件作为中转。

        :param parent_key: OSS中的父路径（类似目录）
        :param file_extension: 文件的扩展名 (例如: '.png', '.jpg')，确保包含 '.'
        :param file_bytes: 原始的文件内容 bytes
        :return: 上传成功后的完整文件 key
        """
        if not file_extension.startswith("."):
            file_extension = "." + file_extension

        temp_dir = Path("tmp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        temp_filename = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}_{''.join(random.choices(string.ascii_letters + string.digits, k=6))}{file_extension}"
        temp_file_path = temp_dir / temp_filename

        try:
            with open(temp_file_path, "wb") as f:
                f.write(file_bytes)

            key = self.upload_file(parent_key, str(temp_file_path))
            return key
        finally:
            if temp_file_path.exists():
                os.remove(temp_file_path)

    def download_bytes(self, key: str) -> bytes:
        """
        根据 key 从 OSS 下载文件，并返回原始的 bytes 数据。

        :param key: 文件在 OSS 中的 key
        :return: 原始的文件内容 bytes
        """
        temp_file_path = self.download_file(key, "tmp")
        try:
            with open(temp_file_path, "rb") as f:
                return f.read()
        finally:
            if temp_file_path and Path(temp_file_path).exists():
                os.remove(temp_file_path)
