import base64
import io

import httpx
import pillow_avif
from PIL import Image

# 不要删除
pillow_avif.__version__


def convert_png_base64_encode(image_url: str) -> str:
    """
    下载图片，将其转换为PNG格式，并返回Base64编码的字符串。

    :param image_url: 图片的URL
    :return: Base64编码的PNG图片数据
    """
    try:
        response = httpx.get(image_url)
        response.raise_for_status()  # 确保请求成功
        img_data = response.content

        # 从内存中读取图片
        img = Image.open(io.BytesIO(img_data))

        # 转换为PNG格式到内存中
        with io.BytesIO() as output:
            img.save(output, format="PNG")
            png_data = output.getvalue()

        # Base64编码
        base64_encoded_data = base64.b64encode(png_data).decode('utf-8')
        return base64_encoded_data
    except httpx.HTTPStatusError as e:
        # 可以根据需要添加更详细的错误处理和日志记录
        print(f"请求图片失败: {e.response.status_code} - {e.request.url}")
        raise
    except Exception as e:
        print(f"处理图片时发生错误: {e}")
        raise
