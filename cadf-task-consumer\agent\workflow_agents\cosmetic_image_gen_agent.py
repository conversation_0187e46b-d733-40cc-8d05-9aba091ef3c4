import io

from PIL import Image

from agent.standard_agents.add_caption_to_image import add_caption_to_image
from agent.standard_agents.classify_image_category import classify_image_category
from agent.standard_agents.clone_compliant_character import clone_compliant_character
from agent.standard_agents.clone_compliant_object import clone_compliant_object
from agent.standard_agents.generate_caption_from_image_text import (
    generate_caption_from_image_text,
)
from agent.standard_agents.replace_product_in_image import replace_product_in_image
from omni.log.log import olog


def cosmetic_image_gen_agent(
        scene_image_bytes: bytes, product_image_bytes: bytes, product_intro: str
) -> bytes | None:
    """
    美妆图片生成工作流智能体。
    根据输入的场景图片、产品图片和产品介绍，执行分类、图像处理、配文生成和图文合成。
    - scene_image_bytes: 场景图片的字节数据。
    - product_image_bytes: 产品图片的字节数据。
    - product_intro: 产品介绍文本。
    返回: 处理完成并添加了配文的图片字节，或在失败时返回 None。
    """
    olog.info("启动 赛博广告工厂 - 图片生成流程...")

    Image.open(io.BytesIO(scene_image_bytes))  # 验证场景图片数据
    Image.open(io.BytesIO(product_image_bytes))  # 验证产品图片

    olog.info("图片验证成功。")

    processed_image_bytes = None
    category = "其他"  # 默认分类

    # 步骤 1: 判断场景图片产品类型
    olog.info("步骤 1: 判断场景图片产品类型...")
    category = classify_image_category(scene_image_bytes)
    if category not in ["美妆产品", "人像", "其他"]:
        olog.warning("图片分类失败或返回格式不正确，将按 '其他' 类型处理。")
        category = "其他"
    olog.info(f"图片类型判定: {category}")

    # 步骤 2: 根据类型处理图片
    olog.info(f"步骤 2: 根据类型 '{category}' 处理图片...")
    if category == "美妆产品":
        olog.info("美妆产品类别：已加载产品图片。")
        processed_image_bytes = replace_product_in_image(scene_image_bytes, product_image_bytes)
    elif category == "人像":
        processed_image_bytes = clone_compliant_character(scene_image_bytes)
    else:
        olog.info(f"处理 '{category}' 类型图片，执行通用图片克隆。")
        processed_image_bytes = clone_compliant_object(scene_image_bytes)

    if not processed_image_bytes:
        olog.error("图片处理步骤未能生成有效图片。流程终止。")
        return None
    olog.info("步骤 2 图片处理完成。")

    # 步骤 3: 生成新配文
    olog.info("步骤 3: 生成新配文...")
    additional_text_for_caption_gen = product_intro if product_intro else "请根据图片内容生成合适的推广文案。"
    new_caption = generate_caption_from_image_text(scene_image_bytes, additional_text_for_caption_gen)
    if not new_caption:
        olog.info("没有识别到文字，跳过配文生成。")
        return processed_image_bytes

    olog.info(f"新配文生成成功: {new_caption}")

    # 步骤 4: AI思考文字位置并合成最终图
    olog.info("步骤 4: AI思考文字位置并合成最终图...")
    captions_for_final_image = new_caption['items'] if 'items' in new_caption else []
    if not captions_for_final_image:
        olog.info("没有可用配文，跳过加字。");
        return processed_image_bytes
    final_image_bytes = add_caption_to_image(
        image_bytes=processed_image_bytes,
        captions=captions_for_final_image,
    )
    if not final_image_bytes:
        olog.error("添加文字到图片失败。返回未加文字的已处理图片。")
        final_image_bytes = processed_image_bytes

    olog.info("赛博广告工厂 - 图片生成流程已完成。")
    return final_image_bytes
