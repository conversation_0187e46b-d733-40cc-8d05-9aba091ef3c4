import base64
from typing import List

import httpx
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field

from omni.llm.chat.chat_model_factory import ChatModelFactory
from utils.image_tools import convert_png_base64_encode

PROMPT = """
# 角色
你是一名专业的图片分析师和行业专家。

# 背景
系统中已有的领域列表如下，请在识别时参考：
{existing_domains_str}

# 任务
根据输入的多张图片内容，识别所有图片中展示的产品或服务可能属于的领域。

# 约束
1. 综合分析所有图片，识别产品或服务所属的所有唯一领域。
2. 优先使用或参考系统已有的相似领域 (`{existing_domains_str}`)，若无相关领域则生成新的领域名称。
3. 返回包含所有可能的领域。

# 返回值
{format_instructions}
"""


class DomainList(BaseModel):
    domains: List[str] = Field(description="所有图片中识别出的唯一领域列表，参考了系统已有领域")


def product_domain_image_recognition_agent(image_urls: list[str], existing_domains: List[str]) -> List[str]:
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    parser = PydanticOutputParser(pydantic_object=DomainList)

    existing_domains_str = "\n".join([f"- {domain}" for domain in existing_domains]) if existing_domains else "无"

    image_data = [
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{convert_png_base64_encode(url)}"
            }
        }
        for url in image_urls
    ]

    prompt_text = PROMPT.format(
        existing_domains_str=existing_domains_str,
        format_instructions=parser.get_format_instructions()
    )

    human_message_content = [
        {"type": "text", "text": prompt_text},
        *image_data,
    ]

    messages = [HumanMessage(content=human_message_content)]

    chain = llm | parser

    result: DomainList = chain.invoke(messages)
    print(f"Identified domains considering existing ones: {result.domains}")
    return result.domains
