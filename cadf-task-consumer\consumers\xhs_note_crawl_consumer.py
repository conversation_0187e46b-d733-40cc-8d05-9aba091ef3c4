import time
import random
import requests
import os
import tempfile
from mongoengine.errors import DoesNotExist

from config.config import XHS_NOTE_CRAWL_SET, OSS_PIC_DIR
from omni.integration.oss.tencent_oss import OSSClient
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from repository.models import AiBasicMaterial, ImageInfo, Account
from scraper.xhs_note_scraper import xhs_note_scraper

MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 5

# --- Helper Functions ---

def _fetch_material(material_id: str) -> AiBasicMaterial | None:
    """获取 AiBasicMaterial 对象。"""
    try:
        return AiBasicMaterial.objects.get(id=material_id)
    except AiBasicMaterial.DoesNotExist:
        olog.error(f"未找到 AiBasicMaterial，ID: {material_id}")
        return None
    except Exception as e:
        olog.error(f"获取 AiBasicMaterial 时出错，ID: {material_id}: {e}")
        return None

def _get_random_xhs_online_account_cookie() -> list | None:
    """随机获取一个在线小红书账户的 cookie 列表。"""
    try:
        # 从所有用户中查找在线的小红书账户
        online_accounts = Account.objects(platform="小红书", status="在线")
        if online_accounts:
            account = random.choice(list(online_accounts))
            if account.cookie:
                olog.info(f"随机选择账户 {account.id} (用户 {account.user_id}) 的 cookie。")
                return account.cookie
            else:
                olog.warning(f"随机选择的账户 {account.id} (用户 {account.user_id}) 没有 cookie。")
                return None
        else:
            olog.warning(f"未找到任何在线的小红书账户。")
            return None
    except Exception as e:
        olog.error(f"获取随机小红书账户/cookie 时出错: {e}")
        return None

def _scrape_xhs_note_with_retry(material_id: str, share_url: str) -> dict | None:
    """尝试爬取小红书笔记，包含重试逻辑，使用随机平台账户。"""
    scraped_data = None
    last_exception = None

    for attempt in range(MAX_RETRIES):
        olog.info(f"尝试爬取 (第 {attempt + 1}/{MAX_RETRIES} 次) - Material ID: {material_id}, URL: {share_url}")
        cookies_list = None # 每次重试前重置
        try:
            # 尝试获取随机的在线账户cookie
            cookies_list = _get_random_xhs_online_account_cookie()
            if not cookies_list:
                 olog.warning(f"无法获取任何随机小红书 cookie，将尝试无 cookie 爬取 (尝试 {attempt + 1})。")
                 cookies_list = [] # 确保是空列表而不是 None

            # 调用爬虫
            scraped_data = xhs_note_scraper(xhs_url=share_url, mongo_cookies=cookies_list)

            # 检查爬取结果是否有效
            if scraped_data and scraped_data.get("title") != "未找到标题":
                olog.info(f"爬取成功 (尝试 {attempt + 1}) - Material ID: {material_id}")
                return scraped_data # 成功，返回数据
            elif scraped_data:
                 olog.warning(f"爬虫未能从 {share_url} 获取有效数据 (标题未找到)。(Material ID: {material_id}, 尝试 {attempt + 1})")
                 # 根据业务决定是否重试或返回部分数据
                 # return scraped_data # 如果允许部分数据，可以取消注释
            else:
                 olog.error(f"爬取失败，未返回数据，URL: {share_url} (Material ID: {material_id}, 尝试 {attempt + 1})")

            last_exception = ValueError("爬取失败或数据无效")

        except Exception as e:
            last_exception = e
            olog.error(f"爬取或账户获取过程中发生异常 (尝试 {attempt + 1})，URL: {share_url} (Material ID: {material_id}): {e}")

        # 等待后重试
        if attempt < MAX_RETRIES - 1:
            olog.info(f"等待 {RETRY_DELAY_SECONDS} 秒后重试...")
            time.sleep(RETRY_DELAY_SECONDS)
        else:
            olog.error(f"所有 {MAX_RETRIES} 次爬取尝试均失败。Material ID: {material_id}, URL: {share_url}. 最后错误: {last_exception}")
            return None # 所有重试失败

    return None # 理论上不会执行到这里，但为了清晰

def _process_images(image_urls: list[str]) -> list[ImageInfo]:
    """下载图片并上传到 OSS。"""
    formatted_images = []
    if not image_urls:
        return formatted_images

    oss_client = OSSClient.getInstance()
    parent_key = f"{OSS_PIC_DIR}"

    for i, img_url in enumerate(image_urls):
        temp_file_path = None
        try:
            response = requests.get(img_url, stream=True, timeout=30)
            response.raise_for_status()

            file_extension = os.path.splitext(img_url.split('?')[0])[-1]
            if not file_extension:
                 content_type = response.headers.get('Content-Type')
                 if content_type and '/' in content_type:
                     guessed_extension = '.' + content_type.split('/')[-1].split(';')[0]
                     if guessed_extension.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']:
                         file_extension = guessed_extension
                     else:
                         file_extension = '.jpg'
                 else:
                     file_extension = '.jpg'

            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                for chunk in response.iter_content(chunk_size=8192):
                    temp_file.write(chunk)
                temp_file_path = temp_file.name

            # 上传到OSS
            try:
                oss_key = oss_client.upload_file(parent_key=parent_key, local_file_path=temp_file_path)
                formatted_images.append(ImageInfo(oss_key=oss_key, order=i))
                olog.info(f"图片 {i+1}/{len(image_urls)} 上传成功: URL={img_url}, OSS Key={oss_key}")
            except Exception as upload_err:
                olog.error(f"上传图片失败 (URL: {img_url}): {upload_err}")

        except requests.exceptions.RequestException as download_err:
            olog.error(f"下载图片失败 (URL: {img_url}): {download_err}")
        except Exception as e:
             olog.error(f"处理图片时发生未知错误 (URL: {img_url}): {e}")
        finally:
            if temp_file_path and os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    return formatted_images


def _update_material(material: AiBasicMaterial, scraped_data: dict, formatted_images: list[ImageInfo]):
    """使用爬取的数据更新 AiBasicMaterial。"""
    scraped_title = scraped_data.get("title")
    scraped_content = scraped_data.get("content")

    update_fields = {}
    if scraped_title and scraped_title != "未找到标题":
         update_fields['set__title'] = scraped_title
    if scraped_content is not None:
         update_fields['set__content'] = scraped_content
    if formatted_images:
         update_fields['set__images'] = formatted_images
    # 无论是否有其他更新，只要走到这里，就标记为已完成
    update_fields['set__fetch_status'] = "已完成"
    # update_fields['set__last_scraped_at'] = int(time.time()) # 可选

    if not update_fields: # 理论上因为 fetch_status 总会被设置，这个判断可以移除，但保留以防万一
        olog.warning(f"没有有效的爬取数据可用于更新 AiBasicMaterial {material.id}")
        # 即使没有标题/内容/图片更新，也应标记为完成，因为爬取过程是成功的
        material.update(set__fetch_status="已完成")
        olog.info(f"AiBasicMaterial {material.id} (来自 {material.share_url}) 爬取成功，但无内容更新，标记为已完成。")
        return

    try:
        material.update(**update_fields)
        olog.info(f"成功使用爬取的数据更新了 AiBasicMaterial {material.id} (来自 {material.share_url})，状态更新为 '已完成'")
    except Exception as e:
        olog.error(f"更新 AiBasicMaterial 时出错，ID: {material.id}: {e}")
        # 更新失败，尝试将状态标记为失败
        try:
            material.update(set__fetch_status="失败")
            olog.warning(f"尝试将 AiBasicMaterial {material.id} 的状态标记为 '失败'，因为更新时出错。")
        except Exception as inner_e:
            olog.error(f"尝试标记 AiBasicMaterial {material.id} 为 '失败' 时再次出错: {inner_e}")


# --- Main Consumer Function ---

@consume_redis_set(redis_key=XHS_NOTE_CRAWL_SET, num_threads=1)
def handle_task(material_id: str):
    """
    处理单个小红书笔记爬取任务 (已重构)。
    :param material_id: AiBasicMaterial 的 ID。
    """
    olog.info(f"开始处理 AiBasicMaterial ID: {material_id}")

    # 1. 获取 Material
    material = _fetch_material(material_id)
    if not material:
        # 错误已在 _fetch_material 中记录，无需额外操作
        return

    # 检查是否已经是完成或进行中状态，避免重复处理 (可选逻辑)
    # if material.fetch_status in ["已完成", "爬取中"]:
    #     olog.warning(f"AiBasicMaterial {material_id} 状态为 '{material.fetch_status}'，跳过处理。")
    #     return

    # 标记为爬取中
    try:
        material.update(set__fetch_status="爬取中")
        olog.info(f"AiBasicMaterial {material_id} 状态更新为 '爬取中'")
    except Exception as e:
        olog.error(f"更新 AiBasicMaterial {material_id} 状态为 '爬取中' 时失败: {e}")
        # 即使更新状态失败，也可能继续尝试爬取，或者直接返回错误
        return # 如果状态更新很重要，则直接返回

    try:
        share_url = material.share_url
        if not share_url:
            olog.error(f"AiBasicMaterial {material_id} 缺少 share_url。")
            material.update(set__fetch_status="失败") # 标记为失败
            return

        # 2. 爬取数据 (包含重试逻辑, 使用随机平台账户)
        scraped_data = _scrape_xhs_note_with_retry(material_id, share_url)
        if not scraped_data:
            olog.error(f"爬取数据失败或未获取有效数据，处理终止。Material ID: {material_id}")
            material.update(set__fetch_status="失败") # 标记为失败
            return

        # 3. 处理图片 (下载和上传)
        scraped_image_urls = scraped_data.get("images", [])
        formatted_images = _process_images(scraped_image_urls)

        # 4. 更新 Material (包括设置状态为 "已完成")
        _update_material(material, scraped_data, formatted_images)

    except Exception as e:
        olog.error(f"处理 AiBasicMaterial {material_id} 过程中发生未捕获的异常: {e}", exc_info=True)
        try:
            # 发生任何未预料的错误，都标记为失败
            material.update(set__fetch_status="失败")
            olog.info(f"因处理过程中发生异常，AiBasicMaterial {material_id} 状态更新为 '失败'")
        except Exception as update_err:
            olog.error(f"尝试将 AiBasicMaterial {material_id} 标记为 '失败' 时出错: {update_err}")

    olog.info(f"完成处理 AiBasicMaterial ID: {material_id}")
