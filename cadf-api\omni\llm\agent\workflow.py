from typing import Callable, Type, Any, List

from langgraph.graph import StateGraph
from langgraph.graph.graph import CompiledGraph


def build_agent_workflow(nodes: List[Callable], state: Type[Any]) -> CompiledGraph:
    """创建 AI 代理工作流图"""
    workflow = StateGraph(state)

    # 检查 nodes 列表是否为空
    if not nodes:
        raise ValueError("nodes 列表不能为空。")

    # 检查必需的节点是否存在
    node_names = [node.__name__ for node in nodes]
    if "start_node" not in node_names:
        raise ValueError("必须包含 'start_node' 节点")
    if "end_node" not in node_names:
        raise ValueError("必须包含 'end_node' 节点")

    workflow.set_entry_point("start_node")
    workflow.set_finish_point("end_node")

    # 添加节点
    for node_func in nodes:
        node_name = node_func.__name__
        workflow.add_node(node_name, node_func)

    # 为每个节点添加条件边, 除了 end_node
    for node_name in node_names:
        if node_name != "end_node":
            workflow.add_conditional_edges(node_name, lambda x: x['next_node'])

    return workflow.compile()
