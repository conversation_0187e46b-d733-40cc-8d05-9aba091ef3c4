import time
import traceback
from datetime import datetime, timedelta, time as dt_time

import pytz

from omni.log.log import olog
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from repository.models import (
    PromotionTaskDetail,
    AccountMetrics,
    AiGeneratedMaterial,
    DailyTaskRevenue,
)


@register_scheduler(trigger="cron", hour="7", minute="0")
# @register_scheduler(trigger='interval', seconds=10)  # 每10秒执行一次
class DailyTaskRevenueScheduler(BaseScheduler):
    def run_task(self):
        """
        **目的**: 定时计算每个推广任务在前一天的浏览量增量和对应收益，并记录到数据库（考虑上海时区）。

        **算法描述**:
            - **计算时间范围**: 确定需要计算收益的日期（基于上海时区的昨天），并获取该日期的开始和结束 UTC 时间戳。
            - **筛选任务**: 从 `PromotionTaskDetail` 中筛选出所有状态为"成功"且已关联 `account_id` 的任务详情。
            - **遍历任务详情**: 逐一处理筛选出的任务详情。
                - **有效性检查**: 跳过没有 `account_id` 或 `ai_generated_material_id` 的任务详情。
                - **获取帖子标题**: 根据 `ai_generated_material_id` 找到关联的 `AiGeneratedMaterial` 记录，获取其 `title`。如果找不到素材或标题为空，则跳过。
                - **查询结束时指标**: 在 `AccountMetrics` 中查找该账号 (`account_id`) 下对应标题 (`title`) 的帖子在**昨天结束时间点 (`end_of_yesterday_utc_ts`) 或之前**的最新 `crawled_at` 记录。
                - **查询开始前指标**: 在 `AccountMetrics` 中查找该账号 (`account_id`) 下对应标题 (`title`) 的帖子在**昨天开始时间点 (`start_of_yesterday_utc_ts`) 之前**的最新 `crawled_at` 记录。
                - **发布时间验证**: 对查询到的两个指标（结束时指标和开始前指标），分别检查其 `publish_time` 与 `PromotionTaskDetail` 中的 `publish_at` 的时间差。如果绝对差值超过 1 天 (86400 秒)，则认为该指标的发布时间与任务记录不符（可能存在作弊行为或数据异常），将该指标对应的浏览量视为 0，并记录警告。
                - **计算日增量**: 计算**经过发布时间验证后**的昨天结束时的浏览量减去**经过发布时间验证后**的昨天开始前的浏览量，得到昨天的浏览量增量（确保非负）。
                - **计算日收益**: 根据浏览量增量计算收益（单位：分，每 100 浏览量 = 1 分）。
                - **记录/更新收益**: 如果日浏览量增量大于 0，则在 `DailyTaskRevenue` 集合中为该任务 (`promotion_task_detail_id`) 和对应用户 (`user_id`) 创建或更新昨天的收益记录。使用昨天的开始 UTC 时间戳 (`start_of_yesterday_utc_ts`) 作为日期标识，并记录日增量和日收益。
            - **日志记录**: 记录任务启动、结束信息，以及处理过程中可能出现的错误和最终统计数据（处理任务数、产生收益的任务数）。
            - **异常处理**: 捕获并记录处理单个任务及整个调度任务执行过程中的任何异常。
        """
        olog.info("开始执行每日任务收益计算任务...")

        try:

            shanghai_tz = pytz.timezone('Asia/Shanghai')

            now_shanghai = datetime.now(shanghai_tz)
            yesterday_shanghai = now_shanghai.date() - timedelta(days=1)

            start_dt_shanghai = shanghai_tz.localize(datetime.combine(yesterday_shanghai, dt_time.min))
            end_dt_shanghai = shanghai_tz.localize(datetime.combine(yesterday_shanghai, dt_time.max))

            start_of_yesterday_utc_ts = int(start_dt_shanghai.timestamp())
            end_of_yesterday_utc_ts = int(end_dt_shanghai.timestamp())

            olog.info(f"计算日期 (上海): {yesterday_shanghai}, 开始 UTC 时间戳: {start_of_yesterday_utc_ts}, 结束 UTC 时间戳: {end_of_yesterday_utc_ts}")

            tasks_to_process = PromotionTaskDetail.objects(
                validation_status="成功", account_id__exists=True
            )
            olog.info(f"找到 {tasks_to_process.count()} 个需要处理的任务详情。")

            processed_count = 0
            revenue_generated_count = 0
            for task_detail in tasks_to_process:
                try:

                    if not task_detail.account_id or not task_detail.ai_generated_material_id:
                        continue

                    material = AiGeneratedMaterial.objects(
                        id=task_detail.ai_generated_material_id
                    ).first()
                    if not material or not material.title:
                        olog.warning(
                            f"无法找到任务 {task_detail.id} 关联的素材 {task_detail.ai_generated_material_id} 或素材缺少标题，跳过。"
                        )
                        continue

                    post_title = material.title
                    account_id = task_detail.account_id

                    metric_yesterday_end = (
                        AccountMetrics.objects(
                            account_id=account_id,
                            title=post_title,
                            crawled_at__lte=end_of_yesterday_utc_ts,
                        )
                        .order_by("-crawled_at")
                        .first()
                    )
                    views_yesterday_end = 0
                    if metric_yesterday_end:
                        publish_time_valid = True
                        if task_detail.publish_at and metric_yesterday_end.publish_time:
                            time_diff = abs(task_detail.publish_at - metric_yesterday_end.publish_time)
                            if time_diff > 86400:
                                publish_time_valid = False
                                olog.warning(
                                    f"任务 {task_detail.id} 的 metric_yesterday_end ({metric_yesterday_end.id}) "
                                    f"publish_time ({metric_yesterday_end.publish_time}) 与 "
                                    f"task_detail publish_at ({task_detail.publish_at}) "
                                    f"差距超过1天 ({time_diff}秒), 将忽略此指标的浏览量。"
                                )

                        if publish_time_valid:
                            views_yesterday_end = metric_yesterday_end.view_count if metric_yesterday_end.view_count is not None else 0

                    metric_before_yesterday = (
                        AccountMetrics.objects(
                            account_id=account_id,
                            title=post_title,
                            crawled_at__lt=start_of_yesterday_utc_ts,
                        )
                        .order_by("-crawled_at")
                        .first()
                    )
                    views_before_yesterday = 0
                    if metric_before_yesterday:
                        publish_time_valid = True
                        if task_detail.publish_at and metric_before_yesterday.publish_time:
                            time_diff = abs(task_detail.publish_at - metric_before_yesterday.publish_time)
                            if time_diff > 86400:
                                publish_time_valid = False
                                olog.warning(
                                    f"任务 {task_detail.id} 的 metric_before_yesterday ({metric_before_yesterday.id}) "
                                    f"publish_time ({metric_before_yesterday.publish_time}) 与 "
                                    f"task_detail publish_at ({task_detail.publish_at}) "
                                    f"差距超过1天 ({time_diff}秒), 将忽略此指标的浏览量。"
                                )

                        if publish_time_valid:
                            views_before_yesterday = metric_before_yesterday.view_count if metric_before_yesterday.view_count is not None else 0

                    daily_view_increase = max(0, views_yesterday_end - views_before_yesterday)

                    daily_revenue_cents = daily_view_increase // 100

                    if daily_view_increase > 0:
                        revenue_generated_count += 1
                        DailyTaskRevenue.objects(
                            user_id=task_detail.user_id,
                            promotion_task_detail_id=str(task_detail.id),
                            date=start_of_yesterday_utc_ts,
                        ).update_one(
                            set__daily_views=daily_view_increase,
                            set__daily_revenue=daily_revenue_cents,
                            upsert=True,
                        )

                    processed_count += 1

                except Exception as e:
                    olog.error(
                        f"处理任务详情 {task_detail.id} 时出错: {e}\n{traceback.format_exc()}"
                    )

            olog.info(
                f"每日任务收益计算完成。共处理 {processed_count} 个任务详情，其中 {revenue_generated_count} 个产生收益。"
            )

        except Exception as e:
            olog.error(f"执行每日任务收益计算任务失败: {e}\n{traceback.format_exc()}")
