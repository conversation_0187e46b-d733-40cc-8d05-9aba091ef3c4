"use client";

import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  Container,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ArrowLeft, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { advertiserManagementApi } from "@/api/advertiser-management-api";

export default function CreateAdvertiser() {
  const theme = useTheme();
  const router = useRouter();
  const dispatch = useDispatch();

  // 表单数据
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // 表单验证
  const validateForm = () => {
    const newErrors = {};
    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }
    if (!formData.password.trim()) {
      newErrors.password = '请输入密码';
    }
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = '请确认密码';
    }
    if (formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      await advertiserManagementApi.create(formData.username, formData.password);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.CREATE }));
      router.push('/protected/advertiser-management');
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '创建广告主失败' 
      }));
    } finally {
      setLoading(false);
    }
  };

  // 处理返回
  const handleBack = () => {
    router.push('/protected/advertiser-management');
  };

  return (
    <Box sx={{ py: 4 }}>
      {/* 返回按钮 */}
      <Box sx={{ mb: 3 }}>
        <Button
          startIcon={<ArrowLeft size={18} />}
          onClick={handleBack}
          variant="text"
          sx={{ color: 'text.secondary' }}
        >
          返回
        </Button>
      </Box>

      <Container maxWidth="sm">
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Typography variant="h5" component="h1" fontWeight="bold">
            新增广告主
          </Typography>
        </Box>

        {/* 表单 */}
        <Paper 
          elevation={0} 
          sx={{ 
            p: 3,
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                label="用户名"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                fullWidth
                required
                error={!!errors.username}
                helperText={errors.username}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="密码"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                fullWidth
                required
                error={!!errors.password}
                helperText={errors.password}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="确认密码"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                fullWidth
                required
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  disabled={loading}
                >
                  取消
                </Button>
                <Button
                  variant="contained"
                  startIcon={<Save size={18} />}
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  {loading ? '创建中...' : '创建'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Container>
    </Box>
  );
} 