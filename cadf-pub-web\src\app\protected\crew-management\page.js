"use client";

import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, <PERSON>ton, Card, CardActionArea, CardContent, Chip, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, InputAdornment, Pagination, Paper, Snackbar, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme, CircularProgress, Skeleton} from '@mui/material';
import {AlertTriangle, Check, ChevronRight, Clock, DollarSign, LogIn, RefreshCw, Search, UserCheck, UserX, X, Users} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {useDispatch} from "react-redux";
import {addAlert} from "@/core/components/redux/alert-slice";
import {AlertType, AlertMsg} from "@/core/components/alert";
import {crewManagementApi} from '@/api/crew-management-api'; // 导入API

export default function CrewManagement() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;
    const dispatch = useDispatch();

    // New state: statistics data
    const [stats, setStats] = useState({online_count: 0, offline_count: 0, active_count: 0, inactive_count: 0});
    const [loadingStats, setLoadingStats] = useState(true);

    // New state: ranking data
    const [rankingData, setRankingData] = useState({crew: [], total_count: 0});
    const [loadingRanking, setLoadingRanking] = useState(true);

    // New state: crew count
    const [crewCount, setCrewCount] = useState(0);
    const [loadingCrewCount, setLoadingCrewCount] = useState(true);

    // Invitation code state (unchanged)
    const [invitationDialogOpen, setInvitationDialogOpen] = useState(false);
    const [invitationCode, setInvitationCode] = useState('');
    const [invitationCodeLoading, setInvitationCodeLoading] = useState(false);
    const [invitationCodeCopied, setInvitationCodeCopied] = useState(false);

    // Feature card data (unchanged)
    const featureCards = [
        {
            title: '舰队人数',
            description: '查看您当前管理的舰员总数',
            icon: <Users size={36}/>,
            action: null,
            color: '#2196f3',
            infoKey: 'crewCount'
        },
        {
            title: '管理邀请码',
            description: '管理和生成邀请舰员的邀请码',
            icon: <RefreshCw size={36}/>,
            action: () => handleInvitationDialog(),
            color: '#4caf50'
        }
    ];

    // Fetch statistics data
    const fetchStats = async () => {
        setLoadingStats(true);
        try {
            const response = await crewManagementApi.getCrewStats();
            if (response) {
                setStats(response);
            } else {
                 setStats({online_count: 0, offline_count: 0, active_count: 0, inactive_count: 0}); // Reset on error/null
            }
        } catch (error) {
            console.error("获取舰员统计失败:", error);
            dispatch(addAlert({type: AlertType.ERROR, message: "获取舰员统计失败"}));
            setStats({online_count: 0, offline_count: 0, active_count: 0, inactive_count: 0}); // Reset on error
        } finally {
            setLoadingStats(false);
        }
    };

    // Fetch ranking data
    const fetchRanking = async (currentPage = 1) => {
        setLoadingRanking(true);
        try {
            const response = await crewManagementApi.getCrewRevenueRanking(currentPage, rowsPerPage);
            if (response) {
                setRankingData(response);
            } else {
                 setRankingData({crew: [], total_count: 0}); // API might return null/undefined
            }
        } catch (error) {
            console.error("获取舰员排名失败:", error);
            dispatch(addAlert({type: AlertType.ERROR, message: "获取舰员排名失败"}));
            setRankingData({crew: [], total_count: 0}); // Reset on error
        } finally {
            setLoadingRanking(false);
        }
    };

    // Fetch crew count
    const fetchCrewCount = async () => {
        setLoadingCrewCount(true);
        try {
            const response = await crewManagementApi.getCrewCount();
            if (response && typeof response.crew_count === 'number') {
                setCrewCount(response.crew_count);
            } else {
                setCrewCount(0); // Reset on error/invalid data
            }
        } catch (error) {
            console.error("获取舰员数量失败:", error);
            dispatch(addAlert({type: AlertType.ERROR, message: "获取舰员数量失败"}));
            setCrewCount(0); // Reset on error
        } finally {
            setLoadingCrewCount(false);
        }
    };

    // Fetch data on component mount
    useEffect(() => {
        fetchStats();
        fetchRanking(page); // Fetch first page of rankings
        fetchCrewCount(); // Fetch crew count
    }, [page]); // Update dependency array to include page if fetchRanking depends on it, otherwise keep empty

    // Handle pagination change
    const handlePageChange = (event, newPage) => {
        setPage(newPage);
        fetchRanking(newPage); // Fetch data for the new page
    };

    // Navigate to feature page or execute action (unchanged, but removed disabled features)
    const navigateToFeature = (path, action, disabled) => {
        if (disabled) {
            dispatch(addAlert({type: AlertType.INFO, message: "该功能暂未开放，敬请期待"}));
            return;
        }
        if (action) {
            action();
            return;
        }
        router.push(path);
    };

    // Invitation code related functions (unchanged)
    const handleInvitationDialog = async () => {
        setInvitationCodeLoading(true);
        setInvitationDialogOpen(true);
        try {
            const response = await crewManagementApi.getInviteCode();
            if (response && response.invite_code) {
                setInvitationCode(response.invite_code);
            } else {
                 await handleGenerateNewCode(false);
            }
        } catch (error) {
            console.error("获取邀请码失败:", error);
            dispatch(addAlert({type: AlertType.ERROR, message: "获取邀请码失败，请稍后重试"}));
            setInvitationCode('');
        } finally {
            setInvitationCodeLoading(false);
        }
    };

    const handleCloseInvitationDialog = () => {
        setInvitationDialogOpen(false);
        setInvitationCodeCopied(false);
    };

    const handleCopyInvitationCode = () => {
        if (!invitationCode) return;
        navigator.clipboard.writeText(invitationCode);
        setInvitationCodeCopied(true);
        setTimeout(() => setInvitationCodeCopied(false), 2000);
    };

    const handleGenerateNewCode = async (showLoading = true) => {
        if(showLoading) setInvitationCodeLoading(true);
        try {
            const response = await crewManagementApi.generateInviteCode();
            if (response && response.invite_code) {
                setInvitationCode(response.invite_code);
                dispatch(addAlert({type: AlertType.SUCCESS, message: "已生成新的邀请码"}));
            } else {
                 dispatch(addAlert({type: AlertType.ERROR, message: "生成邀请码失败"}));
            }
        } catch (error) {
            console.error("生成邀请码失败:", error);
             dispatch(addAlert({type: AlertType.ERROR, message: "生成邀请码失败，请稍后重试"}));
        } finally {
             if(showLoading) setInvitationCodeLoading(false);
        }
    };

    // Define skeleton loading components
    const StatSkeleton = () => <Skeleton variant="rectangular" width="100%" height={118} />; // Adjusted height to match cards
    const TableSkeleton = () => (
        <TableContainer component={Paper} variant="outlined">
            <Table>
                <TableHead>
                    <TableRow>
                         <TableCell><Skeleton width="80%" /></TableCell>
                         <TableCell sx={{display: {xs: 'none', sm: 'table-cell'}}}><Skeleton width="60%" /></TableCell>
                         <TableCell><Skeleton width="70%" /></TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {[...Array(rowsPerPage)].map((_, index) => (
                        <TableRow key={index}>
                            <TableCell><Skeleton /></TableCell>
                            <TableCell sx={{display: {xs: 'none', sm: 'table-cell'}}}><Skeleton /></TableCell>
                            <TableCell><Skeleton /></TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
     const MobileCardSkeleton = () => (
        <Box>
            {[...Array(rowsPerPage)].map((_, index) => (
                <Card key={index} variant="outlined" sx={{mb: 1.5}}>
                    <CardContent sx={{p: 1.5}}>
                         <Skeleton variant="text" sx={{ fontSize: '1rem', width: '60%' }} />
                         <Skeleton variant="text" sx={{ fontSize: '0.8rem', width: '80%' }} />
                         <Skeleton variant="text" sx={{ fontSize: '0.8rem', width: '70%' }} />
                    </CardContent>
                </Card>
            ))}
        </Box>
    );

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Typography variant="h4" component="h1" gutterBottom sx={{mb: 3, fontWeight: 500, fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}}}>
                舰员管理
            </Typography>

            {/* Feature card area */}
            <Box sx={{mb: 4}}>
                <Typography variant="h5" sx={{mb: 2, fontSize: {xs: '1.1rem', sm: '1.2rem', md: '1.5rem'}}}>
                    功能菜单
                </Typography>
                <Grid container spacing={{xs: 1, sm: 2}}>
                    {featureCards.map((card, index) => {
                        const cardContent = (
                            <CardContent sx={{height: '100%', p: {xs: 1.5, sm: 2}}}>
                                <Box
                                    sx={{
                                        mb: 2,
                                        color: card.color,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between'
                                    }}
                                >
                                    {card.icon}
                                    {card.infoKey !== 'crewCount' && <ChevronRight size={isMobile ? 16 : 20}/>}
                                </Box>
                                <Typography variant="h6" component="div" gutterBottom sx={{fontSize: {xs: '1rem', sm: '1.1rem', md: '1.25rem'}}}>
                                    {card.title}
                                </Typography>
                                {card.infoKey === 'crewCount' ? (
                                    loadingCrewCount ? (
                                        <Skeleton variant="text" width="50%" />
                                    ) : (
                                        <Typography variant="h4" component="div" sx={{fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}, fontWeight: 'bold'}}>
                                             {crewCount}
                                        </Typography>
                                    )
                                ) : (
                                     <Typography variant="body2" color="text.secondary">
                                         {card.description}
                                     </Typography>
                                )}
                            </CardContent>
                        );

                        return (
                            <Grid item xs={12} sm={6} md={4} key={index}> {/* Adjusted grid size for potentially 3 cards */}
                                <Card
                                    sx={{
                                        height: '100%',
                                        opacity: card.disabled ? 0.7 : 1,
                                        position: 'relative',
                                        overflow: 'visible',
                                        // Make non-actionable cards visually distinct if desired
                                        // cursor: card.infoKey === 'crewCount' || card.disabled ? 'default' : 'pointer'
                                    }}
                                >
                                    {card.disabled && (
                                        <Box
                                            sx={{
                                                position: 'absolute',
                                                top: -10,
                                                right: -10,
                                                bgcolor: 'grey.300',
                                                color: 'text.secondary',
                                                py: 0.5,
                                                px: 1.5,
                                                borderRadius: 1,
                                                fontSize: '0.75rem',
                                                fontWeight: 500,
                                                zIndex: 1,
                                                boxShadow: 1
                                            }}
                                        >
                                            即将上线
                                        </Box>
                                    )}
                                    {card.infoKey === 'crewCount' || card.disabled ? (
                                         // Render content directly for non-actionable cards
                                         cardContent
                                    ) : (
                                        // Wrap content in CardActionArea for actionable cards
                                        <CardActionArea
                                            sx={{height: '100%'}}
                                            onClick={() => navigateToFeature(card.path, card.action, card.disabled)}
                                        >
                                            {cardContent}
                                        </CardActionArea>
                                    )}
                                </Card>
                            </Grid>
                        );
                    })}
                </Grid>
            </Box>

            {/* Statistics cards */}
            <Box sx={{mb: 4}}>
                <Typography variant="h5" sx={{mb: 2, fontSize: {xs: '1.1rem', sm: '1.2rem', md: '1.5rem'}}}>
                    舰员状态概览
                </Typography>

                {/* Account status group */}
                <Typography variant="subtitle1" sx={{mb: 1, fontSize: {xs: '0.9rem', sm: '1rem', md: '1.1rem'}}}>
                    账号状态
                </Typography>
                <Box sx={{ maxWidth: { md: '700px' }, /* mx: 'auto', */ mb: 3 }}> {/* Reduced width slightly, removed mx: auto to keep left-aligned */}
                    <Grid container spacing={{ xs: 1, sm: 2 }}>
                        <Grid item xs={6} md={6}>
                            {loadingStats ? <StatSkeleton /> : (
                                <Card sx={{bgcolor: 'success.light', color: 'success.contrastText'}}>
                                    <CardActionArea onClick={() => router.push('/protected/crew-management/online')} sx={{height: '100%'}}>
                                        <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                                            <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                                                <Typography variant="h4" component="div" sx={{fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}}}>
                                                    {stats.online_count}
                                                </Typography>
                                                <UserCheck size={24} />
                                                {/* <ChevronRight size={20} sx={{ml: 0.5, color: 'success.dark'}} /> */}
                                            </Box>
                                            <Typography variant="body1" sx={{mt: 1}}>在线账户</Typography>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                            )}
                        </Grid>
                        <Grid item xs={6} md={6}>
                            {loadingStats ? <StatSkeleton /> : (
                                <Card sx={{bgcolor: 'grey.200'}}>
                                    <CardActionArea onClick={() => router.push('/protected/crew-management/offline')} sx={{height: '100%'}}>
                                        <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                                            <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                                                <Typography variant="h4" component="div" sx={{fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}}}>
                                                    {stats.offline_count}
                                                </Typography>
                                                <Clock size={24} />
                                                {/* <ChevronRight size={20} sx={{ml: 0.5, color: 'text.secondary'}} /> */}
                                            </Box>
                                            <Typography variant="body1" sx={{mt: 1}}>离线账户</Typography>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                             )}
                        </Grid>
                    </Grid>
                </Box>

                {/* Activity status group */}
                <Typography variant="subtitle1" sx={{mb: 1, fontSize: {xs: '0.9rem', sm: '1rem', md: '1.1rem'}}}>
                    活跃度状态
                </Typography>
                <Box sx={{ maxWidth: { md: '700px' }, /* mx: 'auto', */ mb: 3 }}> {/* Reduced width slightly, removed mx: auto */}
                    <Grid container spacing={{ xs: 1, sm: 2 }}>
                        <Grid item xs={6} md={6}>
                             {loadingStats ? <StatSkeleton /> : (
                                <Card sx={{bgcolor: 'info.light', color: 'info.contrastText'}}>
                                    <CardActionArea onClick={() => router.push('/protected/crew-management/active')} sx={{height: '100%'}}>
                                        <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                                            <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                                                <Typography variant="h4" component="div" sx={{fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}}}>
                                                    {stats.active_count}
                                                </Typography>
                                                <UserCheck size={24} />
                                                {/* <ChevronRight size={20} sx={{ml: 0.5, color: 'info.dark'}} /> */}
                                            </Box>
                                            <Typography variant="body1" sx={{mt: 1}}>活跃舰员</Typography>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                             )}
                        </Grid>
                        <Grid item xs={6} md={6}>
                            {loadingStats ? <StatSkeleton /> : (
                                <Card sx={{bgcolor: 'error.light', color: 'error.contrastText'}}>
                                    <CardActionArea onClick={() => router.push('/protected/crew-management/inactive')} sx={{height: '100%'}}>
                                        <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                                            <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                                                <Typography variant="h4" component="div" sx={{fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}}}>
                                                    {stats.inactive_count}
                                                </Typography>
                                                <UserX size={24} />
                                                {/* <ChevronRight size={20} sx={{ml: 0.5, color: 'error.dark'}} /> */}
                                            </Box>
                                            <Typography variant="body1" sx={{mt: 1}}>不活跃舰员</Typography>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                            )}
                        </Grid>
                    </Grid>
                </Box>
            </Box>

            {/* Revenue leaderboard */}
            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="h6" gutterBottom sx={{fontSize: {xs: '1rem', sm: '1.1rem', md: '1.25rem'}}}>
                        舰员收益排行榜
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        根据舰员完成的任务数量和产生的总收益进行排名。
                    </Typography>

                    {loadingRanking ? (
                         isMobile ? <MobileCardSkeleton /> : <TableSkeleton />
                    ) : isMobile ? (
                        // Mobile card layout
                        <Box>
                            {rankingData.crew.length > 0 ? (
                                rankingData.crew.map((member) => (
                                    <Card key={member.id} variant="outlined" sx={{mb: 1.5}}>
                                        <CardActionArea /* onClick={() => router.push(`/protected/crew-management/${member.id}`)} */ sx={{cursor: 'default'}}>
                                            <CardContent sx={{p: 1.5}}>
                                                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1}}>
                                                    <Typography variant="subtitle1" sx={{fontWeight: 500}}>
                                                        {member.user_account}
                                                    </Typography>
                                                    {/* <ChevronRight size={18} color={theme.palette.primary.main} /> */}
                                                </Box>
                                                <Typography variant="body2" color="text.secondary" sx={{mb: 0.5}}>
                                                    完成任务数: {member.task_count}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{mb: 0.5}}>
                                                    总收益: ¥{(member.revenue / 100).toFixed(2)} {/* Assuming backend revenue is in cents */}
                                                </Typography>
                                            </CardContent>
                                        </CardActionArea>
                                    </Card>
                                ))
                            ) : (
                                <Card variant="outlined" sx={{p: 2, textAlign: 'center'}}>
                                    <Typography>没有找到舰员数据</Typography>
                                </Card>
                            )}
                        </Box>
                    ) : (
                        // Desktop table layout
                        <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell>用户账号</TableCell>
                                        <TableCell sx={{display: {xs: 'none', sm: 'table-cell'}}}>完成任务数</TableCell>
                                        <TableCell>总收益 (元)</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {rankingData.crew.length > 0 ? (
                                        rankingData.crew.map((member) => (
                                            <TableRow key={member.id} hover>
                                                <TableCell>{member.user_account}</TableCell>
                                                <TableCell sx={{display: {xs: 'none', sm: 'table-cell'}}}>{member.task_count}</TableCell>
                                                <TableCell>¥{(member.revenue / 100).toFixed(2)}</TableCell> {/* Assuming backend revenue is in cents */}
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={3} align="center">
                                                没有找到舰员数据
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}

                    {/* Pagination controls */}
                     {rankingData.total_count > rowsPerPage && (
                        <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                            <Pagination
                                count={Math.ceil(rankingData.total_count / rowsPerPage)}
                                page={page}
                                onChange={handlePageChange}
                                color="primary"
                                size={isMobile ? "small" : "medium"}
                                showFirstButton
                                showLastButton
                                siblingCount={isMobile ? 0 : 1}
                                disabled={loadingRanking} // Disable pagination while loading
                            />
                        </Stack>
                    )}
                </CardContent>
            </Card>

            {/* Invitation code dialog (unchanged) */}
            <Dialog
                open={invitationDialogOpen}
                onClose={handleCloseInvitationDialog}
                maxWidth="sm"
                fullWidth
                fullScreen={isMobile}
            >
                 {/* Invitation dialog content */}
                 <DialogTitle sx={{
                    fontSize: {xs: '1rem', sm: '1.1rem', md: '1.25rem'},
                    p: isMobile ? 1.5 : 2
                }}>
                    {isMobile && (
                        <IconButton
                            edge="start"
                            color="inherit"
                            onClick={handleCloseInvitationDialog}
                            sx={{mr: 1}}
                        >
                            <X size={20}/>
                        </IconButton>
                    )}
                    舰员邀请码管理
                </DialogTitle>
                <DialogContent dividers sx={{p: isMobile ? 1.5 : 2}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        您可以使用以下邀请码邀请新舰员加入您的舰队。
                    </Typography>

                    <Box sx={{
                        mt: 2,
                        p: 2,
                        bgcolor: 'background.paper',
                        borderRadius: 1,
                        border: '1px dashed',
                        borderColor: 'primary.main',
                        textAlign: 'center',
                        minHeight: '50px', // Added min height for loading indicator
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        {invitationCodeLoading ? (
                            <CircularProgress size={24} />
                        ) : (
                            <Typography variant="h5" sx={{
                                fontFamily: 'monospace',
                                letterSpacing: 1,
                                fontWeight: 'bold',
                                color: 'primary.main'
                            }}>
                                {invitationCode || '暂无邀请码'}
                            </Typography>
                        )}
                    </Box>

                    <Box sx={{mt: 3, mb: 2}}>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                            邀请须知：
                        </Typography>
                        <Typography variant="body2" component="ul" sx={{pl: 2}}>
                            <li>您可以使用此邀请码邀请多名舰员</li>
                            <li>受邀舰员将自动加入您的舰队</li>
                            <li>您将获得受邀舰员产生收益的20%提成</li>
                            <li>请勿将邀请码分享给不信任的用户</li>
                        </Typography>
                    </Box>
                </DialogContent>
                <DialogActions sx={{p: isMobile ? 1.5 : 2, justifyContent: 'space-between'}}>
                    <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => handleGenerateNewCode()} // Ensure this calls the correct function
                        startIcon={<RefreshCw size={isMobile ? 16 : 18}/>}
                        disabled={invitationCodeLoading} // Disable button when loading
                    >
                        {invitationCodeLoading ? '生成中...' : '重新生成'}
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleCopyInvitationCode}
                        disabled={invitationCodeCopied || !invitationCode || invitationCodeLoading} // Disable button when loading or no code
                    >
                        {invitationCodeCopied ? '已复制' : '复制邀请码'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
} 