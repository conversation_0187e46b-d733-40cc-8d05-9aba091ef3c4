"use client";

import {useCallback, useEffect, useState} from 'react';
import {Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Grid, IconButton, Pagination, Typography} from '@mui/material';
import {Plus, RefreshCw, Search, Trash2} from 'lucide-react';
import ProductCard from '@/components/ProductCard';
import {productApi} from '@/api/product-api';


export default function ProductManagement() {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [totalProducts, setTotalProducts] = useState(0);

    const [search, setSearch] = useState('');
    const [apiSearchTerm, setApiSearchTerm] = useState('');

    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);

    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);

    const handleChangePage = (event, newPage) => {
        setPage(newPage - 1);
    };

    const handleSearchChange = (event) => {
        setSearch(event.target.value);
    };

    const handleSearchKeyDown = (event) => {
        if (event.key === 'Enter') {
            handleFilter();
        }
    };

    const handleViewProduct = (product) => {
        window.location.href = `/protected/product-management/${product.id}`;
    };

    const handleDeleteClick = (event, product) => {
        event.stopPropagation();
        setProductToDelete(product);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!productToDelete) return;
        try {
            await productApi.delete(productToDelete.id);
            setDeleteDialogOpen(false);
            setProductToDelete(null);
            fetchProducts();
        } catch (err) {
            console.error("Failed to delete product:", err);
            alert(`删除失败: ${err.message || '未知错误'}`);
        }
    };

    const handleDeleteCancel = () => {
        setDeleteDialogOpen(false);
        setProductToDelete(null);
    };

    const handleAddProduct = () => {
        window.location.href = `/protected/product-management/new`;
    };

    const fetchProducts = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await productApi.query_all(apiSearchTerm, page, rowsPerPage);
            const formattedProducts = (response.products || []).map(p => {
                const imageObjects = (p.images || []).map((img, index) => ({
                    id: img.oss_key || index,
                    name: `产品图片 ${p.id_}-${index + 1}`,
                    size: 'N/A',
                    url: img.url || 'https://placehold.co/300x400/eeeeee/cccccc?text=No+Image',
                    oss_key: img.oss_key,
                }));

                return {
                    id: p.id_,
                    title: p.title || `产品 ${p.id_}`,
                    images: imageObjects,
                    domain: p.domain?.join(',') || '无',
                    description: p.description || '暂无描述',
                    date: p.create_at ? new Date(p.create_at * 1000).toLocaleDateString() : '未知日期',
                };
            });
            setProducts(formattedProducts);
            setTotalProducts(response.total_count);
        } catch (err) {
            console.error("Failed to fetch products:", err);
            setError(err.message || '获取产品数据失败');
            setProducts([]);
            setTotalProducts(0);
        } finally {
            setLoading(false);
        }
    }, [apiSearchTerm, page, rowsPerPage]);

    useEffect(() => {
        fetchProducts();
    }, [fetchProducts]);

    const pageCount = Math.ceil(totalProducts / rowsPerPage);
    const paginatedProducts = products;

    const handleFilter = () => {
        setPage(0);
        setApiSearchTerm(search);
    };

    return (
        <Box sx={{py: 3}}>
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                <Typography variant="h4" component="h1" sx={{fontWeight: 600}}>
                    产品管理
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<Plus size={18}/>}
                    onClick={handleAddProduct}
                >
                    添加产品
                </Button>
            </Box>

            <Box sx={{mb: 4}}>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                    <Box sx={{display: 'flex', gap: 1, alignItems: 'center'}}>
                        <Box
                            placeholder="搜索产品标题或描述"
                            component="input"
                            sx={{
                                border: `1px solid #e0e0e0`,
                                borderRadius: 2,
                                px: 2,
                                py: 1,
                                width: 240,
                                display: 'flex',
                                alignItems: 'center'
                            }}
                            value={search}
                            onChange={handleSearchChange}
                            onKeyDown={handleSearchKeyDown}
                        />
                        <IconButton
                            onClick={handleFilter}
                            size="small"
                            sx={{
                                border: `1px solid #e0e0e0`,
                                borderRadius: 2,
                            }}
                        >
                            <Search size={18}/>
                        </IconButton>
                        <IconButton
                            onClick={fetchProducts}
                            size="small"
                            sx={{
                                border: `1px solid #e0e0e0`,
                                borderRadius: 2,
                            }}
                        >
                            <RefreshCw size={18}/>
                        </IconButton>
                    </Box>
                </Box>
                <Divider/>
            </Box>

            {loading && <Typography sx={{textAlign: 'center', my: 4}}>加载中...</Typography>}
            {error && <Typography color="error" sx={{textAlign: 'center', my: 4}}>错误: {error}</Typography>}

            {!loading && !error && products.length === 0 && (
                <Typography sx={{textAlign: 'center', my: 4}}>没有找到产品。</Typography>
            )}
            {!loading && !error && products.length > 0 && (
                <Grid container spacing={3} sx={{mb: 4}}>
                    {paginatedProducts.map((product) => (
                        <Grid item xs={12} sm={6} md={4} lg={2.4} key={product.id}>
                            <ProductCard
                                images={product.images.map(img => img.url)}
                                onClick={() => handleViewProduct(product)}
                            >
                                <Box sx={{p: 2}}>
                                    <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                                        <Typography variant="subtitle1" sx={{fontWeight: 600, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flexGrow: 1, mr: 1}}>
                                            {product.title}
                                        </Typography>
                                        <IconButton
                                            size="small"
                                            onClick={(event) => handleDeleteClick(event, product)}
                                            sx={{color: 'error.main'}}
                                            aria-label="delete product"
                                        >
                                            <Trash2 size={16}/>
                                        </IconButton>
                                    </Box>

                                    {product.domain && (
                                        <Typography variant="body2" color="text.secondary" sx={{mb: 0.5}}>
                                            <strong>领域:</strong> {product.domain}
                                        </Typography>
                                    )}

                                    {product.description && (
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                height: 40,
                                                lineHeight: '20px'
                                            }}
                                        >
                                            {product.description}
                                        </Typography>
                                    )}

                                    <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1}}>
                                        <Typography variant="caption" color="text.secondary">
                                            {product.date}
                                        </Typography>
                                    </Box>
                                </Box>
                            </ProductCard>
                        </Grid>
                    ))}
                </Grid>
            )}

            {!loading && !error && totalProducts > 0 && (
                <Box sx={{display: 'flex', justifyContent: 'center', mt: 3}}>
                    <Pagination
                        count={pageCount}
                        page={page + 1}
                        onChange={handleChangePage}
                        color="primary"
                    />
                </Box>
            )}

            <Dialog
                open={deleteDialogOpen}
                onClose={handleDeleteCancel}
                aria-labelledby="alert-dialog-title"
            >
                <DialogTitle id="alert-dialog-title">
                    确定要删除此产品吗？
                </DialogTitle>
                <DialogContent>
                    <Typography variant="body2">
                        删除后将无法恢复，请确认您的操作。
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleDeleteCancel} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleDeleteConfirm} color="error" autoFocus>
                        删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}
