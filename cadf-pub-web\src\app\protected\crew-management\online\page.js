"use client";

import {useState, useEffect, useCallback} from 'react';
import {Box, Button, Card, CardContent, Chip, IconButton, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, useMediaQuery, useTheme, CircularProgress, Alert} from '@mui/material';
import {ArrowLeft, Search, UserCheck} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {crewManagementApi} from '@/api/crew-management-api';

export default function OnlineCrewMembers() {
    const router = useRouter();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    
    const [crewMembers, setCrewMembers] = useState([]);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const rowsPerPage = 10;

    const fetchCrewMembers = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await crewManagementApi.getCrewList('在线', page, rowsPerPage);
            if (response && response.crew) {
                setCrewMembers(response.crew);
                setTotalCount(response.total_count || 0);
            } else {
                setCrewMembers([]);
                setTotalCount(0);
            }
        } catch (err) {
            console.error("获取在线舰员失败:", err);
            setError(err.message || '加载数据时发生错误');
            setCrewMembers([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    }, [page, rowsPerPage]);

    useEffect(() => {
        fetchCrewMembers();
    }, [fetchCrewMembers]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const getAccountStatusText = (status) => {
        return status === '在线' ? '在线' : (status === 'offline' ? '离线' : '封禁');
    };

    const getAccountStatusColor = (status) => {
        switch (status) {
            case '在线':
                return 'success';
            case '离线':
                return 'default';
            default:
                return 'error';
        }
    };

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton 
                    onClick={() => router.back()}
                    sx={{mr: 1}}
                    color="primary"
                >
                    <ArrowLeft />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{
                    fontWeight: 500, 
                    fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}
                }}>
                    在线账户 ({loading ? '...' : totalCount})
                </Typography>
            </Box>

            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        查看所有当前在线的舰员账户列表，了解他们的活跃状态和收益情况。
                    </Typography>

                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        mb: 3,
                        gap: 1
                    }}>
                    </Box>

                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                            <CircularProgress />
                        </Box>
                    ) : error ? (
                        <Alert severity="error">{error}</Alert>
                    ) : (
                        <>
                            {isMobile ? (
                                <Box>
                                    {crewMembers.length > 0 ? (
                                        crewMembers.map((member) => (
                                            <Card 
                                                key={member.id_}
                                                sx={{
                                                    mb: 2,
                                                    borderRadius: 2,
                                                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                                                    transition: 'transform 0.2s ease, box-shadow 0.3s ease',
                                                    '&:hover': {
                                                        transform: 'translateY(-2px)',
                                                        boxShadow: '0 6px 16px rgba(0,0,0,0.08)'
                                                    },
                                                    overflow: 'hidden',
                                                    border: '1px solid rgba(0,0,0,0.08)'
                                                }}
                                            >
                                                <Box sx={{
                                                    p: 0.5,
                                                    background: theme.palette.success.main,
                                                    height: 4
                                                }} />
                                                <CardContent sx={{p: 2}}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        mb: 1.5
                                                    }}>
                                                        <Typography 
                                                            variant="subtitle1" 
                                                            sx={{
                                                                fontWeight: 600,
                                                                fontSize: '1.1rem',
                                                                color: theme.palette.text.primary
                                                            }}
                                                        >
                                                            {member.user_account}
                                                        </Typography>
                                                        <Chip
                                                            label={getAccountStatusText(member.login_status)}
                                                            color={getAccountStatusColor(member.login_status)}
                                                            size="small"
                                                            sx={{
                                                                borderRadius: '4px',
                                                                fontWeight: 500,
                                                                fontSize: '0.7rem'
                                                            }}
                                                        />
                                                    </Box>
                                                    
                                                    <Box sx={{
                                                        display: 'flex', 
                                                        flexDirection: 'column', 
                                                        gap: 1.2,
                                                        background: 'rgba(0,0,0,0.02)',
                                                        p: 1.5,
                                                        borderRadius: 1.5,
                                                        mb: 1
                                                    }}>
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center'
                                                        }}>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.secondary,
                                                                fontWeight: 500,
                                                                minWidth: '80px'
                                                            }}>
                                                                平台账号:
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.primary,
                                                                ml: 1
                                                            }}>
                                                                {member.platform_account || '未设置'}
                                                            </Typography>
                                                        </Box>
                                                        
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center'
                                                        }}>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.secondary,
                                                                fontWeight: 500,
                                                                minWidth: '80px'
                                                            }}>
                                                                最后登录:
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.primary,
                                                                ml: 1
                                                            }}>
                                                                {member.last_login || '未知'}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    
                                                    <Box sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        background: 'rgba(46, 174, 52, 0.08)',
                                                        borderRadius: 1,
                                                        py: 0.6,
                                                        px: 1
                                                    }}>
                                                        <UserCheck size={14} color={theme.palette.success.main} style={{marginRight: '6px'}} />
                                                        <Typography variant="caption" sx={{color: theme.palette.success.main, fontWeight: 500}}>
                                                            当前在线
                                                        </Typography>
                                                    </Box>
                                                </CardContent>
                                            </Card>
                                        ))
                                    ) : (
                                        <Card variant="outlined" sx={{
                                            p: 3, 
                                            textAlign: 'center',
                                            borderRadius: 2,
                                            borderStyle: 'dashed',
                                            background: 'rgba(0,0,0,0.02)'
                                        }}>
                                            <Typography>没有找到匹配的在线账户</Typography>
                                        </Card>
                                    )}
                                </Box>
                            ) : (
                                <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>用户账号</TableCell>
                                                <TableCell>平台账号</TableCell>
                                                <TableCell>账号状态</TableCell>
                                                <TableCell>最后登录时间</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {crewMembers.length > 0 ? (
                                                crewMembers.map((member) => (
                                                    <TableRow key={member.id_} hover>
                                                        <TableCell>{member.user_account}</TableCell>
                                                        <TableCell>{member.platform_account || '未设置'}</TableCell>
                                                        <TableCell>
                                                            <Chip
                                                                label={getAccountStatusText(member.login_status)}
                                                                color={getAccountStatusColor(member.login_status)}
                                                                size="small"
                                                            />
                                                        </TableCell>
                                                        <TableCell>{member.last_login || '未知'}</TableCell>
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <TableRow>
                                                    <TableCell colSpan={4} align="center">
                                                        没有找到匹配的在线账户
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            )}

                            {totalCount > rowsPerPage && (
                                <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                                    <Pagination
                                        count={Math.max(1, Math.ceil(totalCount / rowsPerPage))}
                                        page={page}
                                        onChange={handlePageChange}
                                        color="primary"
                                        size={isMobile ? "small" : "medium"}
                                        disabled={loading}
                                    />
                                </Stack>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
} 