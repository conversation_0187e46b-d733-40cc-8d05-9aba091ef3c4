from config.config import XHS_IMAGE_GEN_SET, OSS_PIC_DIR
from omni.msg_queue.redis_set_consumer import consume_redis_set
from agent.workflow_agents.cosmetic_image_gen_agent import cosmetic_image_gen_agent
from omni.integration.oss.tencent_oss import OSSClient
from omni.log.log import olog
from repository.models import AiGeneratedMaterial, Product, AiBasicMaterial, ImageInfo
from concurrent.futures import ThreadPoolExecutor

@consume_redis_set(redis_key=XHS_IMAGE_GEN_SET, num_threads=1)
def handle_task(message_data):
    ai_generated_material_id = message_data
    oss_client = OSSClient.getInstance()
    material_doc = AiGeneratedMaterial.objects(id=ai_generated_material_id).first()
    material_doc.image_generation_status = "生成中"
    material_doc.save()

    product_doc = Product.objects(id=material_doc.product_id, is_deleted=False).first()
    product_image_key = next((img.oss_key for img in product_doc.images), None)
    product_image_bytes = oss_client.download_bytes(product_image_key)

    basic_material_doc = AiBasicMaterial.objects(id=material_doc.material_id, is_deleted=False).first()
    basic_material_image_bytes_list = [oss_client.download_bytes(img.oss_key) for img in basic_material_doc.images]

    def process_image(scene_image_bytes):
        try:
            final_image_bytes = cosmetic_image_gen_agent(
                scene_image_bytes=scene_image_bytes,
                product_image_bytes=product_image_bytes,
                product_intro=product_doc.description
            )
            if final_image_bytes:
                oss_key = oss_client.upload_bytes(OSS_PIC_DIR, ".png", final_image_bytes)
                if oss_key:
                    olog.info(f"图片上传成功，key: {oss_key}")
                    return oss_key
                olog.error("图片上传失败")
            olog.warning("未生成图片")
            return None
        except Exception as e:
            olog.exception(f"处理图片时发生异常: {e}")
            return None

    with ThreadPoolExecutor(max_workers=len(basic_material_image_bytes_list)) as executor:
        uploaded_keys = list(executor.map(process_image, basic_material_image_bytes_list))

    successful_uploads = [key for key in uploaded_keys if key is not None]
    if successful_uploads:
        material_doc.images = [ImageInfo(oss_key=key, order=i) for i, key in enumerate(successful_uploads)]
        material_doc.image_generation_status = "已完成"
        material_doc.image_generation_fail_reason = "部分图片生成失败" if len(successful_uploads) < len(basic_material_image_bytes_list) else None
    else:
        material_doc.image_generation_status = "失败"
        material_doc.image_generation_fail_reason = "所有图片生成失败"
    olog.info(f"图片生成完成，成功上传数量: {len(successful_uploads)}")
    material_doc.save()




    