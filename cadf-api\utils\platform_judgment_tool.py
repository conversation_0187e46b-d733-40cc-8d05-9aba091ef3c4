import re
from typing import Literal


def extract_url_from_text(text: str, platform_name: Literal["小红书"]) -> str | None:
    """
    根据平台名称从分享文本中提取对应的 URL。

    Args:
        text: 分享文本内容。
        platform_name: 平台名称，目前支持 "小红书"。

    Returns:
        提取到的 URL 字符串，如果无法匹配则返回 None。
    """
    if not isinstance(text, str):
        return None  # 输入必须是字符串

    url_match = None
    if platform_name == "小红书":
        url_match = re.search(r"(https?://(?:www\.xiaohongshu\.com|xhslink\.com)/[^\s，]+)", text)
    return url_match.group(0) if url_match else None
