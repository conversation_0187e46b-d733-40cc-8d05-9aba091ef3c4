"""
redis:
  url: redis://default:<EMAIL>:6379
"""

import redis

from omni.config.config_loader import config_dict


class RedisClient:
    def __init__(self):
        self.redis_client = redis.from_url(
            config_dict["redis"]["url"], health_check_interval=30, socket_keepalive=True
        )

    def get_redis_client(self):
        return self.redis_client


rc = RedisClient().get_redis_client()
