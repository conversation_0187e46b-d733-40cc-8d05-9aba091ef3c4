import base64
import io

from PIL import Image
from langchain_core.messages import HumanMessage
from pydantic import BaseModel

from omni.llm.agent.structured_output_agent import structured_output_agent
from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog
from agent.utils.openai_image_edit import OpenaiImageEditor
from agent.utils.seedream_image_gen import SeedreamImageGenerator

"""
克隆合规的角色智能体
"""


def clone_base_image(image_bytes: bytes) -> tuple[bytes, str] | None:
    """
    根据给定的图片 bytes 编辑图片，移除所有非自然元素（如文字、表情、贴纸等），确保场景干净。
    :param image_bytes: 图片的二进制数据
    :return: 编辑后的图片二进制数据，若发生错误则返回 None
    """
    prompt_clean_scene = """
    移除图片中所有艺术字体，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素，确保场景干净。
    """
    olog.info("开始执行图片清理操作...")
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    edited_image_data, error_type = editor.generate_image_from_image(
        image_bytes_list=[image_bytes],
        prompt=prompt_clean_scene,
    )
    olog.info(f"图片清理操作完成，结果类型: {error_type}")
    return edited_image_data, error_type


def identify_elements_from_image(image_bytes: bytes) -> str:
    olog.info("开始执行 identify_elements_from_image (Bytes模式)...")
    llm = ChatModelFactory.get_llm("QWEN_VL_MAX")
    img = Image.open(io.BytesIO(image_bytes))
    original_format = img.format
    olog.info(f"处理图片，原始格式为: {original_format}")

    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=original_format)
    base64_str = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")
    mime_type = original_format.lower() if original_format else "png"

    llm_image_input = {
        "type": "image_url",
        "image_url": {"url": f"data:image/{mime_type};base64,{base64_str}"},
    }

    prompt = """
# 角色
你是一名专业的图片识别和分析专家，精通识别图片中的所有元素和细节。

# 任务
详细分析并识别图片中的所有关键元素、特征和细节，尽可能全面和准确。
忽略图片上文字，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素。

# 返回值
```
# 物体识别
- 主要物体（列出画面中的主要物体）
- 物体颜色和材质
- 物体位置和排列
- 特殊物品或标志性元素

# 场景特征
- 场景类型（如室内、室外、自然风光、城市街道等）
- 环境描述（如海滩、山脉、森林、办公室、客厅等）
- 时间（如白天、黄昏、夜晚等）
- 天气状况（如晴朗、多云、雨天等）
- 光线条件（如明亮、昏暗、逆光、侧光等）

# 美学和视觉元素
- 主色调（画面的主要颜色）
- 构图特点（如对称、三分法、主体突出等）
- 视角（如平视、俯视、仰视等）
- 质感和纹理（如光滑、粗糙、金属质感等）
- 景深和焦点

# 其他重要细节
- 特殊效果（如景深模糊、运动模糊等）
- 独特或不寻常的元素
- 图片整体氛围和风格（如正式、休闲、艺术、写实等）
```
"""

    human_message_content = [
        {"type": "text", "text": prompt},
        llm_image_input,
    ]

    raw_result = llm.invoke([HumanMessage(content=human_message_content)]).content
    return raw_result


class DetailedPromptModel(BaseModel):
    chinese_prompt: str


def generate_portrait_prompt(prompt_text: str) -> str | None:
    olog.info("开始执行 generate_portrait_prompt 函数，生成详细人像提示词...")
    olog.info(f"输入的初步提示词: {prompt_text}")
    detailed_prompt = f"""
# 角色
你是一名专精于AI绘画提示词工程的专家。

# 背景

图片描述:
```
{prompt_text}
```

# 任务
根据图片描述，生成更详细、更丰富、更有创意的图片描述和提示词，适合直接作为AI绘画模型的输入。

生成的提示词中必须融入以下提示词:
```
用 iPhone 后置镜头拍照
日常快照风格，非精心构图或打光，构图随意、角度尴尬、画面不够对称或美观
画质带有日常感和粗糙感，整体呈现出一种刻意的平庸感-就像是从口袋里拿手机时不小心拍到的。
```

# 返回值

```
# 中文提示词
[在这里提供详细的中文提示词]
```
"""
    llm = ChatModelFactory.get_llm("QWEN_MAX")
    raw_result = llm.invoke(
        [HumanMessage(content=[{"type": "text", "text": detailed_prompt}])])
    olog.info(f"详细提示词生成成功，最终中文提示词: {raw_result.content}")
    # 使用结构化输出代理进行结构化
    result = structured_output_agent(DetailedPromptModel, raw_result.content).chinese_prompt
    return result


def generate_image_from_prompt(prompt: str) -> bytes | None:
    olog.info("开始执行 generate_image_from_prompt 函数，生成图片...")
    generator = SeedreamImageGenerator(llm_config_key="SEEDREAM")
    image_bytes = generator.gen_image(prompt)
    return image_bytes


def clone_compliant_object(image_bytes: bytes) -> bytes | None:
    """
    克隆合规的角色图片。
    1. 先尝试清理图片中的非自然元素。
    2. 如果清理成功，返回处理后的图片。
    3. 如果清理因 'security_blocked' 失败，则识别图片元素并生成新的合规图片。
    4. 其他错误返回 None。
    :param image_bytes: 图片的二进制数据
    :return: 合规的图片二进制数据或 None
    """
    raw_clone_result = clone_base_image(image_bytes)
    if not isinstance(raw_clone_result, tuple) or len(raw_clone_result) != 2:
        return None

    edited_image_data, error_type = raw_clone_result
    if error_type == "success":
        olog.info("图片克隆成功。")
        return edited_image_data
    elif error_type == "security_blocked":
        olog.warning(
            "图片克隆因 '安全机制' 失败，将尝试识别元素并生成新图片。"
        )
        elements = identify_elements_from_image(image_bytes)
        generated_prompt_str = generate_portrait_prompt(elements)
        new_image = generate_image_from_prompt(generated_prompt_str)
        return new_image
    else:
        return None
