---
description: 
globs: **/*.py
alwaysApply: false
---
# Python 开发规范

## 基本规范

- 注释需要使用中文
- 简化代码,不需要任何,非空值判断

## 异常处理

- 由于最外层有统一异常处理,所以所有方法不需要异常处理
- 异常日志使用 `olog.exception()`

## 日志系统

- **基本要求**: 日志信息必须使用中文。
- **使用方法**:

  ```python
  from omni.log.log import olog

  # 调试信息
  olog.debug("这是一条调试信息")
  # 一般信息
  olog.info("这是一条普通信息")
  # 警告信息
  olog.warning("这是一条警告信息")
  # 错误信息
  olog.error("这是一条错误信息")
  # 严重错误信息
  olog.critical("这是一条严重错误信息")
  ```
