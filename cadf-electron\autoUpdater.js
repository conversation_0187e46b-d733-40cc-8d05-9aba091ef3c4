const {BrowserWindow, ipcMain} = require('electron');
const {autoUpdater} = require("electron-updater");
const path = require('path');

let progressWindow = null;

function createProgressWindow() {
    progressWindow = new BrowserWindow({
        width: 300,
        height: 150,
        frame: false,
        resizable: false,
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });

    progressWindow.loadFile(path.join(__dirname, 'progress.html'));

    progressWindow.once('ready-to-show', () => {
        progressWindow.show();
    });
}

function setupAutoUpdater() {
    autoUpdater.autoDownload = true;
    autoUpdater.autoInstallOnAppQuit = true;

    autoUpdater.on('update-available', (info) => {
        createProgressWindow();
    });

    autoUpdater.on('download-progress', (progressObj) => {
        if (progressWindow) {
            progressWindow.webContents.send('download-progress', progressObj.percent);
        }
    });

    autoUpdater.on('update-downloaded', () => {
        if (progressWindow) {
            progressWindow.close();
            progressWindow = null;
        }
        autoUpdater.quitAndInstall();
    });

    autoUpdater.on('error', (err) => {
        console.error('更新错误: ' + err);
        if (progressWindow) {
            progressWindow.close();
            progressWindow = null;
        }
    });

    autoUpdater.checkForUpdates();
}

module.exports = {
    setupAutoUpdater
};
