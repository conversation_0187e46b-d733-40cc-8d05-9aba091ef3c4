"""
Redis Set 消费者模块

提供一个装饰器 `@consume_redis_set` 用于将函数注册为 Redis Set 的消费者，
并提供自动发现和统一启动消费者的功能。

使用方法:

1.  **定义消费者函数**:
    在你的项目中，创建一个函数来处理从 Redis Set 中获取的消息。
    使用 `@consume_redis_set` 装饰这个函数，并指定 Redis Key 和需要的线程数。
    将这些消费者函数放在约定的包目录下 (例如 `consumers`)。

    ```python
    # 在例如 consumers/task_handler.py 文件中
    from omni.msg_queue.redis_set_consumer import consume_redis_set
    from omni.log.log import olog

    @consume_redis_set(redis_key="your_task_set_key", num_threads=2)
    def handle_task(message_data):
        olog.info(f"处理任务: {message_data}")
        # ... 执行处理逻辑 ...
    ```

2.  **发现并启动消费者**:
    在你的应用程序主启动脚本中 (例如 main.py)，导入 `start_all_consumers`。
    调用 `start_all_consumers()`。
    -   它会自动发现并启动在默认包 `consumers` 中定义的消费者。
    -   你可以传入一个包含包路径字符串的列表给 `package_paths` 参数来指定搜索位置，例如: 
        `start_all_consumers(package_paths=['my_app.handlers', 'shared.listeners'])`
    确保这些包可以被 Python 正确导入。

    ```python
    # 在 main.py 或应用入口文件
    from omni.msg_queue.redis_set_consumer import start_all_consumers
    from omni.log.log import olog # 确保日志已配置

    olog.info("正在发现并启动消费者...")
    # 发现并启动默认包 'consumers' 中的消费者
    start_all_consumers()
    # 或者指定其他包:
    # start_all_consumers(package_paths=['my_app.handlers', 'shared.listeners'])
    olog.info("消费者发现和启动流程已调用。")

    # 保持主程序运行...
    # try:
    #     while True:
    #         time.sleep(60)
    # except KeyboardInterrupt:
    #     olog.info("应用程序关闭。")
    ```

"""
import importlib
import os
import pkgutil
import threading
import time

# 使用自定义日志记录器
from omni.log.log import olog
from omni.redis.redis_client import rc  # 导入 Redis 客户端实例

# 模块级别常量
DEFAULT_CONSUMER_PACKAGES = ['consumers']
EMPTY_SET_SLEEP_SECONDS = 0.1
SPOP_ERROR_SLEEP_SECONDS = 1.0

# 全局消费者注册列表，存储 (函数, redis_key, num_threads)
_registered_consumers = []


def _try_import_module(module_name: str):
    """
    尝试导入指定模块，并记录任何导入或加载错误。
    """
    try:
        importlib.import_module(module_name)
    except ImportError as e:
        olog.warning(f"导入模块 {module_name} 时出错: {e}", exc_info=False)
    except Exception as e:
        olog.error(f"加载模块 {module_name} 时发生意外错误: {e}", exc_info=True)


def _run_consumer_thread(redis_key: str, processing_func):
    """单个消费者线程的运行逻辑"""
    thread_name = threading.current_thread().name
    while True:  # 可根据需要添加更优雅的停止机制
        raw_message = None  # 用于帮助判断错误来源
        try:
            # SPOP 原子地移除并返回集合中的一个随机元素
            raw_message = rc.spop(redis_key)
            if raw_message:
                # 尝试解码消息
                decoded_message = raw_message.decode('utf-8')
                # 调用注册的函数处理消息
                processing_func(decoded_message)
            else:
                # 集合为空时短暂休眠，避免CPU空转
                time.sleep(EMPTY_SET_SLEEP_SECONDS)
        except UnicodeDecodeError as ude:
            # spop 成功，但解码失败
            olog.error(f"线程 {thread_name} 解码来自 Redis Set '{redis_key}' 的消息时出错: {ude}. 原始消息 (部分): {raw_message[:100] if raw_message else 'N/A'}", exc_info=True)
            # 消息已被 SPOP，记录错误并继续处理下一条
        except Exception as e:
            if raw_message is not None:
                # 如果 raw_message 不是 None，说明 spop 成功，错误发生在解码 (非UnicodeDecodeError) 或 processing_func 中
                olog.error(f"线程 {thread_name} 处理或解码来自 Redis Set '{redis_key}' 的消息时出错 ({processing_func.__name__}): {e}. 原始消息 (部分): {raw_message[:100] if raw_message else '无'}", exc_info=True)
            else:
                # raw_message 是 None，说明错误发生在 rc.spop()
                olog.error(f"线程 {thread_name} 从 Redis Set '{redis_key}' 消费时出错: {e}", exc_info=True)
                # 发生连接错误等可适当重试
                time.sleep(SPOP_ERROR_SLEEP_SECONDS)


def consume_redis_set(redis_key: str, num_threads: int = 1):
    """
    装饰器：将函数注册为 Redis Set (redis_key) 的消费者，指定处理线程数 (num_threads)。
    注意：此装饰器仅注册，不启动消费者线程。启动由 start_all_consumers 完成。
    """
    if num_threads <= 0:
        raise ValueError("num_threads 必须是正整数")

    def decorator(func):
        # 将函数及其配置注册到全局列表
        _registered_consumers.append((func, redis_key, num_threads))
        # 返回原始函数，使其仍可被其他方式调用
        return func

    return decorator


def _discover_consumers(package_paths: list[str]):
    """
    内部函数：动态发现并导入指定 Python 包路径下的所有模块，以触发消费者注册。

    Args:
        package_paths: 一个包含包导入路径字符串的列表。
    """
    # olog.info(f"开始在包 {package_paths} 中发现消费者模块...") # 根据之前的分析，保持此详细日志为注释状态
    for package_path_str in package_paths:
        try:
            package = importlib.import_module(package_path_str)
            package_root_paths = []
            if hasattr(package, '__path__'):
                package_root_paths = package.__path__
            elif hasattr(package, '__file__') and package.__file__:
                package_root_paths = [os.path.dirname(package.__file__)]
            else:
                olog.warning(f"无法确定包 {package_path_str} 的物理路径，跳过扫描其子模块。")
                continue

            for finder, name, is_pkg in pkgutil.walk_packages(package_root_paths, prefix=package_path_str + '.'):
                if not is_pkg:  # 只导入模块
                    _try_import_module(name) # 使用辅助函数导入

        except ImportError as e:
            olog.error(f"无法导入或找到包路径 {package_path_str}: {e}", exc_info=False)
        except Exception as e:
            olog.error(f"在发现包 {package_path_str} 的模块时发生意外错误: {e}", exc_info=True)
    # olog.info("消费者模块发现完成。")


def start_all_consumers():
    """
    发现并启动所有已注册的消费者线程。

    首先调用内部发现机制来加载默认包 (DEFAULT_CONSUMER_PACKAGES) 中的消费者定义（触发注册），
    然后启动所有通过 @consume_redis_set 装饰器注册的消费者。
    """
    olog.info("开始执行消费者发现与启动流程...")

    # 发现并注册消费者 (通过导入模块触发装饰器)
    _discover_consumers(DEFAULT_CONSUMER_PACKAGES)

    # 启动已注册的消费者
    if not _registered_consumers:
        olog.warning("没有找到已注册的 Redis Set 消费者。")
        return

    olog.info(f"共发现 {len(_registered_consumers)} 个消费者配置，正在尝试启动...")
    all_threads = []
    successful_launches = 0
    failed_launches = 0

    for consumer_func, redis_key, num_threads in _registered_consumers:
        for i in range(num_threads):
            try:
                thread = threading.Thread(
                    target=_run_consumer_thread,
                    args=(redis_key, consumer_func),
                    daemon=True,
                    name=f"RedisSetConsumer-{redis_key}-{i + 1}"
                )
                thread.start()
                all_threads.append(thread)
                successful_launches +=1
                olog.info(f"成功启动消费者线程: Key='{redis_key}', Handler='{consumer_func.__name__}', ThreadNum={i + 1}/{num_threads}")
            except Exception as e:
                failed_launches += 1
                olog.error(f"启动 Redis Key '{redis_key}' 的第 {i + 1}/{num_threads} 个消费者线程失败: {e}", exc_info=True)

    if successful_launches > 0:
        # olog.info(f"成功启动 {successful_launches} 个消费者线程。") # 此信息已在每个线程启动时单独记录
        pass # 保留此块结构以防未来需要添加聚合日志
    if failed_launches > 0:
        olog.warning(f"{failed_launches} 个消费者线程启动失败。")
    
    if not all_threads and not successful_launches: # 修正条件，确保仅在确实没有任何线程成功启动时记录
        olog.warning("未能成功启动任何消费者线程。")
    
    olog.info("消费者启动流程结束。")
