from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.log.log import olog
from omni.mongo.mongo_client import docs_to_dict, doc_to_dict
from repository.models import AiGeneratedMaterial


@register_handler('ai_generated_material')
class AiGeneratedMaterialApi:

    @auth_required(['admin', 'advertiser'])
    def delete(self, data):
        id_ = data.get('id_')
        user_id = data.get('user_id')
        if not id_:
            raise MException("删除操作需要提供 'id_'")

        # 使用 update_one 设置 is_deleted 为 True
        result = AiGeneratedMaterial.objects(id=id_, user_id=user_id, is_deleted=False).update_one(set__is_deleted=True)

        if result == 0:
            # 检查是否因为文档不存在或已被删除
            existing_material = AiGeneratedMaterial.objects(id=id_, user_id=user_id).only('is_deleted').first()
            if not existing_material:
                raise MException("未找到指定的 AI 生成素材或无权删除")
            elif existing_material.is_deleted:
                raise MException("该素材已被删除")
            else:
                # 其他可能的原因，虽然理论上不应该发生
                raise MException("更新删除标记失败，未知错误")

        return {'message': '删除成功'}

    @auth_required(['admin', 'advertiser'])
    def query_by_task_id(self, data):
        task_id = data.get('task_id')
        user_id = data.get('user_id')
        page = data.get('page', 1)  # 默认为第1页
        limit = data.get('limit', 10)  # 默认每页10条

        if not task_id:
            raise MException("按 task_id 查询需要提供 'task_id'")

        query_filter = {
            'task_id': task_id,
            'user_id': user_id,
            'image_generation_status': '已完成',
            'text_generation_status': '已完成',
            'is_deleted': False
        }

        # 计算跳过的文档数
        skip = (page - 1) * limit

        # 查询分页数据
        materials = AiGeneratedMaterial.objects(**query_filter).skip(skip).limit(limit)
        # 获取总数
        total_count = AiGeneratedMaterial.objects(**query_filter).count()

        # 转换并添加签名 URL
        materials_list = docs_to_dict(materials)
        oss_client = OSSClient.getInstance()

        for material_dict in materials_list:
            if 'images' in material_dict and isinstance(material_dict['images'], list):
                # 按 order 排序
                material_dict['images'].sort(key=lambda img: img.get('order', 0))
                for image_info in material_dict['images']:
                    if 'oss_key' in image_info and image_info['oss_key']:
                        try:
                            # 生成签名 URL，有效时间默认1小时
                            image_info['signed_url'] = oss_client.signed_url(SignedMethod.GET, image_info['oss_key'])
                        except Exception as e:
                            olog.error(f"为 key {image_info['oss_key']} 生成签名 URL 失败: {e}")
                            image_info['signed_url'] = None  # 或者一个表示错误的占位符 URL
                    else:
                        image_info['signed_url'] = None  # oss_key 不存在或为空

        return {
            'results': materials_list,  # 返回包含签名 URL 的列表
            'total_count': total_count,
            'page': page,
            'limit': limit
        }
        
    @auth_required(['admin', 'advertiser'])
    def query_by_id(self, data):
        id_ = data.get('id_')
        user_id = data.get('user_id')
        
        if not id_:
            raise MException("查询单个内容需要提供 'id_'")
            
        # 查询单个素材
        material = AiGeneratedMaterial.objects(id=id_, user_id=user_id, is_deleted=False).first()
        
        if not material:
            raise MException("未找到指定的 AI 生成素材或无权访问")
            
        # 转换为字典并添加签名 URL
        material_dict = doc_to_dict(material)
        oss_client = OSSClient.getInstance()
        
        if 'images' in material_dict and isinstance(material_dict['images'], list):
            # 按 order 排序
            material_dict['images'].sort(key=lambda img: img.get('order', 0))
            for image_info in material_dict['images']:
                if 'oss_key' in image_info and image_info['oss_key']:
                    try:
                        # 生成签名 URL，有效时间默认1小时
                        image_info['signed_url'] = oss_client.signed_url(SignedMethod.GET, image_info['oss_key'])
                    except Exception as e:
                        olog.error(f"为 key {image_info['oss_key']} 生成签名 URL 失败: {e}")
                        image_info['signed_url'] = None
                else:
                    image_info['signed_url'] = None
                    
        return material_dict
