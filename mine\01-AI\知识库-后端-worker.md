# worker 编写文档

```python
import traceback

from langgraph.graph import StateGraph, END

from config.config import Topic
from core.llm.langchain import LLMOutputFormat, LLMCompletionBuilderExecutor, OpenAiCompletionBuilder
from core.log.log import syslog
from core.mq.kafka_mq import MessageReceiver, MessageReceiverStatus
from core.tools.common_tool import read_file_content
from core.worker.worker_register import register_worker
from repository.models import ArticleGen, ArticleExample
from worker.tool.gen_log_tool import async_gen_log, add_gen_log
from worker.tool.worker_state import ArticleState


@register_worker(5)  # 使用装饰器注册工作器，参数5表示能有多少个并行的worker
class ArticleGenWorker(MessageReceiver):

    def __init__(self):
        super().__init__(Topic.GEN_ARTICLE)  # 调用父类的初始化函数，并订阅kafka主题

    # 读取原始数据,并把需要的数据写进State
    def before(self, state: ArticleState):
        # 获取state中所有的数据
        id_ = state.id_

        # 获取原始数据
        article_gen = ArticleGen.objects(id=id_).first()
        article_type = article_gen.article_type
        article_paragraphs = article_gen.article_content
        article_example = ArticleExample.objects(article_type=article_type).first()
        article_paragraphs_example = article_example.article_content

        # 数据传递进State
        return {
            "article_paragraphs_example": article_paragraphs_example,
            "article_paragraphs": article_paragraphs
        }

    # 执行AI生成功能,生成的所有中间结果在State中传递
    def gen_article_paragraphs(self, state: ArticleState):
        # 获取state中所有的数据
        id_ = state.id_
        article_paragraphs = state.article_paragraphs

        # 异步记录日志，记录任务ID、任务类型和任务步骤
        async_gen_log(
            task_id=id_,
            task_type='article',
            task_step='gen_article_paragraphs',
        )

        # LLMCompletionBuilderExecutor用于多线程并行执行大模型请求
        executor = LLMCompletionBuilderExecutor()
        for article_paragraph in article_paragraphs:
            builder = OpenAiCompletionBuilder()
            builder = builder.set_prompt(read_file_content('resource/prompt/文章生成-单段生成.txt'))  # 设置生成提示词模板,这里统一从某个文件中读取
            builder = builder.add_prompt_input(article_paragraph=article_paragraph)  # 添加模板输入的参数
            builder = builder.add_image_input('path/to/image')  # 添加需要识别的图片
            builder = builder.set_output_format(LLMOutputFormat.TEXT)  # 设置输出格式为文本,LLMOutputFormat.TEXT或者LLMOutputFormat.JSON
            executor.add_builder(builder)  # 将生成器添加到执行器中
        responses = executor.multiple_chat()  # 并行执行生成任务并获取响应

        # LLMCompletionBuilder单独使用,用于单线程执行一次大模型请求,如果只有一个任务,请选择他
        builder = OpenAiCompletionBuilder()
        builder = builder.set_prompt(read_file_content('resource/prompt/文章生成-单段生成.txt'))  # 设置生成提示词模板,这里统一从某个文件中读取
        builder = builder.add_prompt_input(article_paragraph=article_paragraph)  # 添加模板输入的参数
        builder = builder.add_image_input('path/to/image')  # 添加需要识别的图片
        builder = builder.set_output_format(LLMOutputFormat.TEXT)  # 设置输出格式为文本,LLMOutputFormat.TEXT或者LLMOutputFormat.JSON
        response = builder.simple_chat()  # 执行生成任务并获取响应
        # 数据传递进State
        return {"article_gen_paragraphs": responses}

    # AI生成完毕后,要做一些数据处理,如数据保存等
    def after(self, state: ArticleState):
        # 获取state中所有的数据
        id_ = state.id_
        article_gen_paragraphs = state.article_gen_paragraphs

        # 保存数据
        article_gen = ArticleGen.objects(id=id_).first()
        article_gen.article_gen_paragraphs = article_gen_paragraphs
        article_gen.save()  # 保存文章生成对象

    # 定义kafka消息接收处理函数
    def on_rcv_msg(self, body):
        id_ = body['id_']  # 获取消息内容
        try:
            work_flow_builder = StateGraph(ArticleState)  # 创建状态图构建器

            work_flow_builder.add_node("before", self.before)  # 添加读取初始数据节点
            work_flow_builder.add_node("gen_article_paragraphs", self.gen_article_paragraphs)  # 添加AI处理器节点
            work_flow_builder.add_node("after", self.after)  # 添加数据处理节点

            work_flow_builder.set_entry_point("before")  # 设置入口节点为读取初始数据
            work_flow_builder.add_edge("before", 'gen_article_paragraphs')  # 添加节点之间的边
            work_flow_builder.add_edge("gen_article_paragraphs", 'after')  # 添加节点之间的边
            work_flow_builder.add_edge("after", END)  # 添加节点之间的边，并设置结束节点

            work_flow = work_flow_builder.compile()  # 编译状态图
            work_flow.invoke({  # 调用状态图，传入初始数据
                "id_": id_,
            })
            add_gen_log(id_, 'article', 'AI大模型【轻舟】生成文章完毕！')
            return MessageReceiverStatus.SUCCESS
        except:
            syslog.error(traceback.format_exc())
            add_gen_log(id_, 'article', 'AI大模型【轻舟】生成文章失败！请重新生成!')
            return MessageReceiverStatus.SUCCESS
```
