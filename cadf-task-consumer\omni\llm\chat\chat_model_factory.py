from typing import Optional, Dict, Any, ClassVar

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel

from omni.config.config_loader import config_dict
from omni.log.log import olog


class ChatModelFactory:
    """根据配置文件初始化LangChain聊天模型，单例模式"""

    _instance: ClassVar[Optional['ChatModelFactory']] = None

    def __new__(cls):
        """确保只创建一个实例"""
        if cls._instance is None:
            cls._instance = super(ChatModelFactory, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化ChatModelFactory并加载所有LLM配置"""
        if not getattr(self, "_initialized", False):
            self.default_llm_name: Optional[str] = None
            self.llm_configs: Dict[str, Dict[str, Any]] = {}
            self.llm_instances: Dict[str, BaseChatModel] = {}
            self._load_llm_configs()
            self._initialized = True

    def _load_llm_configs(self) -> None:
        """从配置文件加载所有LLM配置并初始化LLM实例"""
        llm_config = config_dict.get("llm", {})

        if not llm_config:
            raise ValueError("配置中不存在LLM配置")

        # 获取默认LLM名称
        self.default_llm_name = llm_config.get("DEFAULT-LLM-NAME")

        # 加载所有有效的LLM配置
        self.llm_configs = {k: v for k, v in llm_config.items()
                            if k != "DEFAULT-LLM-NAME" and isinstance(v, dict)}

        if not self.llm_configs:
            raise ValueError("配置中没有可用的LLM")

        # 如果没有设置默认LLM，使用第一个可用的LLM
        if not self.default_llm_name:
            self.default_llm_name = list(self.llm_configs.keys())[0]
            olog.info(f"未设置默认LLM名称，使用: {self.default_llm_name}")
        elif self.default_llm_name not in self.llm_configs:
            available_llms = list(self.llm_configs.keys())
            self.default_llm_name = available_llms[0]
            olog.info(f"默认LLM [{self.default_llm_name}] 不存在，使用: {self.default_llm_name}")

        # 初始化所有LLM实例
        for llm_name, config in self.llm_configs.items():
            try:
                # 获取必要参数
                api_base = config.get("api_base")
                model_name = config.get("model_name")
                api_key = config.get("api_key")

                # 检查必要参数
                required = {"api_base": api_base, "model_name": model_name, "api_key": api_key}
                missing = [k for k, v in required.items() if not v]

                if missing:
                    olog.warning(f"LLM '{llm_name}' 配置不完整，缺少: {', '.join(missing)}，跳过初始化")
                    continue

                # 创建LLM实例
                self.llm_instances[llm_name] = init_chat_model(
                    model=model_name,
                    model_provider="openai",
                    base_url=api_base,
                    api_key=api_key
                )
                olog.info(f"成功初始化LLM: {llm_name}")
            except Exception as e:
                olog.error(f"初始化LLM '{llm_name}' 失败: {e}")

    def _get_llm_instance(self, llm_name: Optional[str] = None) -> BaseChatModel:
        """获取LangChain LLM实例（实例方法）
        
        Args:
            llm_name: 要使用的LLM名称，如果为None则使用默认LLM
            
        Returns:
            初始化好的LLM实例
        """
        # 如果未指定llm_name，使用默认LLM
        if llm_name is None:
            llm_name = self.default_llm_name

        # 检查LLM是否存在
        if llm_name not in self.llm_instances:
            available_llms = list(self.llm_instances.keys())
            llms_str = ", ".join(available_llms) if available_llms else "无"
            raise ValueError(f"未找到已初始化的LLM {llm_name}，可用LLM: {llms_str}")

        return self.llm_instances[llm_name]

    @classmethod
    def get_llm(cls, llm_name: Optional[str] = None) -> BaseChatModel:
        """获取LangChain LLM实例（类方法）
        
        Args:
            llm_name: 要使用的LLM名称，如果为None则使用默认LLM
            
        Returns:
            初始化好的LLM实例
        """
        # 获取单例实例
        instance = cls()
        # 调用实例方法
        return instance._get_llm_instance(llm_name)

ChatModelFactory.get_llm()