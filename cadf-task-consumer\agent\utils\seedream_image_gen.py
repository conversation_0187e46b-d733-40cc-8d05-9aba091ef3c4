import re
import time

import requests
from openai import OpenAI

from omni.config.config_loader import config_dict
from omni.log.log import olog


class SeedreamImageGenerator:
    def __init__(self, llm_config_key: str):
        llm_config = config_dict.get("llm", {}).get(llm_config_key)
        self.api_key = llm_config.get("api_key")
        self.api_base = llm_config.get("api_base")
        self.model_name = llm_config.get("model_name")
        self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)

    def gen_image(self, prompt: str, size: str = "1024x1792") -> bytes | None:
        for i in range(10):
            try:
                response = self.client.images.generate(
                    model=self.model_name, prompt=prompt, n=1, size=size
                )
                image_url = response.data[0].url
                image_response = requests.get(image_url)
                image_response.raise_for_status()  # 检查 HTTP 错误
                return image_response.content
            except Exception as e:
                # 检查是否为风险图片错误
                if 'Post Img Risk Not Pass' in str(e):
                    olog.error(f"Seedream生成图片失败，触发风控，错误信息: {e}")
                    return None
                olog.warning(f"第{i+1}次生成或下载图片失败，1秒后重试。错误信息: {e}")
                time.sleep(1)
        olog.error("生成或下载图片重试10次均失败")
        return None
