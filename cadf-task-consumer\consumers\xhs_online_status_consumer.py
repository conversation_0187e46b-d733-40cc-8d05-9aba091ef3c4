import time

from config.config import XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from repository.models import Account
from scraper.xhs_online_status_scraper import check_xhs_login_status


@consume_redis_set(redis_key=XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET, num_threads=1)
def xhs_online_status_checker(account_id):
    """
    检查指定小红书账号的在线状态并更新数据库。

    Args:
        account_id: 要检查的账号ID。
    """
    try:
        olog.info(f"开始检查账号 {account_id} 的小红书在线状态...")
        account = Account.objects(id=account_id).first()

        if not account:
            olog.warning(f"未找到 ID 为 {account_id} 的账号。")
            return

        if account.platform != "小红书":
            olog.warning(f"账号 {account_id} 的平台为 {account.platform}，非小红书，跳过检查。")
            return

        cookies_list = account.cookie
        if not cookies_list:
            olog.warning(f"账号 {account_id} 没有找到 Cookie 信息，无法检查登录状态。")
            # 即使没有 Cookie，也更新检查时间，并将状态视为离线
            account.status = "离线"
            account.last_login_check_at = int(time.time())
            account.save()
            return

        # 调用 scraper 进行登录状态检查
        is_logged_in = check_xhs_login_status(mongo_cookies=cookies_list)

        # 更新账号状态和最后检查时间
        new_status = "在线" if is_logged_in else "离线"
        olog.info(f"账号 {account_id} 检查完成，状态为: {new_status}")
        account.status = new_status
        account.last_login_check_at = int(time.time())
        account.save()
        olog.info(f"已更新账号 {account_id} 的状态和最后检查时间。")

    except Exception as e:
        olog.error(f"检查账号 {account_id} 在线状态时发生错误: {e}", exc_info=True)
