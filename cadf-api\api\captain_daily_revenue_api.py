from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import docs_to_dict
from repository.models import CaptainDailyRevenue
from omni.api.exception import MException


@register_handler("captain_daily_revenue")
class CaptainDailyRevenueApi:
    @auth_required(["captain", "admin"])
    def query_all(self, data):
        captain_user_id = data.get("user_id")
        if not captain_user_id:
            raise MException("无法获取舰长ID")

        try:
            revenue_records = CaptainDailyRevenue.objects(
                captain_user_id=captain_user_id
            ).order_by("-date")
            return docs_to_dict(revenue_records)
        except Exception as e:
            # 可以加入日志记录
            # olog.error(f"查询舰长收益失败: {e}")
            raise MException(f"查询舰长收益时出错: {e}")
