import time

from omni.llm.chat.chat_model_factory import ChatModelFactory
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import start_all_consumers

if __name__ == '__main__':
    olog.info("调用 start_all_consumers (将启动此模块内的演示消费者)...")
    start_all_consumers()

    olog.info("消费者已启动，主线程将保持运行...")
    try:
        while True:
            time.sleep(10)
    except KeyboardInterrupt:
        olog.info("主线程收到退出信号，消费者结束。")
