import {createTheme} from '@mui/material/styles';

export default createTheme({
    palette: {
        // 使用柔和的蓝灰色作为主色调，体现专业性和科技感
        primary: {
            main: '#3f51b5',
            light: '#757de8',
            dark: '#002984',
            contrastText: '#ffffff',
        },
        // 使用温暖的橙色作为辅助色，增加亲和力
        secondary: {
            main: '#ff9800',
            light: '#ffc947',
            dark: '#c66900',
            contrastText: '#000000',
        },
        // 中性色调
        grey: {
            50: '#fafafa',
            100: '#f5f5f5',
            200: '#eeeeee',
            300: '#e0e0e0',
            400: '#bdbdbd',
            500: '#9e9e9e',
            600: '#757575',
            700: '#616161',
            800: '#424242',
            900: '#212121',
        },
        // 背景色使用柔和的灰白色，减少视觉疲劳
        background: {
            default: '#f8f9fa',
            paper: '#ffffff',
        },
        // 文本颜色
        text: {
            primary: 'rgba(0, 0, 0, 0.87)',
            secondary: 'rgba(0, 0, 0, 0.6)',
            disabled: 'rgba(0, 0, 0, 0.38)',
        },
        // 错误、警告、信息和成功的颜色
        error: {
            main: '#f44336',
            light: '#e57373',
            dark: '#d32f2f',
        },
        warning: {
            main: '#ff9800',
            light: '#ffb74d',
            dark: '#f57c00',
        },
        info: {
            main: '#2196f3',
            light: '#64b5f6',
            dark: '#1976d2',
        },
        success: {
            main: '#4caf50',
            light: '#81c784',
            dark: '#388e3c',
        },
    },
    // 排版设置
    typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: 14,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700,
        h1: {
            fontSize: '2.5rem',
            fontWeight: 300,
            lineHeight: 1.2,
            letterSpacing: '-0.01562em',
        },
        h2: {
            fontSize: '2rem',
            fontWeight: 300,
            lineHeight: 1.2,
            letterSpacing: '-0.00833em',
        },
        h3: {
            fontSize: '1.75rem',
            fontWeight: 400,
            lineHeight: 1.2,
            letterSpacing: '0em',
        },
        h4: {
            fontSize: '1.5rem',
            fontWeight: 400,
            lineHeight: 1.2,
            letterSpacing: '0.00735em',
        },
        h5: {
            fontSize: '1.25rem',
            fontWeight: 400,
            lineHeight: 1.2,
            letterSpacing: '0em',
        },
        h6: {
            fontSize: '1rem',
            fontWeight: 500,
            lineHeight: 1.2,
            letterSpacing: '0.0075em',
        },
        subtitle1: {
            fontSize: '1rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0.00938em',
        },
        subtitle2: {
            fontSize: '0.875rem',
            fontWeight: 500,
            lineHeight: 1.57,
            letterSpacing: '0.00714em',
        },
        body1: {
            fontSize: '1rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0.00938em',
        },
        body2: {
            fontSize: '0.875rem',
            fontWeight: 400,
            lineHeight: 1.43,
            letterSpacing: '0.01071em',
        },
        button: {
            fontSize: '0.875rem',
            fontWeight: 500,
            lineHeight: 1.75,
            letterSpacing: '0.02857em',
            textTransform: 'none', // 移除按钮文字全大写的默认设置
        },
        caption: {
            fontSize: '0.75rem',
            fontWeight: 400,
            lineHeight: 1.66,
            letterSpacing: '0.03333em',
        },
        overline: {
            fontSize: '0.75rem',
            fontWeight: 400,
            lineHeight: 2.66,
            letterSpacing: '0.08333em',
        },
    },
    // 形状设置
    shape: {
        borderRadius: 8, // 适中的圆角，既现代又不过分
    },
    // 间距设置
    spacing: 8, // 基础间距单位为8px
    // 组件覆盖
    components: {
        // 按钮组件
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 8,
                    padding: '8px 16px',
                    boxShadow: 'none',
                    '&:hover': {
                        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                    },
                },
                contained: {
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
                },
                outlined: {
                    borderWidth: 1.5,
                },
            },
        },
        // 卡片组件
        MuiCard: {
            styleOverrides: {
                root: {
                    boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)',
                    borderRadius: 12,
                },
            },
        },
        // 纸张组件
        MuiPaper: {
            styleOverrides: {
                root: {
                    backgroundImage: 'none', // 移除默认的背景图案
                },
                elevation1: {
                    boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.08)',
                },
                elevation2: {
                    boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.08)',
                },
                elevation3: {
                    boxShadow: '0px 3px 8px rgba(0, 0, 0, 0.08)',
                },
                elevation4: {
                    boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.08)',
                },
            },
        },
        // 输入框组件
        MuiTextField: {
            styleOverrides: {
                root: {
                    '& .MuiOutlinedInput-root': {
                        borderRadius: 8,
                        '& fieldset': {
                            borderWidth: 1.5,
                        },
                        '&:hover fieldset': {
                            borderWidth: 1.5,
                        },
                        '&.Mui-focused fieldset': {
                            borderWidth: 1.5,
                        },
                    },
                },
            },
        },
        // 表格组件
        MuiTableCell: {
            styleOverrides: {
                root: {
                    padding: '16px 24px',
                    borderBottom: '1px solid rgba(224, 224, 224, 1)',
                },
                head: {
                    fontWeight: 600,
                    backgroundColor: 'rgba(245, 245, 245, 0.5)',
                },
            },
        },
        // 分割线组件
        MuiDivider: {
            styleOverrides: {
                root: {
                    margin: '16px 0',
                },
            },
        },
        // 图标按钮组件
        MuiIconButton: {
            styleOverrides: {
                root: {
                    padding: 8,
                },
            },
        },
        // 切换开关组件
        MuiSwitch: {
            styleOverrides: {
                root: {
                    width: 42,
                    height: 26,
                    padding: 0,
                },
                switchBase: {
                    padding: 1,
                    '&.Mui-checked': {
                        transform: 'translateX(16px)',
                        '& + .MuiSwitch-track': {
                            opacity: 1,
                        },
                    },
                },
                thumb: {
                    width: 24,
                    height: 24,
                },
                track: {
                    borderRadius: 13,
                    opacity: 1,
                },
            },
        },
    },
    // 过渡效果
    transitions: {
        easing: {
            easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
            easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
            easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
            sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
        },
        duration: {
            shortest: 150,
            shorter: 200,
            short: 250,
            standard: 300,
            complex: 375,
            enteringScreen: 225,
            leavingScreen: 195,
        },
    },
    // 断点设置
    breakpoints: {
        values: {
            xs: 0,
            sm: 600,
            md: 960,
            lg: 1280,
            xl: 1920,
        },
    },
});