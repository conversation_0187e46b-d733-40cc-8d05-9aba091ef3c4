import api from "@/core/api/api";

export const aiGenerationTaskApi = {
    create: async (product_id, material_ids, target_quantity) => {
        return await api({
            resource: "ai_generation_task",
            method_name: "create",
            product_id,
            material_ids,
            target_quantity,
        });
    },

    delete: async (id_) => {
        return await api({
            resource: "ai_generation_task",
            method_name: "delete",
            id_,
        });
    },

    query_one: async (id_) => {
        return await api({
            resource: "ai_generation_task",
            method_name: "query_one",
            id_,
        });
    },

    query_all: async (status, task_name, page, page_size) => {
        return await api({
            resource: "ai_generation_task",
            method_name: "query_all",
            status,
            task_name,
            page,
            page_size,
        });
    },
}; 