import time

from mongoengine.queryset.visitor import Q

from config.config import XHS_NOTE_CRAWL_SET
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.mongo.mongo_client import save_or_update
from omni.msg_queue.redis_set_publisher import publish_to_redis_set
from repository.models import AiBasicMaterial

# 初始化 OSSClient
oss_client = OSSClient.getInstance()


# 辅助函数：处理单个 Material 文档，添加 signed_url
def _process_material_doc(material_doc):
    material_dict = material_doc.to_mongo().to_dict()
    material_dict['id_'] = str(material_dict['_id'])
    del material_dict['_id']

    if 'images' in material_dict and isinstance(material_dict['images'], list):
        for img in material_dict['images']:
            if isinstance(img, dict) and 'oss_key' in img and img['oss_key']:
                try:
                    img['signed_url'] = oss_client.signed_url(SignedMethod.GET, img['oss_key'])
                except Exception as e:
                    # 处理签名URL生成失败的情况，例如记录日志
                    print(f"Error generating signed URL for {img['oss_key']}: {e}")
                    img['signed_url'] = None  # 或者设置一个默认的错误图片URL
            else:
                img['signed_url'] = None  # 确保字段存在
    return material_dict


# 辅助函数：处理 Material 文档列表
def _process_material_docs(material_docs):
    return [_process_material_doc(doc) for doc in material_docs]


@register_handler('ai_basic_material')
class AiBasicMaterialApi:
    @auth_required(['admin', 'advertiser'])
    def create(self, data):
        share_url = data.get('share_url')
        user_id = data.get('user_id')
        if not user_id:
            raise MException("无法获取用户信息，请重新登录")

        if not share_url:
            raise MException("缺少必要的参数 'share_url'")

        platform = None
        if 'xiaohongshu.com' in share_url:
            platform = '小红书'
        elif 'xhslink.com' in share_url:
            platform = '小红书'
        elif 'douyin.com' in share_url:
            platform = '抖音'
        elif 'weibo.com' in share_url or 'weibo.cn' in share_url:
            platform = '微博'
        elif 'bilibili.com' in share_url or 'b23.tv' in share_url:
            platform = 'Bilibili'
        elif 'kuaishou.com' in share_url or 'kwai.com' in share_url:
            platform = '快手'
        elif 'zhihu.com' in share_url:
            platform = '知乎'
        else:
            raise MException("无法从 share_url 推断平台类型")

        save_data = {
            'user_id': user_id,
            'platform': platform,
            'share_url': share_url,
            'create_at': int(time.time()),
            'is_deleted': False,
            'fetch_status': '待爬取'
        }

        id_ = save_or_update(AiBasicMaterial, save_data)
        id_str = str(id_)

        publish_to_redis_set(XHS_NOTE_CRAWL_SET, id_str)

        return {'id_': id_str}

    @auth_required(['admin', 'advertiser'])
    def delete(self, data):
        id_ = data.get('id_')
        if not id_:
            raise MException("删除操作需要提供 id_")

        material = AiBasicMaterial.objects(id=id_).first()
        if not material:
            return {}

        material.is_deleted = True
        material.save()
        return {}

    @auth_required(['admin', 'advertiser'])
    def get_by_id(self, data):
        id_ = data.get('id_')
        user_id = data.get('user_id')

        if not id_:
            raise MException("缺少必要的参数 'id_'")
        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        material = AiBasicMaterial.objects(id=id_, user_id=user_id, is_deleted=False).first()

        if not material:
            raise MException(f"ID为 {id_} 的素材不存在或已被删除")

        # 使用辅助函数处理单个文档
        return _process_material_doc(material)

    @auth_required(['admin', 'advertiser'])
    def query_all(self, data):
        search_query = data.get('search')
        page = int(data.get('page', 0))
        page_size = int(data.get('page_size', 5))
        user_id = data.get('user_id')
        platform = data.get('platform')

        if not user_id:
            raise MException("缺少必要的参数 'user_id'")

        query_filter = Q(user_id=user_id) & Q(is_deleted=False)

        if search_query:
            query_filter &= Q(title__icontains=search_query)

        if platform:
            query_filter &= Q(platform=platform)

        offset = page * page_size
        query_set = AiBasicMaterial.objects(query_filter)
        total_count = query_set.count()
        materials = query_set.order_by('-create_at').skip(offset).limit(page_size)

        # 使用辅助函数处理文档列表
        processed_materials = _process_material_docs(materials)

        return {
            'data': processed_materials,
            'total': total_count,
            'page': page,
            'page_size': page_size
        }
