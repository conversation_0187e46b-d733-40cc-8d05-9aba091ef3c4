from PIL import Image, features

print(f"Pillow version: {Image.__version__}")

# 检查 AVIF 编解码器是否可用
print(f"AVIF codec available (features.check_codec): {features.check_codec('avif')}")

# 检查 AVIF 插件特性是否可用
print(f"AVIF plugin feature available (features.check_feature): {features.check_feature('avif_plugin')}")

# Pillow >= 9.2.0 会尝试使用 libheif (如果可用)
# 较旧版本的 Pillow 可能没有 'check' 方法，所以我们先检查它是否存在
if hasattr(features, "check"):
    print(f"AVIF via libheif (features.check): {features.check('avif')}")
else:
    print("features.check('avif') is not available in this Pillow version.")

print("\nRegistered AVIF plugin (if any):")
try:
    # pillow-avif-plugin 应该注册一个名为 'AvifImagePlugin' 的插件
    from PIL import AvifImagePlugin
    print("Found AvifImagePlugin (likely from pillow-avif-plugin)")
except ImportError:
    print("AvifImagePlugin not found directly.")

print("\nAttempting to list all plugins to see if AVIF is mentioned:")
Image.init() #确保所有插件都被加载
# 打印所有已注册的打开图像的插件及其关联的文件扩展名
for ext, plugin_name in Image.OPEN.items():
    if "AVIF" in ext.upper() or "AVIF" in plugin_name.__name__.upper():
        print(f"Found AVIF related plugin: {ext} -> {plugin_name.__name__}")

# 打印支持的格式列表
# print(Image.SAVE.keys()) # 查看支持保存的格式
# print(Image.MIME.values()) # 查看支持的MIME类型