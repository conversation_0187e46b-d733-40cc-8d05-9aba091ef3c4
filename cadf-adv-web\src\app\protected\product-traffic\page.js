"use client";

import React, {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {Alert as Mui<PERSON>lert, Box, Card, CardContent, CardHeader, Chip, CircularProgress, Divider, Grid, Pagination, Stack, Typography, Fab, Button} from '@mui/material';
import {Bookmark, Eye, Share2, ThumbsUp, MessageCircle, Plus, RefreshCw} from 'lucide-react';
import ProductCard from '../../../components/ProductCard';
import {advProductTrafficApi} from '@/api/adv-product-traffic-api';
import {addAlert} from '@/core/components/redux/alert-slice';
import {AlertType} from '@/core/components/alert';
import Link from 'next/link';


const StatCard = ({icon, title, value, color = 'primary'}) => {
    return (
        <Card sx={{height: '100%', bgcolor: `${color}.50`}}>
            <CardContent sx={{p: 2.5, pb: '16px !important'}}>
                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                    {icon && (
                        <Box
                            sx={{
                                color: `${color}.main`,
                                mr: 1,
                                display: 'flex',
                                alignItems: 'center'
                            }}
                        >
                            {icon}
                        </Box>
                    )}
                    <Typography variant="subtitle1" color="text.secondary">
                        {title}
                    </Typography>
                </Box>
                <Typography variant="h4" sx={{fontWeight: 'medium'}}>
                    {value !== null && value !== undefined ? value.toLocaleString() : 'N/A'}
                </Typography>
            </CardContent>
        </Card>
    );
};


const renderProductCard = (productData) => {

    const platforms = [
        {id: 1, name: '小红书', color: '#FF2442'},
        {id: 2, name: '抖音', color: '#000000'},
        {id: 3, name: '微博', color: '#E6162D'},
        {id: 4, name: '哔哩哔哩', color: '#00A1D6'},
        {id: 5, name: '快手', color: '#FF4A00'},
        {id: 6, name: '知乎', color: '#0084FF'},
    ];

    const getPlatformChip = (platformName) => {
        const platformInfo = platforms.find(p => p.name === platformName);
        return platformInfo ? (
            <Chip
                key={platformName}
                label={platformName}
                size="small"
                sx={{
                    bgcolor: platformInfo.color,
                    color: 'white',
                    height: 20,
                    fontSize: '0.7rem',
                    '& .MuiChip-label': {px: 0.75}
                }}
            />
        ) : null;
    };


    const productImages = productData.images && productData.images.length > 0
        ? productData.images

            .map(img => img.signed_url || 'https://placehold.co/300x400/cccccc/969696?text=Image+Error')
            .filter(url => url)
        : ['https://placehold.co/300x400/cccccc/969696?text=No+Image'];


    if (productImages.length === 0) {
        productImages.push('https://placehold.co/300x400/cccccc/969696?text=No+Image');
    }

    return (
        <ProductCard
            images={productImages}
        >
            <CardHeader
                title={productData.title || `产品 ${productData.product_id.slice(-6)}`}
                titleTypographyProps={{
                    variant: 'h6',
                    sx: {
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '100%'
                    }
                }}
            />
            <Divider/>
            <CardContent sx={{flexGrow: 1}}>
                <Stack direction="row" spacing={1} sx={{mb: 2}}>
                    <Typography variant="body2" color="text.secondary">数据来源:</Typography>
                    <Box sx={{display: 'flex', gap: 0.5, flexWrap: 'wrap'}}>

                        {productData.platforms && productData.platforms.length > 0
                            ? productData.platforms.map(getPlatformChip)
                            : <Chip label="暂无平台" size="small" sx={{height: 20, fontSize: '0.7rem'}}/>
                        }
                    </Box>
                </Stack>


                <Grid container spacing={2}>
                    <Grid item xs={12} sm={12}>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <Eye size={16} color="#1976d2"/>
                            <Typography
                                variant="body2">阅读量: {productData.total_view_count?.toLocaleString() ?? 0}</Typography>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <ThumbsUp size={16} color="#2e7d32"/>
                            <Typography
                                variant="body2">点赞量: {productData.total_like_count?.toLocaleString() ?? 0}</Typography>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <MessageCircle size={16} color="#9c27b0"/>
                            <Typography
                                variant="body2">评论量: {productData.total_comment_count?.toLocaleString() ?? 0}</Typography>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <Share2 size={16} color="#0288d1"/>
                            <Typography
                                variant="body2">转发量: {productData.total_share_count?.toLocaleString() ?? 0}</Typography>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <Bookmark size={16} color="#ed6c02"/>
                            <Typography
                                variant="body2">收藏量: {productData.total_favorite_count?.toLocaleString() ?? 0}</Typography>
                        </Stack>
                    </Grid>
                </Grid>
            </CardContent>
        </ProductCard>
    );
};

export default function DataTrafficCenter() {
    const dispatch = useDispatch();
    const [productTraffics, setProductTraffics] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [totalProducts, setTotalProducts] = useState(0);
    const [totalStats, setTotalStats] = useState(null);
    const [isLoadingStats, setIsLoadingStats] = useState(true);
    const [statsError, setStatsError] = useState(null);


    const [page, setPage] = useState(1);
    const productsPerPage = 5;


    const fetchListData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await advProductTrafficApi.queryAll({page, limit: productsPerPage});

            if (response && Array.isArray(response.items) && typeof response.total === 'number') {
                setProductTraffics(response.items);
                setTotalProducts(response.total);
            } else {

                setProductTraffics([]);
                setTotalProducts(0);
                console.warn("API did not return expected structure:", response);
                dispatch(addAlert({type: AlertType.WARNING, message: '获取产品数据格式错误'}));
            }
        } catch (err) {
            console.error("Error fetching product traffic:", err);
            setError("加载产品流量数据失败，请稍后重试。");
            setProductTraffics([]);
            setTotalProducts(0);
            dispatch(addAlert({type: AlertType.ERROR, message: '加载数据失败'}));
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchListData();
    }, [dispatch, page]);


    useEffect(() => {
        const fetchTotalStats = async () => {
            setIsLoadingStats(true);
            setStatsError(null);
            try {
                const statsResponse = await advProductTrafficApi.getTotalStats();
                if (statsResponse) {
                    setTotalStats(statsResponse);
                } else {
                    console.warn("API for total stats did not return expected structure:", statsResponse);
                    dispatch(addAlert({type: AlertType.WARNING, message: '获取总览数据格式错误'}));
                    setTotalStats({
                      total_view_count: 0,
                      total_like_count: 0,
                      total_comment_count: 0,
                      total_favorite_count: 0,
                      total_share_count: 0
                    });
                }
            } catch (err) {
                console.error("Error fetching total stats:", err);
                setStatsError("加载总览数据失败，请稍后重试。");
                dispatch(addAlert({type: AlertType.ERROR, message: '加载总览数据失败'}));
                 setTotalStats({
                      total_view_count: 0,
                      total_like_count: 0,
                      total_comment_count: 0,
                      total_favorite_count: 0,
                      total_share_count: 0
                 });
            } finally {
                setIsLoadingStats(false);
            }
        };

        fetchTotalStats();
    }, [dispatch]);


    const totalPages = Math.ceil(totalProducts / productsPerPage);


    const handlePageChange = (event, value) => {
        setPage(value);

    };


    const renderTotalStats = () => {

        const isLoading = isLoadingStats;
        const error = statsError;
        const stats = totalStats;

        if (isLoading || error || !stats) {
            const defaultValue = isLoading ? '加载中...' : (error ? '错误' : 'N/A');
            const loadingColor = isLoading ? 'grey' : (error ? 'error' : 'primary');

            const displayValue = (isLoading || error) ? defaultValue : 'N/A';

            return (
                <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={2.4}>
                        <StatCard icon={<Eye size={20}/>} title="总阅读量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                        <StatCard icon={<ThumbsUp size={20}/>} title="总点赞量" value={displayValue} color={loadingColor}/>
                    </Grid>
                     <Grid item xs={12} sm={6} md={2.4}>
                        <StatCard icon={<MessageCircle size={20}/>} title="总评论量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                        <StatCard icon={<Share2 size={20}/>} title="总转发量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                        <StatCard icon={<Bookmark size={20}/>} title="总收藏量" value={displayValue} color={loadingColor}/>
                    </Grid>
                </Grid>
            );
        }

        const totalViews = stats.total_view_count ?? 0;
        const totalLikes = stats.total_like_count ?? 0;
        const totalComments = stats.total_comment_count ?? 0;
        const totalShares = stats.total_share_count ?? 0;
        const totalFavorites = stats.total_favorite_count ?? 0;

        return (
            <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={2.4}>
                    <StatCard
                        icon={<Eye size={20}/>}
                        title="总阅读量"
                        value={totalViews}
                        color="primary"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                    <StatCard
                        icon={<ThumbsUp size={20}/>}
                        title="总点赞量"
                        value={totalLikes}
                        color="success"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                     <StatCard
                        icon={<MessageCircle size={20}/>}
                        title="总评论量"
                        value={totalComments}
                        color="secondary"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                    <StatCard
                        icon={<Share2 size={20}/>}
                        title="总转发量"
                        value={totalShares}
                        color="info"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                    <StatCard
                        icon={<Bookmark size={20}/>}
                        title="总收藏量"
                        value={totalFavorites}
                        color="warning"
                    />
                </Grid>
            </Grid>
        );
    };

    return (
        <Box sx={{
            width: '100%',
            px: {xs: 2, sm: 3}
        }}>
            <Box sx={{py: 4}}>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4}}>
                    <Typography variant="h4" sx={{fontWeight: 'medium'}}>
                        产品数据总览
                    </Typography>
                </Box>

                {renderTotalStats()}

                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, mt: 4}}>
                    <Typography variant="h5">
                        产品流量详情 (共 {totalProducts} 个)
                    </Typography>
                    <Button 
                        variant="outlined" 
                        startIcon={<RefreshCw size={16} />} 
                        onClick={fetchListData}
                        disabled={isLoading}
                    >
                        刷新
                    </Button>
                </Box>

                {isLoading && (
                    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200}}>
                        <CircularProgress/>
                    </Box>
                )}

                {error && (
                    <MuiAlert severity="error" sx={{mb: 2}}>{error}</MuiAlert>
                )}

                {!isLoading && !error && totalProducts === 0 && (
                    <Box sx={{textAlign: 'center', mt: 6, mb: 4}}>
                        <Typography sx={{color: 'text.secondary', mb: 3}}>
                            暂无产品数据
                        </Typography>
                    </Box>
                )}

                {!isLoading && !error && totalProducts > 0 && (
                    <>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} md={2.4}>
                                <Link href="/protected/product-management/new" passHref style={{textDecoration: 'none'}}>
                                    <Card
                                        sx={{
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            width: '100%%',
                                            mx: 'auto',
                                            borderStyle: 'dashed',
                                            borderWidth: 2,
                                            borderColor: 'primary.main',
                                            backgroundColor: 'primary.50',
                                            transition: 'all 0.3s',
                                            '&:hover': {
                                                transform: 'translateY(-8px)',
                                                boxShadow: 3,
                                                borderColor: 'primary.dark'
                                            },
                                            cursor: 'pointer'
                                        }}
                                    >
                                        <Box sx={{
                                            width: '100%',
                                            paddingTop: '133.33%', // Match 3:4 aspect ratio of ProductCard image area
                                            position: 'relative', // Needed for absolute positioning if any child elements were added
                                            overflow: 'hidden' // Hide any potential overflow
                                        }} />
                                        <Box
                                            sx={{
                                                flexGrow: 1, // Allow this box to grow
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                p: 2.5,
                                                pb: '16px !important'
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    width: 60,
                                                    height: 60,
                                                    borderRadius: '50%%',
                                                    bgcolor: 'primary.main',
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    mb: 2
                                                }}
                                            >
                                                <Plus size={30} color="#fff" />
                                            </Box>
                                            <Typography variant="h6" color="primary.main" sx={{fontWeight: 'medium'}}>
                                                新增产品
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary" sx={{mt: 1}}>
                                                添加产品开始监测流量数据
                                            </Typography>
                                        </Box>
                                    </Card>
                                </Link>
                            </Grid>
                            {productTraffics.map((traffic) => (
                                <Grid item xs={12} sm={6} md={2.4} key={traffic.product_id}>
                                    {renderProductCard(traffic)}
                                </Grid>
                            ))}
                        </Grid>

                        {totalPages > 1 && (
                            <Box sx={{display: 'flex', justifyContent: 'center', mt: 4}}>
                                <Pagination
                                    count={totalPages}
                                    page={page}
                                    onChange={handlePageChange}
                                    color="primary"
                                    size="large"
                                />
                            </Box>
                        )}
                    </>
                )}
            </Box>
        </Box>
    );
}