from langchain_core.output_parsers import StrOutputParser  # 导入Json输出解析器
from langchain_core.prompts import ChatPromptTemplate

from omni.llm.chat.chat_model_factory import ChatModelFactory  # 导入聊天模型工厂

PROMPT = """
给我讲一个关于{topic}的笑话
"""


def example_text_output():
    """使用LangChain的ChatPromptTemplate来生成关于特定主题的笑话。"""

    prompt = ChatPromptTemplate.from_template(PROMPT)
    llm = ChatModelFactory.get_llm()
    parser = StrOutputParser()

    chain = prompt | llm | parser
    chain = chain.with_retry()
    params = {"topic": "猫咪"}
    result = chain.invoke(params)
    print(result)
